//
//  ErrorHandlingService.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import Foundation
import SwiftUI

// MARK: - Error Categories

enum ErrorCategory: String, CaseIterable {
    case audio = "audio"
    case permissions = "permissions"
    case storage = "storage"
    case network = "network"
    case speech = "speech"
    case keyword = "keyword"
    case ui = "ui"
    case system = "system"
    case unknown = "unknown"
    
    var displayName: String {
        switch self {
        case .audio: return "Audio"
        case .permissions: return "Permissions"
        case .storage: return "Storage"
        case .network: return "Network"
        case .speech: return "Speech Recognition"
        case .keyword: return "Keyword Detection"
        case .ui: return "User Interface"
        case .system: return "System"
        case .unknown: return "Unknown"
        }
    }
    
    var icon: String {
        switch self {
        case .audio: return "waveform.circle"
        case .permissions: return "lock.circle"
        case .storage: return "externaldrive.circle"
        case .network: return "network"
        case .speech: return "text.bubble.circle"
        case .keyword: return "tag.circle"
        case .ui: return "app.circle"
        case .system: return "gear.circle"
        case .unknown: return "questionmark.circle"
        }
    }
}

// MARK: - Error Severity

enum ErrorSeverity: String, CaseIterable, Comparable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"
    case warning = "warning"
    case error = "error"
    
    static func < (lhs: ErrorSeverity, rhs: ErrorSeverity) -> Bool {
        let order: [ErrorSeverity] = [.low, .medium, .warning, .high, .error, .critical]
        guard let lhsIndex = order.firstIndex(of: lhs),
              let rhsIndex = order.firstIndex(of: rhs) else {
            return false
        }
        return lhsIndex < rhsIndex
    }
    
    var displayName: String {
        switch self {
        case .low: return "Low"
        case .medium: return "Medium"
        case .high: return "High"
        case .critical: return "Critical"
        case .warning: return "Warning"
        case .error: return "Error"
        }
    }
    
    var color: Color {
        switch self {
        case .low: return .blue
        case .medium: return .orange
        case .high: return .red
        case .critical: return .purple
        case .warning: return .yellow
        case .error: return .red
        }
    }

    var icon: String {
        switch self {
        case .low: return "info.circle"
        case .medium: return "exclamationmark.triangle"
        case .high: return "exclamationmark.circle"
        case .critical: return "xmark.circle"
        case .warning: return "exclamationmark.triangle.fill"
        case .error: return "xmark.circle.fill"
        }
    }
}

// MARK: - EchoNote Errors

protocol EchoNoteError: Error {
    var category: ErrorCategory { get }
    var severity: ErrorSeverity { get }
    var userMessage: String { get }
    var technicalMessage: String { get }
    var recoveryActions: [RecoveryAction] { get }
    var errorCode: String { get }
}

// MARK: - Recovery Actions

struct RecoveryAction {
    let title: String
    let description: String
    let action: () -> Void
    let isDestructive: Bool
    
    init(title: String, description: String, isDestructive: Bool = false, action: @escaping () -> Void) {
        self.title = title
        self.description = description
        self.isDestructive = isDestructive
        self.action = action
    }
}

// MARK: - Specific Error Types

enum AudioError: EchoNoteError {
    case recordingFailed(underlying: Error?)
    case playbackFailed(underlying: Error?)
    case audioSessionSetupFailed(underlying: Error?)
    case fileNotFound(path: String)
    case unsupportedFormat(format: String)
    case deviceNotAvailable
    
    var category: ErrorCategory { .audio }
    
    var severity: ErrorSeverity {
        switch self {
        case .deviceNotAvailable: return .critical
        case .audioSessionSetupFailed: return .high
        case .recordingFailed, .playbackFailed: return .medium
        case .fileNotFound, .unsupportedFormat: return .low
        }
    }
    
    var userMessage: String {
        switch self {
        case .recordingFailed:
            return "Unable to record audio. Please check your microphone and try again."
        case .playbackFailed:
            return "Unable to play audio. The audio file may be corrupted."
        case .audioSessionSetupFailed:
            return "Audio system initialization failed. Please restart the app."
        case .fileNotFound:
            return "Audio file not found. It may have been moved or deleted."
        case .unsupportedFormat:
            return "Audio format not supported."
        case .deviceNotAvailable:
            return "Audio device not available. Please check your device settings."
        }
    }
    
    var technicalMessage: String {
        switch self {
        case .recordingFailed(let error):
            return "Recording failed: \(error?.localizedDescription ?? "Unknown error")"
        case .playbackFailed(let error):
            return "Playback failed: \(error?.localizedDescription ?? "Unknown error")"
        case .audioSessionSetupFailed(let error):
            return "Audio session setup failed: \(error?.localizedDescription ?? "Unknown error")"
        case .fileNotFound(let path):
            return "Audio file not found at path: \(path)"
        case .unsupportedFormat(let format):
            return "Unsupported audio format: \(format)"
        case .deviceNotAvailable:
            return "Audio device not available"
        }
    }
    
    var recoveryActions: [RecoveryAction] {
        switch self {
        case .recordingFailed:
            return [
                RecoveryAction(title: "Try Again", description: "Attempt recording again") { /* Retry recording */ },
                RecoveryAction(title: "Check Permissions", description: "Verify microphone permissions") { /* Open settings */ }
            ]
        case .playbackFailed:
            return [
                RecoveryAction(title: "Try Again", description: "Attempt playback again") { /* Retry playback */ }
            ]
        case .audioSessionSetupFailed:
            return [
                RecoveryAction(title: "Restart App", description: "Close and reopen the app") { /* Restart app */ }
            ]
        case .fileNotFound:
            return [
                RecoveryAction(title: "Refresh", description: "Refresh the notes list") { /* Refresh */ }
            ]
        case .unsupportedFormat:
            return []
        case .deviceNotAvailable:
            return [
                RecoveryAction(title: "Check Settings", description: "Open device audio settings") { /* Open settings */ }
            ]
        }
    }
    
    var errorCode: String {
        switch self {
        case .recordingFailed: return "AUD001"
        case .playbackFailed: return "AUD002"
        case .audioSessionSetupFailed: return "AUD003"
        case .fileNotFound: return "AUD004"
        case .unsupportedFormat: return "AUD005"
        case .deviceNotAvailable: return "AUD006"
        }
    }
}

enum PermissionError: EchoNoteError {
    case microphoneDenied
    case speechRecognitionDenied
    case notificationsDenied
    case permissionRequestFailed(underlying: Error?)
    
    var category: ErrorCategory { .permissions }
    var severity: ErrorSeverity { .high }
    
    var userMessage: String {
        switch self {
        case .microphoneDenied:
            return "Microphone access is required to record voice notes. Please enable it in Settings."
        case .speechRecognitionDenied:
            return "Speech recognition access is required for transcription. Please enable it in Settings."
        case .notificationsDenied:
            return "Notification access helps you stay updated. You can enable it in Settings."
        case .permissionRequestFailed:
            return "Permission request failed. Please try again or check Settings."
        }
    }
    
    var technicalMessage: String {
        switch self {
        case .microphoneDenied:
            return "Microphone permission denied"
        case .speechRecognitionDenied:
            return "Speech recognition permission denied"
        case .notificationsDenied:
            return "Notifications permission denied"
        case .permissionRequestFailed(let error):
            return "Permission request failed: \(error?.localizedDescription ?? "Unknown error")"
        }
    }
    
    var recoveryActions: [RecoveryAction] {
        [
            RecoveryAction(title: "Open Settings", description: "Go to app settings to enable permissions") { /* Open settings */ },
            RecoveryAction(title: "Try Again", description: "Request permission again") { /* Retry permission */ }
        ]
    }
    
    var errorCode: String {
        switch self {
        case .microphoneDenied: return "PER001"
        case .speechRecognitionDenied: return "PER002"
        case .notificationsDenied: return "PER003"
        case .permissionRequestFailed: return "PER004"
        }
    }
}

enum StorageError: EchoNoteError {
    case saveFailed(underlying: Error?)
    case loadFailed(underlying: Error?)
    case deleteFailed(underlying: Error?)
    case insufficientSpace
    case corruptedData
    case encryptionFailed
    case decryptionFailed
    
    var category: ErrorCategory { .storage }
    
    var severity: ErrorSeverity {
        switch self {
        case .insufficientSpace: return .critical
        case .corruptedData, .encryptionFailed, .decryptionFailed: return .high
        case .saveFailed, .loadFailed, .deleteFailed: return .medium
        }
    }
    
    var userMessage: String {
        switch self {
        case .saveFailed:
            return "Unable to save data. Please check available storage space."
        case .loadFailed:
            return "Unable to load data. The file may be corrupted."
        case .deleteFailed:
            return "Unable to delete item. Please try again."
        case .insufficientSpace:
            return "Insufficient storage space. Please free up space and try again."
        case .corruptedData:
            return "Data appears to be corrupted and cannot be loaded."
        case .encryptionFailed:
            return "Unable to secure your data. Please try again."
        case .decryptionFailed:
            return "Unable to access your data. It may be corrupted."
        }
    }
    
    var technicalMessage: String {
        switch self {
        case .saveFailed(let error):
            return "Save operation failed: \(error?.localizedDescription ?? "Unknown error")"
        case .loadFailed(let error):
            return "Load operation failed: \(error?.localizedDescription ?? "Unknown error")"
        case .deleteFailed(let error):
            return "Delete operation failed: \(error?.localizedDescription ?? "Unknown error")"
        case .insufficientSpace:
            return "Insufficient storage space available"
        case .corruptedData:
            return "Data corruption detected"
        case .encryptionFailed:
            return "Data encryption failed"
        case .decryptionFailed:
            return "Data decryption failed"
        }
    }
    
    var recoveryActions: [RecoveryAction] {
        switch self {
        case .saveFailed, .insufficientSpace:
            return [
                RecoveryAction(title: "Free Up Space", description: "Delete unused files") { /* Open storage management */ },
                RecoveryAction(title: "Try Again", description: "Attempt save again") { /* Retry save */ }
            ]
        case .loadFailed, .corruptedData:
            return [
                RecoveryAction(title: "Try Again", description: "Attempt load again") { /* Retry load */ },
                RecoveryAction(title: "Reset Data", description: "Clear corrupted data", isDestructive: true) { /* Reset data */ }
            ]
        case .deleteFailed:
            return [
                RecoveryAction(title: "Try Again", description: "Attempt delete again") { /* Retry delete */ }
            ]
        case .encryptionFailed, .decryptionFailed:
            return [
                RecoveryAction(title: "Try Again", description: "Attempt operation again") { /* Retry */ },
                RecoveryAction(title: "Reset Security", description: "Reset encryption keys", isDestructive: true) { /* Reset security */ }
            ]
        }
    }
    
    var errorCode: String {
        switch self {
        case .saveFailed: return "STO001"
        case .loadFailed: return "STO002"
        case .deleteFailed: return "STO003"
        case .insufficientSpace: return "STO004"
        case .corruptedData: return "STO005"
        case .encryptionFailed: return "STO006"
        case .decryptionFailed: return "STO007"
        }
    }
}

// MARK: - Error Handling Service

@MainActor
class ErrorHandlingService: ObservableObject {

    // MARK: - Published Properties

    @Published var currentError: EchoNoteError?
    @Published var errorHistory: [ErrorRecord] = []
    @Published var showingErrorAlert = false

    // MARK: - Properties

    private let maxHistoryCount = 100
    private var errorCount: [String: Int] = [:]

    // MARK: - Error Record

    struct ErrorRecord: Identifiable {
        let id = UUID()
        let error: EchoNoteError
        let timestamp: Date
        let context: String?
        let wasHandled: Bool

        init(error: EchoNoteError, context: String? = nil, wasHandled: Bool = false) {
            self.error = error
            self.timestamp = Date()
            self.context = context
            self.wasHandled = wasHandled
        }
    }

    // MARK: - Singleton

    static let shared = ErrorHandlingService()

    private init() {}

    // MARK: - Error Handling

    /// Handle an error with optional context
    func handle(_ error: EchoNoteError, context: String? = nil, showToUser: Bool = true) {
        let record = ErrorRecord(error: error, context: context, wasHandled: showToUser)

        // Add to history
        addToHistory(record)

        // Track error frequency
        trackError(error)

        // Log the error
        logError(error, context: context)

        // Show to user if requested
        if showToUser {
            presentError(error)
        }

        // Handle critical errors
        if error.severity == .critical {
            handleCriticalError(error)
        }
    }

    /// Handle a generic error by converting it to EchoNoteError
    func handle(_ error: Error, category: ErrorCategory = .unknown, context: String? = nil, showToUser: Bool = true) {
        let echoError = GenericError(underlying: error, category: category)
        handle(echoError, context: context, showToUser: showToUser)
    }

    /// Present error to user
    private func presentError(_ error: EchoNoteError) {
        currentError = error
        showingErrorAlert = true
    }

    /// Dismiss current error
    func dismissError() {
        currentError = nil
        showingErrorAlert = false
    }

    /// Execute recovery action
    func executeRecoveryAction(_ action: RecoveryAction) {
        action.action()
        dismissError()
    }

    // MARK: - Error Tracking

    private func addToHistory(_ record: ErrorRecord) {
        errorHistory.insert(record, at: 0)

        // Limit history size
        if errorHistory.count > maxHistoryCount {
            errorHistory = Array(errorHistory.prefix(maxHistoryCount))
        }
    }

    private func trackError(_ error: EchoNoteError) {
        let key = error.errorCode
        errorCount[key, default: 0] += 1
    }

    /// Get error frequency for a specific error code
    func getErrorFrequency(for errorCode: String) -> Int {
        return errorCount[errorCode, default: 0]
    }

    /// Check if error is recurring (appears more than 3 times)
    func isRecurringError(_ error: EchoNoteError) -> Bool {
        return getErrorFrequency(for: error.errorCode) > 3
    }

    // MARK: - Critical Error Handling

    private func handleCriticalError(_ error: EchoNoteError) {
        // Log critical error
        print("CRITICAL ERROR: \(error.technicalMessage)")

        // Report to crash reporting service
        reportCriticalError(error)

        // Show critical error UI
        // This could trigger a different UI flow for critical errors
    }

    // MARK: - Logging

    private func logError(_ error: EchoNoteError, context: String?) {
        let logger = LoggingService.shared
        let logCategory = mapErrorCategoryToLogCategory(error.category)

        var metadata: [String: String] = [
            "errorCode": error.errorCode,
            "severity": error.severity.rawValue
        ]

        if let context = context {
            metadata["context"] = context
        }

        let message = error.technicalMessage

        switch error.severity {
        case .low:
            logger.info(message, category: logCategory, metadata: metadata)
        case .medium:
            logger.warning(message, category: logCategory, metadata: metadata)
        case .high:
            logger.error(message, category: logCategory, metadata: metadata)
        case .critical:
            logger.fault(message, category: logCategory, metadata: metadata)
        case .warning:
            logger.warning(message, category: logCategory, metadata: metadata)
        case .error:
            logger.error(message, category: logCategory, metadata: metadata)
        }
    }

    private func mapErrorCategoryToLogCategory(_ errorCategory: ErrorCategory) -> LogCategory {
        switch errorCategory {
        case .audio: return .audio
        case .permissions: return .permissions
        case .storage: return .storage
        case .network: return .network
        case .speech: return .speech
        case .keyword: return .keyword
        case .ui: return .ui
        case .system: return .system
        case .unknown: return .debug
        }
    }

    // MARK: - Clear History

    func clearErrorHistory() {
        errorHistory.removeAll()
        errorCount.removeAll()
    }
}

// MARK: - Generic Error Wrapper

struct GenericError: EchoNoteError {
    let underlying: Error
    let category: ErrorCategory

    var severity: ErrorSeverity { .medium }

    var userMessage: String {
        return "An unexpected error occurred. Please try again."
    }

    var technicalMessage: String {
        return "Generic error: \(underlying.localizedDescription)"
    }

    var recoveryActions: [RecoveryAction] {
        return [
            RecoveryAction(title: "Try Again", description: "Attempt the operation again") { /* Retry */ }
        ]
    }

    var errorCode: String {
        return "GEN001"
    }
}
