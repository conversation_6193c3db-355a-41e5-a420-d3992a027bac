//
//  UnifiedRecordingView.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import SwiftUI
import SwiftData
import AVFoundation

struct UnifiedRecordingView: View {
    @Binding var isPresented: Bool
    @Environment(\.modelContext) private var modelContext

    @State private var audioManager = AudioManager()
    @State private var recordingState: RecordingState = .listening
    @State private var showingSaveDialog = false
    @State private var noteTitle = ""
    @State private var selectedKeywords: [Keyword] = []
    @State private var autoSaveEnabled = true
    @State private var refreshTimer: Timer?
    
    // Animation states
    @State private var keywordDetectedAnimation = false
    @State private var pulseAnimation = false
    @State private var waveformAnimation = false
    @State private var breathingAnimation = false
    @State private var recordingPulse = false

    @Query(sort: \Keyword.text) private var keywords: [Keyword]
    @Query(sort: \UserPreferences.createdDate) private var userPreferences: [UserPreferences]
    @StateObject private var accessibilityService = AccessibilityService.shared
    @StateObject private var themeManager = ThemeManager.shared

    private var dataManager: DataManager {
        DataManager(modelContext: modelContext)
    }

    private var preferences: UserPreferences? {
        userPreferences.first
    }

    var body: some View {
        ZStack {
            themeManager.applyBackground().ignoresSafeArea()

            VStack(spacing: 20) {
                // Top bar
                topBar
                
                Spacer()
                
                // Main content
                VStack(spacing: 30) {
                    // Animated waveform with cute effects
                    animatedWaveformView
                    
                    // Status with feedback animations
                    statusSection
                    
                    // Timer (when recording)
                    if recordingState == .recording {
                        timerSection
                    }
                    
                    // Real-time transcription
                    transcriptionSection
                }
                
                Spacer()
                
                // Control buttons
                controlButtonsSection
                
                Spacer()
            }
            .padding(.horizontal, 25)
        }
        .onAppear {
            setupView()
        }
        .onDisappear {
            cleanup()
        }
        .onChange(of: audioManager.isListening) { _, _ in
            updateRecordingState()
        }
        .onChange(of: audioManager.isRecording) { _, _ in
            updateRecordingState()
            triggerRecordingAnimation()
        }
        .onChange(of: audioManager.detectedKeyword) { _, newKeyword in
            updateRecordingState()
            if !newKeyword.isEmpty {
                triggerKeywordDetectedFeedback()
            }
        }
        .sheet(isPresented: $showingSaveDialog) {
            SaveNoteView(
                title: $noteTitle,
                content: audioManager.transcribedText,
                audioURL: audioManager.currentAudioURL?.path,
                duration: audioManager.recordingDuration,
                selectedKeywords: $selectedKeywords,
                availableKeywords: keywords,
                dataManager: dataManager,
                onSave: {
                    isPresented = false
                }
            )
        }
    }
    
    // MARK: - Top Bar
    private var topBar: some View {
        HStack {
            Button("Cancel") {
                audioManager.stopListening()
                let _ = audioManager.stopRecording()
                isPresented = false
            }
            .foregroundColor(.white)
            .font(.system(size: 16, weight: .medium))
            
            Spacer()
        }
        .padding(.top, 10)
    }
    
    // MARK: - Animated Waveform
    private var animatedWaveformView: some View {
        VStack(spacing: 15) {
            // Cute animated waveform
            HStack(spacing: 3) {
                ForEach(0..<20, id: \.self) { index in
                    RoundedRectangle(cornerRadius: 2)
                        .fill(waveformColor)
                        .frame(width: 4, height: waveformHeight(for: index))
                        .animation(
                            Animation.easeInOut(duration: 0.5)
                                .repeatForever(autoreverses: true)
                                .delay(Double(index) * 0.1),
                            value: waveformAnimation
                        )
                }
            }
            .scaleEffect(recordingState == .recording ? 1.1 : 1.0)
            .animation(.spring(response: 0.5, dampingFraction: 0.6), value: recordingState)
        }
        .onAppear {
            startWaveformAnimation()
        }
    }
    
    // MARK: - Status Section
    private var statusSection: some View {
        VStack(spacing: 12) {
            // Main status text with animation
            Text(recordingState.statusText)
                .font(.system(size: 24, weight: .semibold))
                .foregroundColor(.white)
                .scaleEffect(keywordDetectedAnimation ? 1.2 : 1.0)
                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: keywordDetectedAnimation)
            
            // Subtitle with breathing animation
            if recordingState == .listening {
                Text("Say \"Idea\", \"Groceries\", etc.")
                    .font(.system(size: 16))
                    .foregroundColor(.white.opacity(0.7))
                    .opacity(breathingAnimation ? 0.5 : 1.0)
                    .animation(
                        Animation.easeInOut(duration: 2.0)
                            .repeatForever(autoreverses: true),
                        value: breathingAnimation
                    )
            } else if recordingState == .keywordDetected {
                Text("🎉 Keyword Detected!")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.green)
                    .scaleEffect(pulseAnimation ? 1.1 : 1.0)
                    .animation(
                        Animation.easeInOut(duration: 0.5)
                            .repeatCount(3, autoreverses: true),
                        value: pulseAnimation
                    )
            } else if recordingState == .recording {
                Text("Recording note...")
                    .font(.system(size: 16))
                    .foregroundColor(.red.opacity(0.8))
                    .opacity(recordingPulse ? 0.6 : 1.0)
                    .animation(
                        Animation.easeInOut(duration: 1.0)
                            .repeatForever(autoreverses: true),
                        value: recordingPulse
                    )
            }
        }
    }
    
    // MARK: - Timer Section
    private var timerSection: some View {
        Text(audioManager.formatDuration(audioManager.recordingDuration))
            .font(.system(size: 32, weight: .bold, design: .monospaced))
            .foregroundColor(.green)
            .scaleEffect(recordingPulse ? 1.05 : 1.0)
            .animation(
                Animation.easeInOut(duration: 1.0)
                    .repeatForever(autoreverses: true),
                value: recordingPulse
            )
    }
    
    // MARK: - Transcription Section
    private var transcriptionSection: some View {
        Group {
            if !audioManager.transcribedText.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Live Transcription")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    ScrollView {
                        Text(audioManager.transcribedText)
                            .font(.system(size: 16))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding(12)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.white.opacity(0.1))
                            )
                    }
                    .frame(maxHeight: 120)
                }
                .transition(.opacity.combined(with: .move(edge: .bottom)))
                .animation(.easeInOut(duration: 0.3), value: audioManager.transcribedText.isEmpty)
            }
        }
    }
    
    // MARK: - Control Buttons
    private var controlButtonsSection: some View {
        HStack(spacing: 30) {
            if recordingState == .recording {
                // Pause/Resume button
                Button(action: {
                    if audioManager.isRecordingPaused {
                        audioManager.resumeRecording()
                    } else {
                        audioManager.pauseRecording()
                    }
                }) {
                    Image(systemName: audioManager.isRecordingPaused ? "play.fill" : "pause.fill")
                        .font(.title)
                        .foregroundColor(.white)
                        .frame(width: 60, height: 60)
                        .background(
                            Circle()
                                .fill(Color.blue.opacity(0.8))
                        )
                }
                
                // Stop button
                Button(action: {
                    stopRecording()
                }) {
                    Image(systemName: "stop.fill")
                        .font(.title)
                        .foregroundColor(.white)
                        .frame(width: 60, height: 60)
                        .background(
                            Circle()
                                .fill(Color.red.opacity(0.8))
                        )
                }
            } else if recordingState == .listening {
                Button(action: {
                    isPresented = false
                }) {
                    Text("Cancel")
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding(.horizontal, 30)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 25)
                                .fill(Color.gray.opacity(0.3))
                        )
                }
            }
        }
    }

    // MARK: - Helper Methods

    private func setupView() {
        // Update keywords from database
        audioManager.updateKeywords(from: Array(keywords))

        // Add some test keywords if none exist
        if keywords.isEmpty {
            print("⚠️ No keywords in database, adding test keywords")
            audioManager.addKeyword("keyword")
            audioManager.addKeyword("note")
            audioManager.addKeyword("record")
        }

        print("🔧 Setup complete. Keywords: \(audioManager.getRegisteredKeywords())")

        // Set auto-save based on background monitoring preference
        autoSaveEnabled = preferences?.backgroundMonitorEnabled ?? true

        // Start refresh timer for UI updates
        refreshTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { _ in
            DispatchQueue.main.async {
                self.updateRecordingState()
            }
        }

        // Start breathing animation
        breathingAnimation = true

        // Don't start listening here - BackgroundMonitorService is already running
        // Just ensure permissions are granted
        Task {
            await audioManager.requestPermissionsAsync()
        }

        // Listen for keyword detection from BackgroundMonitorService
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("KeywordDetectedRecordingStarted"),
            object: nil,
            queue: .main
        ) { notification in
            if let keywords = notification.userInfo?["keywords"] as? [String],
               let text = notification.userInfo?["text"] as? String {
                print("🎯 UnifiedRecordingView: Keyword detected by BackgroundMonitorService: \(keywords)")
                self.handleKeywordDetected(keywords: keywords, text: text)
            }
        }

        // Listen for recording finished from BackgroundMonitorService
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("KeywordRecordingFinished"),
            object: nil,
            queue: .main
        ) { notification in
            if let text = notification.userInfo?["text"] as? String,
               let duration = notification.userInfo?["duration"] as? TimeInterval,
               let keywords = notification.userInfo?["keywords"] as? [String] {
                print("🏁 UnifiedRecordingView: Recording finished by BackgroundMonitorService")
                self.handleRecordingFinished(text: text, duration: duration, keywords: keywords)
            }
        }
    }

    private func cleanup() {
        refreshTimer?.invalidate()
        refreshTimer = nil
    }

    private func startListening() {
        // Don't start AudioManager listening - BackgroundMonitorService is already running
        // Just update the UI state
        print("🎧 UnifiedRecordingView: Using BackgroundMonitorService for keyword detection")
        updateRecordingState()
    }

    private func handleKeywordDetected(keywords: [String], text: String) {
        print("🎯 Handling keyword detection: \(keywords)")

        // Trigger haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        // Update UI to show keyword detected
        audioManager.detectedKeyword = keywords.first ?? ""
        audioManager.transcribedText = text

        // Don't start AudioManager recording - BackgroundMonitorService is handling recording
        // Just update UI state to show recording is active
        audioManager.isRecording = true
        audioManager.recordingDuration = 0

        // Update UI state
        updateRecordingState()
    }

    private func handleRecordingFinished(text: String, duration: TimeInterval, keywords: [String]) {
        print("🏁 Handling recording finished: \(text.prefix(50))...")

        // Update AudioManager state
        audioManager.isRecording = false
        audioManager.transcribedText = text
        audioManager.recordingDuration = duration
        audioManager.detectedKeyword = keywords.first ?? ""

        // Generate a title based on detected keyword or transcription
        noteTitle = generateNoteTitle()

        // Find matching keywords
        selectedKeywords = findMatchingKeywords()

        // Auto-save if there's transcribed text
        if autoSaveEnabled && !text.isEmpty {
            // Create a dummy audio URL since BackgroundMonitorService handles the actual recording
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
            let dummyURL = documentsPath.appendingPathComponent("background_recording_\(Date().timeIntervalSince1970).m4a")
            autoSaveNote(audioURL: dummyURL)
        } else if !text.isEmpty {
            // Show save dialog if auto-save is disabled but there's content
            showingSaveDialog = true
        } else {
            // No content to save, just close
            isPresented = false
        }

        // Update UI state
        updateRecordingState()
    }

    private func updateRecordingState() {
        let newState: RecordingState

        if audioManager.isRecording {
            newState = .recording
        } else if !audioManager.detectedKeyword.isEmpty {
            newState = .keywordDetected
        } else if audioManager.isListening {
            newState = .listening
        } else {
            newState = .listening
        }

        recordingState = newState
    }

    private func triggerKeywordDetectedFeedback() {
        // Haptic feedback
        if preferences?.hapticFeedbackEnabled ?? true {
            let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
            impactFeedback.impactOccurred()
        }

        // Visual animation
        withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
            keywordDetectedAnimation = true
            pulseAnimation = true
        }

        // Reset animation after delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            withAnimation {
                keywordDetectedAnimation = false
                pulseAnimation = false
            }
        }

        // Auto-start recording after keyword detection
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.audioManager.startRecording()
        }
    }

    private func triggerRecordingAnimation() {
        if audioManager.isRecording {
            recordingPulse = true
        } else {
            recordingPulse = false
        }
    }

    private func startWaveformAnimation() {
        waveformAnimation = true
    }

    private var waveformColor: Color {
        switch recordingState {
        case .listening:
            return .green.opacity(0.6)
        case .keywordDetected:
            return .yellow
        case .recording:
            return .red.opacity(0.8)
        }
    }

    private func waveformHeight(for index: Int) -> CGFloat {
        let baseHeight: CGFloat = 8
        let maxHeight: CGFloat = 40

        if recordingState == .recording {
            // Use actual audio levels if available
            if index < audioManager.audioLevels.count {
                let level = audioManager.audioLevels[index]
                return baseHeight + (CGFloat(level) * (maxHeight - baseHeight))
            }
        }

        // Animated pattern for listening state
        let animationOffset = waveformAnimation ? sin(Double(index) * 0.5 + Date().timeIntervalSince1970 * 2) : 0
        return baseHeight + CGFloat(animationOffset) * 15
    }

    private func stopRecording() {
        if let audioURL = audioManager.stopRecording() {
            // Generate a title based on detected keyword or transcription
            noteTitle = generateNoteTitle()

            // Find matching keywords
            selectedKeywords = findMatchingKeywords()

            // Auto-save if there's transcribed text
            if autoSaveEnabled && !audioManager.transcribedText.isEmpty {
                autoSaveNote(audioURL: audioURL)
            } else if !audioManager.transcribedText.isEmpty {
                // Show save dialog if auto-save is disabled but there's content
                showingSaveDialog = true
            } else {
                // No content to save, just close
                isPresented = false
            }
        } else {
            isPresented = false
        }
    }

    private func autoSaveNote(audioURL: URL) {
        // Check user preference for recording audio
        let shouldIncludeAudio = preferences?.recordAudioWhenCreatingNote ?? true
        let finalAudioURL = shouldIncludeAudio ? audioURL.path : nil

        // Create note with safe relationships
        let note = createNoteWithSafeRelationships(
            title: noteTitle,
            content: audioManager.transcribedText,
            audioURL: finalAudioURL,
            keywords: selectedKeywords
        )

        // Set the recording duration if audio is included
        if shouldIncludeAudio && audioManager.recordingDuration > 0 {
            note.duration = audioManager.recordingDuration
        }

        // Close the recording view
        isPresented = false
    }

    private func createNoteWithSafeRelationships(title: String, content: String, audioURL: String? = nil, keywords: [Keyword]) -> Note {
        // Create note with keywords directly - let SwiftData manage the relationships
        let note = Note(title: title, content: content, audioURL: audioURL, keywords: keywords)
        modelContext.insert(note)

        // Update usage counts only
        for keyword in keywords {
            keyword.incrementUsage()
        }

        // Save everything
        do {
            try modelContext.save()
        } catch {
            print("Error saving note: \(error)")
        }

        return note
    }

    private func generateNoteTitle() -> String {
        if !audioManager.detectedKeyword.isEmpty {
            let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .short)
            return "\(audioManager.detectedKeyword.capitalized) - \(timestamp)"
        } else if !audioManager.transcribedText.isEmpty {
            let words = audioManager.transcribedText.components(separatedBy: .whitespaces)
            let firstWords = Array(words.prefix(3)).joined(separator: " ")
            return firstWords.isEmpty ? "Voice Note" : firstWords
        } else {
            return "Voice Note"
        }
    }

    private func findMatchingKeywords() -> [Keyword] {
        let detectedKeyword = audioManager.detectedKeyword.lowercased()

        // Only use the keyword that triggered the recording
        if !detectedKeyword.isEmpty {
            let triggerKeyword = keywords.first { keyword in
                keyword.isEnabled && keyword.text.lowercased() == detectedKeyword
            }

            if let triggerKeyword = triggerKeyword {
                return [triggerKeyword]
            }
        }

        return []
    }
}

// MARK: - SaveNoteView
struct SaveNoteView: View {
    @Binding var title: String
    let content: String
    let audioURL: String?
    let duration: TimeInterval
    @Binding var selectedKeywords: [Keyword]
    let availableKeywords: [Keyword]
    let dataManager: DataManager
    let onSave: () -> Void

    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @StateObject private var themeManager = ThemeManager.shared
    @Query(sort: \UserPreferences.createdDate) private var userPreferences: [UserPreferences]

    private var preferences: UserPreferences? {
        userPreferences.first
    }

    var body: some View {
        NavigationView {
            ZStack {
                // Apply global background theme
                Group {
                    if themeManager.isGradientTheme,
                       let gradient = themeManager.applyGradient() {
                        gradient.ignoresSafeArea()
                    } else {
                        themeManager.applyBackground().ignoresSafeArea()
                    }
                }

                VStack(spacing: 20) {
                TextField("Note title", text: $title)
                    .padding(12)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.gray.opacity(0.2))
                    )
                    .foregroundColor(.white)

                Text("Content Preview")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity, alignment: .leading)

                ScrollView {
                    Text(content)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding()
                        .background(Color.gray.opacity(0.2))
                        .cornerRadius(8)
                }
                .frame(maxHeight: 200)

                if !availableKeywords.isEmpty {
                    Text("Keywords")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 10) {
                        ForEach(availableKeywords, id: \.id) { keyword in
                            Button(action: {
                                toggleKeyword(keyword)
                            }) {
                                Text(keyword.text)
                                    .font(.caption)
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 6)
                                    .background(
                                        RoundedRectangle(cornerRadius: 15)
                                            .fill(selectedKeywords.contains(where: { $0.id == keyword.id }) ? Color.blue : Color.gray.opacity(0.3))
                                    )
                                    .foregroundColor(.white)
                            }
                        }
                    }
                }

                Spacer()
            }
            .padding()
            }
            .navigationTitle("Save Note")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveNote()
                    }
                    .foregroundColor(.white)
                    .fontWeight(.semibold)
                }
            }
        }
    }

    private func toggleKeyword(_ keyword: Keyword) {
        if let index = selectedKeywords.firstIndex(where: { $0.id == keyword.id }) {
            selectedKeywords.remove(at: index)
        } else {
            selectedKeywords.append(keyword)
        }
    }

    private func saveNote() {
        // Check user preference for recording audio
        let shouldIncludeAudio = preferences?.recordAudioWhenCreatingNote ?? true
        let finalAudioURL = shouldIncludeAudio ? audioURL : nil

        let note = dataManager.createNote(
            title: title,
            content: content,
            audioURL: finalAudioURL,
            keywords: selectedKeywords
        )

        if duration > 0 && shouldIncludeAudio {
            note.duration = duration
        }

        onSave()
        dismiss()
    }
}

#Preview {
    UnifiedRecordingView(isPresented: .constant(true))
        .modelContainer(for: [Note.self, Keyword.self, UserPreferences.self], inMemory: true)
}
