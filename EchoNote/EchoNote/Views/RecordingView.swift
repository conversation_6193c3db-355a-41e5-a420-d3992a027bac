//
//  RecordingView.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI
import SwiftData
import AVFoundation

struct RecordingView: View {
    @Binding var isPresented: Bool
    @Environment(\.modelContext) private var modelContext

    @State private var audioManager = AudioManager()
    @State private var recordingState: RecordingState = .listening
    @State private var showingSaveDialog = false
    @State private var noteTitle = ""
    @State private var selectedKeywords: [Keyword] = []
    @State private var autoSaveEnabled = true // Enable auto-save by default
    @State private var refreshTimer: Timer? // For UI refresh

    @Query private var keywords: [Keyword]
    @Query private var userPreferences: [UserPreferences]
    @StateObject private var accessibilityService = AccessibilityService.shared
    @StateObject private var themeManager = ThemeManager.shared

    private var preferences: UserPreferences? {
        userPreferences.first
    }

    private var dataManager: DataManager {
        DataManager(modelContext: modelContext)
    }

    @State private var keywordManager: KeywordManager?

    var body: some View {
        ZStack {
            themeManager.applyBackground().ignoresSafeArea()

            VStack(spacing: 40) {
                // Close button
                HStack {
                    Button("Cancel") {
                        audioManager.stopListening()
                        audioManager.stopRecording()
                        isPresented = false
                    }
                    .foregroundColor(.white)
                    .accessibilityLabel("Cancel recording")
                    .accessibilityHint(accessibilityService.buttonHint("cancel recording and return to main screen"))

                    Spacer()
                }
                .padding(.top, 20)

                Spacer()

                // Waveform visualization
                waveformView

                // Recording status
                statusView

                // Recording timer
                if recordingState == .recording {
                    timerView
                }

                // Transcribed text
                if !audioManager.transcribedText.isEmpty {
                    transcriptionView
                }





                Spacer()

                // Control buttons
                controlButtons

                Spacer()
            }
            .padding(.horizontal, 30)
        }
        .onAppear {
            // Update keywords from database
            audioManager.updateKeywords(from: Array(keywords))

            // Set auto-save based on background monitoring preference
            // If background monitoring is enabled, user expects automatic behavior
            autoSaveEnabled = preferences?.backgroundMonitorEnabled ?? true

            // Start refresh timer for UI updates
            refreshTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { _ in
                // Force UI refresh by triggering state change
                // This helps ensure UI stays in sync with AudioManager state
                DispatchQueue.main.async {
                    self.updateRecordingState()
                }
            }

            // Request permissions first, then start listening
            Task {
                await audioManager.requestPermissionsAsync()
                DispatchQueue.main.async {
                    self.startListening()
                }
            }
        }
        .onDisappear {
            refreshTimer?.invalidate()
            refreshTimer = nil
        }
        .onChange(of: audioManager.isListening) { _, _ in
            updateRecordingState()
        }
        .onChange(of: audioManager.isRecording) { _, _ in
            updateRecordingState()
        }
        .onChange(of: audioManager.detectedKeyword) { _, _ in
            updateRecordingState()
        }
        .sheet(isPresented: $showingSaveDialog) {
            SaveNoteView(
                title: $noteTitle,
                content: audioManager.transcribedText,
                audioURL: audioManager.currentAudioURL?.path,
                duration: audioManager.recordingDuration,
                selectedKeywords: $selectedKeywords,
                availableKeywords: keywords,
                dataManager: dataManager,
                onSave: {
                    isPresented = false
                }
            )
        }
    }
    
    private var waveformView: some View {
        VStack(spacing: 15) {
            // Optimized real-time waveform with reduced animation frequency
            HStack(spacing: 3) {
                ForEach(0..<30, id: \.self) { index in
                    RoundedRectangle(cornerRadius: 2)
                        .fill(waveformColor(for: index))
                        .frame(width: 4, height: waveformHeight(for: index))
                        .animation(.easeInOut(duration: 0.2), value: audioManager.recordingAudioLevel) // Slightly slower for better performance
                }
            }
            .frame(height: 100)
            .drawingGroup() // Enable Metal acceleration
            .accessibilityElement(children: .ignore)
            .accessibilityLabel("Audio waveform visualization")
            .accessibilityValue(accessibilityService.audioLevelLabel(audioManager.recordingAudioLevel))

            // Audio level indicator
            if recordingState == .recording {
                VStack(spacing: 8) {
                    Text("Audio Level")
                        .font(.caption)
                        .foregroundColor(.gray)

                    HStack(spacing: 4) {
                        ForEach(0..<10, id: \.self) { index in
                            RoundedRectangle(cornerRadius: 1)
                                .fill(audioLevelColor(for: index))
                                .frame(width: 20, height: 4)
                        }
                    }
                }
            }
        }
    }

    private func waveformHeight(for index: Int) -> CGFloat {
        let baseHeight: CGFloat = 8
        let maxHeight: CGFloat = 80

        switch recordingState {
        case .listening:
            // Gentle pulsing animation when listening
            let pulse = sin(Date().timeIntervalSince1970 * 2 + Double(index) * 0.3) * 0.3 + 0.7
            return baseHeight + CGFloat(pulse) * 20

        case .keywordDetected:
            // Excited animation when keyword detected
            let excitement = sin(Date().timeIntervalSince1970 * 8 + Double(index) * 0.5) * 0.5 + 0.5
            return baseHeight + CGFloat(excitement) * 40

        case .recording:
            // Real audio levels when recording
            let audioLevel = CGFloat(audioManager.recordingAudioLevel)
            let variation = sin(Double(index) * 0.5) * 0.2 + 0.8
            return baseHeight + audioLevel * maxHeight * CGFloat(variation)
        }
    }

    private func waveformColor(for index: Int) -> Color {
        switch recordingState {
        case .listening:
            return Color.green.opacity(0.6)
        case .keywordDetected:
            return Color.yellow.opacity(0.8)
        case .recording:
            let audioLevel = audioManager.recordingAudioLevel
            if audioLevel > 0.7 {
                return Color.red.opacity(0.8)
            } else if audioLevel > 0.3 {
                return Color.orange.opacity(0.8)
            } else {
                return Color.green.opacity(0.8)
            }
        }
    }

    private func audioLevelColor(for index: Int) -> Color {
        let audioLevel = audioManager.recordingAudioLevel
        let threshold = Float(index) / 10.0

        if audioLevel > threshold {
            if threshold > 0.7 {
                return Color.red
            } else if threshold > 0.4 {
                return Color.orange
            } else {
                return Color.green
            }
        } else {
            return Color.gray.opacity(0.3)
        }
    }
    
    private var statusView: some View {
        VStack(spacing: 10) {
            Text(recordingState.statusText)
                .font(accessibilityService.scaledFont(.title2))
                .fontWeight(accessibilityService.fontWeight(.semibold))
                .foregroundColor(.white)
            
            if recordingState == .listening {
                Text("Say \"Idea\", \"Groceries\", etc.")
                    .font(accessibilityService.scaledFont(.title))
                    .foregroundColor(.gray)
            } else if recordingState == .keywordDetected {
                Text(audioManager.detectedKeyword.capitalized)
                    .font(accessibilityService.scaledFont(.largeTitle))
                    .fontWeight(accessibilityService.fontWeight(.bold))
                    .foregroundColor(.green)
                    .scaleEffect(1.1)
                    .accessibleSpringAnimation(response: 0.5, dampingFraction: 0.6)
            } else if recordingState == .recording {
                Text("Recording note...")
                    .font(accessibilityService.scaledFont(.title))
                    .foregroundColor(.pink)
            }
        }
    }
    
    private var timerView: some View {
        Text(audioManager.formatDuration(audioManager.recordingDuration))
            .font(accessibilityService.scaledFont(.title))
            .fontWeight(accessibilityService.fontWeight(.bold))
            .foregroundColor(.green)
            .monospacedDigit()
            .accessibilityLabel(accessibilityService.recordingDurationLabel(audioManager.recordingDuration))
    }
    
    private var transcriptionView: some View {
        ScrollView {
            Text(audioManager.transcribedText)
                .font(.body)
                .foregroundColor(.white)
                .padding(20)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.2))
                )
        }
        .frame(maxHeight: 150)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Transcribed text")
        .accessibilityValue(audioManager.transcribedText.isEmpty ? "No text transcribed yet" : audioManager.transcribedText)
    }
    
    private var controlButtons: some View {
        HStack(spacing: 40) {
            if recordingState == .recording {
                // Pause/Resume button
                Button(action: {
                    if audioManager.isRecordingPaused {
                        audioManager.resumeRecording()
                    } else {
                        audioManager.pauseRecording()
                    }
                }) {
                    Image(systemName: audioManager.isRecordingPaused ? "play.fill" : "pause.fill")
                        .font(.title)
                        .foregroundColor(.white)
                        .frame(width: 60, height: 60)
                        .background(
                            Circle()
                                .fill(Color.gray.opacity(0.3))
                        )
                }
                .accessibilityLabel(audioManager.isRecordingPaused ? "Resume recording" : "Pause recording")
                .accessibilityHint(accessibilityService.buttonHint(audioManager.isRecordingPaused ? "resume recording" : "pause recording"))

                // Stop button
                Button(action: {
                    stopRecording()
                }) {
                    Image(systemName: "stop.fill")
                        .font(.title)
                        .foregroundColor(.white)
                        .frame(width: 60, height: 60)
                        .background(
                            Circle()
                                .fill(Color.red)
                        )
                }
                .accessibilityLabel("Stop recording")
                .accessibilityHint(accessibilityService.buttonHint("stop recording and save note"))
            } else if recordingState == .listening {
                Button(action: {
                    isPresented = false
                }) {
                    Text("Cancel")
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding(.horizontal, 30)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 25)
                                .fill(Color.gray.opacity(0.3))
                        )
                }
            }


        }
    }

    private func updateRecordingState() {
        let newState: RecordingState

        if audioManager.isRecording {
            newState = .recording
        } else if !audioManager.detectedKeyword.isEmpty {
            newState = .keywordDetected
        } else if audioManager.isListening {
            newState = .listening
        } else {
            newState = .listening // Default fallback
        }

        recordingState = newState
    }

    private func startListening() {
        audioManager.startListening()

        // Update state after a short delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.updateRecordingState()
        }
    }

    private func stopRecording() {
        if let audioURL = audioManager.stopRecording() {
            // Generate a title based on detected keyword or transcription
            noteTitle = generateNoteTitle()

            // Find matching keywords
            selectedKeywords = findMatchingKeywords()

            // Auto-save if there's transcribed text (with or without keywords)
            if autoSaveEnabled && !audioManager.transcribedText.isEmpty {
                autoSaveNote(audioURL: audioURL)
            } else if !audioManager.transcribedText.isEmpty {
                // Show save dialog if auto-save is disabled but there's content
                showingSaveDialog = true
            } else {
                // No content to save, just close
                isPresented = false
            }
        } else {
            isPresented = false
        }
    }

    private func autoSaveNote(audioURL: URL) {
        // Check user preference for recording audio
        let shouldIncludeAudio = preferences?.recordAudioWhenCreatingNote ?? true
        let finalAudioURL = shouldIncludeAudio ? audioURL.path : nil

        // Use improved DataManager method with better relationship handling
        let note = createNoteWithSafeRelationships(
            title: noteTitle,
            content: audioManager.transcribedText,
            audioURL: finalAudioURL,
            keywords: selectedKeywords
        )

        // Set the recording duration if audio is included
        if shouldIncludeAudio && audioManager.recordingDuration > 0 {
            note.duration = audioManager.recordingDuration
        }

        // Close the recording view
        isPresented = false
    }

    private func createNoteWithSafeRelationships(title: String, content: String, audioURL: String? = nil, keywords: [Keyword]) -> Note {
        // Create note with keywords directly - let SwiftData manage the relationships
        let note = Note(title: title, content: content, audioURL: audioURL, keywords: keywords)
        modelContext.insert(note)

        // Update usage counts only
        for keyword in keywords {
            keyword.incrementUsage()
        }

        // Save everything
        do {
            try modelContext.save()
        } catch {
            // Log error for debugging but don't show to user
            print("Error saving note: \(error)")
        }

        return note
    }

    private func generateNoteTitle() -> String {
        if !audioManager.detectedKeyword.isEmpty {
            return "\(audioManager.detectedKeyword.capitalized) Note"
        } else if !audioManager.transcribedText.isEmpty {
            let words = audioManager.transcribedText.components(separatedBy: .whitespaces)
            let firstWords = Array(words.prefix(3)).joined(separator: " ")
            return firstWords.isEmpty ? "Voice Note" : firstWords
        } else {
            return "Voice Note"
        }
    }

    private func findMatchingKeywords() -> [Keyword] {
        let detectedKeyword = audioManager.detectedKeyword.lowercased()

        // ONLY use the keyword that triggered the recording
        // Do NOT search for additional keywords in the transcription content
        if !detectedKeyword.isEmpty {
            let triggerKeyword = keywords.first { keyword in
                keyword.isEnabled && keyword.text.lowercased() == detectedKeyword
            }

            if let triggerKeyword = triggerKeyword {
                return [triggerKeyword]
            }
        }

        return []
    }
}

enum RecordingState {
    case listening
    case keywordDetected
    case recording

    var statusText: String {
        switch self {
        case .listening:
            return "Listening for keyword..."
        case .keywordDetected:
            return "Keyword Detected!"
        case .recording:
            return "Recording"
        }
    }
}

extension Array {
    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}

#Preview {
    RecordingView(isPresented: .constant(true))
        .modelContainer(for: [Note.self, Keyword.self, UserPreferences.self], inMemory: true)
}
