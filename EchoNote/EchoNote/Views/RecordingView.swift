//
//  RecordingView.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI
import SwiftData
import AVFoundation
import Combine

struct RecordingView: View {
    @Binding var isPresented: Bool
    @Environment(\.modelContext) private var modelContext
    @Environment(\.serviceManager) private var serviceManager

    @State private var recordingCoordinator: RecordingCoordinator?
    @State private var recordingState: RecordingState = .idle
    @State private var showingSaveDialog = false
    @State private var noteTitle = ""
    @State private var selectedKeywords: [Keyword] = []
    @State private var autoSaveEnabled = true
    @State private var refreshTimer: Timer?
    @State private var cancellables = Set<AnyCancellable>()

    @Query private var keywords: [Keyword]
    @Query private var userPreferences: [UserPreferences]
    @StateObject private var accessibilityService = AccessibilityService.shared
    @StateObject private var themeManager = ThemeManager.shared

    private var preferences: UserPreferences? {
        userPreferences.first
    }

    private var dataManager: DataManager {
        DataManager(modelContext: modelContext)
    }

    @State private var keywordManager: KeywordManager?

    var body: some View {
        ZStack {
            themeManager.applyBackground().ignoresSafeArea()

            VStack(spacing: 40) {
                // Close button
                HStack {
                    Button("Cancel") {
                        Task {
                            await recordingCoordinator?.cancelCurrentRecording()
                            isPresented = false
                        }
                    }
                    .foregroundColor(.white)
                    .accessibilityLabel("Cancel recording")
                    .accessibilityHint(accessibilityService.buttonHint("cancel recording and return to main screen"))

                    Spacer()
                }
                .padding(.top, 20)

                Spacer()

                // Waveform visualization
                waveformView

                // Recording status
                statusView

                // Recording timer
                if recordingState == .recording {
                    timerView
                }

                // Transcribed text
                if !audioManager.transcribedText.isEmpty {
                    transcriptionView
                }





                Spacer()

                // Control buttons
                controlButtons

                Spacer()
            }
            .padding(.horizontal, 30)
        }
        .onAppear {
            // Set auto-save based on background monitoring preference
            autoSaveEnabled = preferences?.backgroundMonitorEnabled ?? true

            // Initialize recording coordinator
            Task {
                if let coordinator = await serviceManager.getRecordingCoordinator() {
                    recordingCoordinator = coordinator
                    setupRecordingStateBindings()

                    // Start manual recording
                    try? await coordinator.startManualRecording()
                }
            }
        }
        .onDisappear {
            refreshTimer?.invalidate()
            refreshTimer = nil

            // Cancel recording if still active
            Task {
                await recordingCoordinator?.cancelCurrentRecording()
            }
        }
        .sheet(isPresented: $showingSaveDialog) {
            SaveNoteView(
                title: $noteTitle,
                content: audioManager.transcribedText,
                audioURL: audioManager.currentAudioURL?.path,
                duration: audioManager.recordingDuration,
                selectedKeywords: $selectedKeywords,
                availableKeywords: keywords,
                dataManager: dataManager,
                onSave: {
                    isPresented = false
                }
            )
        }
    }
    
    private var waveformView: some View {
        VStack(spacing: 15) {
            // Optimized real-time waveform with reduced animation frequency
            HStack(spacing: 3) {
                ForEach(0..<30, id: \.self) { index in
                    RoundedRectangle(cornerRadius: 2)
                        .fill(waveformColor(for: index))
                        .frame(width: 4, height: waveformHeight(for: index))
                        .animation(.easeInOut(duration: 0.2), value: audioManager.recordingAudioLevel) // Slightly slower for better performance
                }
            }
            .frame(height: 100)
            .drawingGroup() // Enable Metal acceleration
            .accessibilityElement(children: .ignore)
            .accessibilityLabel("Audio waveform visualization")
            .accessibilityValue(accessibilityService.audioLevelLabel(audioManager.recordingAudioLevel))

            // Audio level indicator
            if recordingState == .recording {
                VStack(spacing: 8) {
                    Text("Audio Level")
                        .font(.caption)
                        .foregroundColor(.gray)

                    HStack(spacing: 4) {
                        ForEach(0..<10, id: \.self) { index in
                            RoundedRectangle(cornerRadius: 1)
                                .fill(audioLevelColor(for: index))
                                .frame(width: 20, height: 4)
                        }
                    }
                }
            }
        }
    }

    private func waveformHeight(for index: Int) -> CGFloat {
        let baseHeight: CGFloat = 8
        let maxHeight: CGFloat = 80

        switch recordingState {
        case .listening:
            // Gentle pulsing animation when listening
            let pulse = sin(Date().timeIntervalSince1970 * 2 + Double(index) * 0.3) * 0.3 + 0.7
            return baseHeight + CGFloat(pulse) * 20

        case .keywordDetected:
            // Excited animation when keyword detected
            let excitement = sin(Date().timeIntervalSince1970 * 8 + Double(index) * 0.5) * 0.5 + 0.5
            return baseHeight + CGFloat(excitement) * 40

        case .recording:
            // Real audio levels when recording
            let audioLevel = CGFloat(audioManager.recordingAudioLevel)
            let variation = sin(Double(index) * 0.5) * 0.2 + 0.8
            return baseHeight + audioLevel * maxHeight * CGFloat(variation)
        }
    }

    private func waveformColor(for index: Int) -> Color {
        switch recordingState {
        case .listening:
            return Color.green.opacity(0.6)
        case .keywordDetected:
            return Color.yellow.opacity(0.8)
        case .recording:
            let audioLevel = audioManager.recordingAudioLevel
            if audioLevel > 0.7 {
                return Color.red.opacity(0.8)
            } else if audioLevel > 0.3 {
                return Color.orange.opacity(0.8)
            } else {
                return Color.green.opacity(0.8)
            }
        }
    }

    private func audioLevelColor(for index: Int) -> Color {
        let audioLevel = audioManager.recordingAudioLevel
        let threshold = Float(index) / 10.0

        if audioLevel > threshold {
            if threshold > 0.7 {
                return Color.red
            } else if threshold > 0.4 {
                return Color.orange
            } else {
                return Color.green
            }
        } else {
            return Color.gray.opacity(0.3)
        }
    }
    
    private var statusView: some View {
        VStack(spacing: 10) {
            Text(recordingState.statusText)
                .font(accessibilityService.scaledFont(.title2))
                .fontWeight(accessibilityService.fontWeight(.semibold))
                .foregroundColor(.white)
            
            if recordingState == .listening {
                Text("Say \"Idea\", \"Groceries\", etc.")
                    .font(accessibilityService.scaledFont(.title))
                    .foregroundColor(.gray)
            } else if recordingState == .keywordDetected {
                Text(audioManager.detectedKeyword.capitalized)
                    .font(accessibilityService.scaledFont(.largeTitle))
                    .fontWeight(accessibilityService.fontWeight(.bold))
                    .foregroundColor(.green)
                    .scaleEffect(1.1)
                    .accessibleSpringAnimation(response: 0.5, dampingFraction: 0.6)
            } else if recordingState == .recording {
                Text("Recording note...")
                    .font(accessibilityService.scaledFont(.title))
                    .foregroundColor(.pink)
            }
        }
    }
    
    private var timerView: some View {
        Text(audioManager.formatDuration(audioManager.recordingDuration))
            .font(accessibilityService.scaledFont(.title))
            .fontWeight(accessibilityService.fontWeight(.bold))
            .foregroundColor(.green)
            .monospacedDigit()
            .accessibilityLabel(accessibilityService.recordingDurationLabel(audioManager.recordingDuration))
    }
    
    private var transcriptionView: some View {
        ScrollView {
            Text(audioManager.transcribedText)
                .font(.body)
                .foregroundColor(.white)
                .padding(20)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.2))
                )
        }
        .frame(maxHeight: 150)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Transcribed text")
        .accessibilityValue(audioManager.transcribedText.isEmpty ? "No text transcribed yet" : audioManager.transcribedText)
    }
    
    private var controlButtons: some View {
        HStack(spacing: 40) {
            if recordingState == .recording {
                // Pause/Resume button
                Button(action: {
                    if audioManager.isRecordingPaused {
                        audioManager.resumeRecording()
                    } else {
                        audioManager.pauseRecording()
                    }
                }) {
                    Image(systemName: audioManager.isRecordingPaused ? "play.fill" : "pause.fill")
                        .font(.title)
                        .foregroundColor(.white)
                        .frame(width: 60, height: 60)
                        .background(
                            Circle()
                                .fill(Color.gray.opacity(0.3))
                        )
                }
                .accessibilityLabel(audioManager.isRecordingPaused ? "Resume recording" : "Pause recording")
                .accessibilityHint(accessibilityService.buttonHint(audioManager.isRecordingPaused ? "resume recording" : "pause recording"))

                // Stop button
                Button(action: {
                    stopRecording()
                }) {
                    Image(systemName: "stop.fill")
                        .font(.title)
                        .foregroundColor(.white)
                        .frame(width: 60, height: 60)
                        .background(
                            Circle()
                                .fill(Color.red)
                        )
                }
                .accessibilityLabel("Stop recording")
                .accessibilityHint(accessibilityService.buttonHint("stop recording and save note"))
            } else if recordingState == .listening {
                Button(action: {
                    isPresented = false
                }) {
                    Text("Cancel")
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding(.horizontal, 30)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 25)
                                .fill(Color.gray.opacity(0.3))
                        )
                }
            }


        }
    }

    private func updateRecordingState() {
        let newState: RecordingState

        if audioManager.isRecording {
            newState = .recording
        } else if !audioManager.detectedKeyword.isEmpty {
            newState = .keywordDetected
        } else if audioManager.isListening {
            newState = .listening
        } else {
            newState = .listening // Default fallback
        }

        recordingState = newState
    }

    private func startListening() {
        audioManager.startListening()

        // Update state after a short delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.updateRecordingState()
        }
    }

    private func stopRecording() {
        if let audioURL = audioManager.stopRecording() {
            // Generate a title based on detected keyword or transcription
            noteTitle = generateNoteTitle()

            // Find matching keywords
            selectedKeywords = findMatchingKeywords()

            // Auto-save if there's transcribed text (with or without keywords)
            if autoSaveEnabled && !audioManager.transcribedText.isEmpty {
                autoSaveNote(audioURL: audioURL)
            } else if !audioManager.transcribedText.isEmpty {
                // Show save dialog if auto-save is disabled but there's content
                showingSaveDialog = true
            } else {
                // No content to save, just close
                isPresented = false
            }
        } else {
            isPresented = false
        }
    }

    private func autoSaveNote(audioURL: URL) {
        // Check user preference for recording audio
        let shouldIncludeAudio = preferences?.recordAudioWhenCreatingNote ?? true
        let finalAudioURL = shouldIncludeAudio ? audioURL.path : nil

        // Use improved DataManager method with better relationship handling
        let note = createNoteWithSafeRelationships(
            title: noteTitle,
            content: audioManager.transcribedText,
            audioURL: finalAudioURL,
            keywords: selectedKeywords
        )

        // Set the recording duration if audio is included
        if shouldIncludeAudio && audioManager.recordingDuration > 0 {
            note.duration = audioManager.recordingDuration
        }

        // Close the recording view
        isPresented = false
    }

    private func createNoteWithSafeRelationships(title: String, content: String, audioURL: String? = nil, keywords: [Keyword]) -> Note {
        // Create note with keywords directly - let SwiftData manage the relationships
        let note = Note(title: title, content: content, audioURL: audioURL, keywords: keywords)
        modelContext.insert(note)

        // Update usage counts only
        for keyword in keywords {
            keyword.incrementUsage()
        }

        // Save everything
        do {
            try modelContext.save()
        } catch {
            // Log error for debugging but don't show to user
            print("Error saving note: \(error)")
        }

        return note
    }

    private func generateNoteTitle() -> String {
        if !audioManager.detectedKeyword.isEmpty {
            return "\(audioManager.detectedKeyword.capitalized) Note"
        } else if !audioManager.transcribedText.isEmpty {
            let words = audioManager.transcribedText.components(separatedBy: .whitespaces)
            let firstWords = Array(words.prefix(3)).joined(separator: " ")
            return firstWords.isEmpty ? "Voice Note" : firstWords
        } else {
            return "Voice Note"
        }
    }

    private func findMatchingKeywords() -> [Keyword] {
        let detectedKeyword = audioManager.detectedKeyword.lowercased()

        // ONLY use the keyword that triggered the recording
        // Do NOT search for additional keywords in the transcription content
        if !detectedKeyword.isEmpty {
            let triggerKeyword = keywords.first { keyword in
                keyword.isEnabled && keyword.text.lowercased() == detectedKeyword
            }

            if let triggerKeyword = triggerKeyword {
                return [triggerKeyword]
            }
        }

        return []
    }
}

// Using unified RecordingState from RecordingCoordinator

extension Array {
    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}

struct SaveNoteView: View {
    @Binding var title: String
    let content: String
    let audioURL: String?
    let duration: TimeInterval
    @Binding var selectedKeywords: [Keyword]
    let availableKeywords: [Keyword]
    let dataManager: DataManager
    let onSave: () -> Void

    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @StateObject private var themeManager = ThemeManager.shared
    @Query private var userPreferences: [UserPreferences]

    private var preferences: UserPreferences? {
        userPreferences.first
    }

    var body: some View {
        NavigationView {
            ZStack {
                // Apply global background theme
                Group {
                    if themeManager.isGradientTheme,
                       let gradient = themeManager.applyGradient() {
                        gradient.ignoresSafeArea()
                    } else {
                        themeManager.applyBackground().ignoresSafeArea()
                    }
                }

                VStack(spacing: 20) {
                TextField("Note title", text: $title)
                    .padding(12)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.gray.opacity(0.2))
                    )
                    .foregroundColor(.white)

                Text("Content Preview")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity, alignment: .leading)

                ScrollView {
                    Text(content)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding()
                        .background(Color.gray.opacity(0.2))
                        .cornerRadius(8)
                }
                .frame(maxHeight: 200)

                if !availableKeywords.isEmpty {
                    Text("Keywords")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    LazyVGrid(columns: [GridItem(.adaptive(minimum: 100))], spacing: 10) {
                        ForEach(availableKeywords, id: \.id) { keyword in
                            Button(action: {
                                if selectedKeywords.contains(where: { $0.id == keyword.id }) {
                                    selectedKeywords.removeAll { $0.id == keyword.id }
                                } else {
                                    selectedKeywords.append(keyword)
                                }
                            }) {
                                Text(keyword.text)
                                    .font(.caption)
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 6)
                                    .background(
                                        RoundedRectangle(cornerRadius: 15)
                                            .fill(selectedKeywords.contains(where: { $0.id == keyword.id }) ?
                                                  Color(hex: keyword.color) : Color.gray.opacity(0.2))
                                    )
                                    .foregroundColor(selectedKeywords.contains(where: { $0.id == keyword.id }) ? .white : .white)
                            }
                        }
                    }
                }

                // Audio recording status
                HStack {
                    Image(systemName: (preferences?.recordAudioWhenCreatingNote ?? true) ? "mic.fill" : "mic.slash.fill")
                        .foregroundColor((preferences?.recordAudioWhenCreatingNote ?? true) ? .green : .red)

                    Text((preferences?.recordAudioWhenCreatingNote ?? true) ?
                         "Audio will be saved with this note" :
                         "Audio recording is disabled in settings")
                        .font(.caption)
                        .foregroundColor(.gray)

                    Spacer()
                }
                .padding(.top, 10)

                Spacer()
                }
                .padding()
            }
            .navigationTitle("Save Note")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveNote()
                    }
                    .disabled(title.isEmpty)
                }
            }
        }
    }

    private func saveNote() {
        // Check user preference for recording audio
        let shouldIncludeAudio = preferences?.recordAudioWhenCreatingNote ?? true
        let finalAudioURL = shouldIncludeAudio ? audioURL : nil

        let note = dataManager.createNote(
            title: title,
            content: content,
            audioURL: finalAudioURL,
            keywords: selectedKeywords
        )

        if duration > 0 && shouldIncludeAudio {
            note.duration = duration
        }

        onSave()
        dismiss()
    }

    // MARK: - Recording State Management

    private func setupRecordingStateBindings() {
        guard let coordinator = recordingCoordinator else { return }

        coordinator.recordingState
            .sink { [weak coordinator] state in
                recordingState = state

                // Handle auto-save when recording completes
                if case .idle = state, let lastNote = coordinator?.lastCreatedNote, autoSaveEnabled {
                    // Auto-save completed - close the view
                    DispatchQueue.main.async {
                        isPresented = false
                    }
                }
            }
            .store(in: &cancellables)
    }
}

#Preview {
    RecordingView(isPresented: .constant(true))
        .modelContainer(for: [Note.self, Keyword.self, UserPreferences.self], inMemory: true)
}
