//
//  RecordingView.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI
import SwiftData
import AVFoundation
import Combine

struct RecordingView: View {
    @Binding var isPresented: Bool
    @Environment(\.modelContext) private var modelContext
    @Environment(\.serviceManager) private var serviceManager: ServiceManager

    @State private var recordingCoordinator: RecordingCoordinator?
    @State private var recordingState: RecordingState = .idle
    @State private var showingSaveDialog = false
    @State private var noteTitle = ""
    @State private var selectedKeywords: [Keyword] = []
    @State private var autoSaveEnabled = true
    @State private var refreshTimer: Timer?
    @State private var cancellables = Set<AnyCancellable>()
    
    // Audio manager for compatibility
    @State private var audioManager: VoiceRecordingService?

    @Query private var keywords: [Keyword]
    @Query private var userPreferences: [UserPreferences]
    @StateObject private var accessibilityService = AccessibilityService.shared
    @StateObject private var themeManager = ThemeManager.shared

    private var preferences: UserPreferences? {
        userPreferences.first
    }

    private var dataManager: DataManager {
        DataManager(modelContext: modelContext)
    }

    var body: some View {
        ZStack {
            // Background
            themeManager.surfaceColor
                .ignoresSafeArea()

            VStack(spacing: 30) {
                // Header
                HStack {
                    Button("Cancel") {
                        Task {
                            await recordingCoordinator?.cancelCurrentRecording()
                            isPresented = false
                        }
                    }
                    .foregroundColor(.white)

                    Spacer()

                    Text("Recording")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Spacer()

                    Button("Done") {
                        Task {
                            try? await recordingCoordinator?.stopManualRecording()
                            isPresented = false
                        }
                    }
                    .foregroundColor(.white)
                }
                .padding()

                Spacer()

                // Recording Status
                VStack(spacing: 20) {
                    // Status Text
                    Text(recordingState.displayText)
                        .font(.title)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)

                    // Timer
                    if case .recording = recordingState {
                        Text("00:00")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.green)
                            .monospacedDigit()
                    }

                    // Transcription
                    if !(audioManager?.transcribedText.isEmpty ?? true) {
                        ScrollView {
                            Text(audioManager?.transcribedText ?? "")
                                .font(.body)
                                .foregroundColor(.white)
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color.black.opacity(0.3))
                                )
                        }
                        .frame(maxHeight: 200)
                    }
                }

                Spacer()

                // Control Buttons
                HStack(spacing: 40) {
                    if case .recording = recordingState {
                        Button(action: {
                            Task {
                                try? await recordingCoordinator?.stopManualRecording()
                            }
                        }) {
                            Image(systemName: "stop.fill")
                                .font(.system(size: 30))
                                .foregroundColor(.white)
                                .frame(width: 80, height: 80)
                                .background(Circle().fill(Color.red))
                        }
                    } else {
                        Button(action: {
                            Task {
                                try? await recordingCoordinator?.startManualRecording()
                            }
                        }) {
                            Image(systemName: "mic.fill")
                                .font(.system(size: 30))
                                .foregroundColor(.white)
                                .frame(width: 80, height: 80)
                                .background(Circle().fill(Color.blue))
                        }
                    }
                }
                .padding(.bottom, 50)
            }
        }
        .onAppear {
            Task {
                if let coordinator = await serviceManager.getRecordingCoordinator() as? RecordingCoordinator {
                    recordingCoordinator = coordinator
                    setupRecordingStateBindings()
                    audioManager = await serviceManager.getVoiceRecordingService() as? VoiceRecordingService
                }
            }
        }
        .onDisappear {
            Task {
                await recordingCoordinator?.cancelCurrentRecording()
            }
        }
    }
    
    private func setupRecordingStateBindings() {
        guard let coordinator = recordingCoordinator else { return }
        
        coordinator.recordingState
            .receive(on: DispatchQueue.main)
            .sink { state in
                recordingState = state
            }
            .store(in: &cancellables)
    }
}

#Preview {
    RecordingView(isPresented: .constant(true))
        .modelContainer(for: [Note.self, Keyword.self, UserPreferences.self], inMemory: true)
}
