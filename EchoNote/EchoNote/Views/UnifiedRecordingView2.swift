//
//  UnifiedRecordingView2.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import SwiftUI
import SwiftData

struct UnifiedRecordingView2: View {
    @Binding var isPresented: Bool
    
    // Services
    @StateObject private var recordingService: UnifiedRecordingService
    @StateObject private var themeManager = ThemeManager.shared
    
    // Data
    @Query(sort: \Keyword.text) private var keywords: [Keyword]
    @Query(sort: \UserPreferences.createdDate) private var userPreferences: [UserPreferences]
    
    // State
    @State private var showingSettings = false
    @State private var breathingAnimation = false
    
    private var preferences: UserPreferences? {
        userPreferences.first
    }
    
    init(isPresented: Binding<Bool>, dataManager: DataManager) {
        self._isPresented = isPresented
        self._recordingService = StateObject(wrappedValue: UnifiedRecordingService(dataManager: dataManager))
    }
    
    var body: some View {
        ZStack {
            // Background
            themeManager.applyBackground()
                .ignoresSafeArea()
            
            VStack(spacing: 30) {
                // Top bar
                topBar
                
                Spacer()
                
                // Main content based on state
                mainContent
                
                Spacer()
                
                // Control buttons
                controlButtons
                
                Spacer()
            }
            .padding(.horizontal, 30)
        }
        .onAppear {
            setupView()
        }
        .onDisappear {
            cleanup()
        }
        .onChange(of: recordingService.currentState) { _, newState in
            handleStateChange(newState)
        }
    }
    
    // MARK: - Top Bar
    
    private var topBar: some View {
        HStack {
            Button("Cancel") {
                recordingService.stopCurrentRecording()
                isPresented = false
            }
            .foregroundColor(.white)
            .font(.system(size: 16, weight: .medium))
            
            Spacer()
            
            Button(action: { showingSettings = true }) {
                Image(systemName: "gear")
                    .foregroundColor(.white)
                    .font(.system(size: 18))
            }
        }
        .padding(.top, 10)
    }
    
    // MARK: - Main Content
    
    @ViewBuilder
    private var mainContent: some View {
        switch recordingService.currentState {
        case .idle:
            idleView
        case .listeningForKeywords:
            listeningView
        case .recordingAfterKeyword, .recordingManual:
            recordingView
        case .processing:
            processingView
        case .cooldown:
            cooldownView
        }
    }
    
    private var idleView: some View {
        VStack(spacing: 20) {
            Text("Ready to Record")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text("Press the headphone button to start manual recording\nor enable keyword listening below")
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
        }
    }
    
    private var listeningView: some View {
        VStack(spacing: 30) {
            // Animated headphone icon
            Image(systemName: "headphones")
                .font(.system(size: 80))
                .foregroundColor(.yellow)
                .scaleEffect(breathingAnimation ? 1.1 : 1.0)
                .opacity(breathingAnimation ? 0.8 : 1.0)
                .animation(
                    .easeInOut(duration: 1.5).repeatForever(autoreverses: true),
                    value: breathingAnimation
                )
            
            Text("Listening for Keywords")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text("Say: \(recordingService.registeredKeywords.joined(separator: ", "))")
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
            
            // Audio level indicator
            audioLevelIndicator
        }
        .onAppear {
            breathingAnimation = true
        }
    }
    
    private var recordingView: some View {
        VStack(spacing: 30) {
            // Recording indicator
            recordingIndicator
            
            // Duration
            if let session = recordingService.currentSession {
                Text(formatDuration(session.duration))
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .monospacedDigit()
            }
            
            // Transcription
            if let session = recordingService.currentSession, !session.transcribedText.isEmpty {
                transcriptionView(session.transcribedText)
            }
            
            // Waveform
            waveformView
        }
    }
    
    private var processingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
                .tint(.white)
            
            Text("Processing Recording...")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
        }
    }
    
    private var cooldownView: some View {
        VStack(spacing: 20) {
            Image(systemName: "clock")
                .font(.system(size: 60))
                .foregroundColor(.orange)
            
            Text("Cooldown Period")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text("Please wait before next recording")
                .font(.body)
                .foregroundColor(.gray)
        }
    }
    
    // MARK: - Recording Indicator
    
    private var recordingIndicator: some View {
        ZStack {
            // Pulsing circle
            Circle()
                .fill(Color.red.opacity(0.3))
                .frame(width: 120, height: 120)
                .scaleEffect(breathingAnimation ? 1.2 : 1.0)
                .animation(
                    .easeInOut(duration: 1.0).repeatForever(autoreverses: true),
                    value: breathingAnimation
                )
            
            // Recording dot
            Circle()
                .fill(Color.red)
                .frame(width: 20, height: 20)
            
            // Recording text
            Text("REC")
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .offset(y: 25)
        }
    }
    
    // MARK: - Audio Level Indicator
    
    private var audioLevelIndicator: some View {
        HStack(spacing: 2) {
            ForEach(0..<20, id: \.self) { index in
                RoundedRectangle(cornerRadius: 1)
                    .fill(Color.green)
                    .frame(width: 3, height: CGFloat(index + 1) * 2)
                    .opacity(recordingService.audioLevel * 20 > Float(index) ? 1.0 : 0.3)
            }
        }
        .frame(height: 40)
    }
    
    // MARK: - Waveform View
    
    private var waveformView: some View {
        HStack(spacing: 2) {
            if let session = recordingService.currentSession {
                ForEach(Array(session.audioLevels.enumerated()), id: \.offset) { index, level in
                    RoundedRectangle(cornerRadius: 1)
                        .fill(Color.blue)
                        .frame(width: 3, height: max(2, CGFloat(level) * 50))
                        .animation(.easeInOut(duration: 0.1), value: level)
                }
            }
        }
        .frame(height: 50)
    }
    
    // MARK: - Transcription View
    
    private func transcriptionView(_ text: String) -> some View {
        ScrollView {
            Text(text)
                .font(.body)
                .foregroundColor(.white)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.black.opacity(0.3))
                )
        }
        .frame(maxHeight: 120)
    }

    // MARK: - Control Buttons

    @ViewBuilder
    private var controlButtons: some View {
        switch recordingService.currentState {
        case .idle:
            idleButtons
        case .listeningForKeywords:
            listeningButtons
        case .recordingAfterKeyword, .recordingManual:
            recordingButtons
        case .processing, .cooldown:
            EmptyView()
        }
    }

    private var idleButtons: some View {
        VStack(spacing: 20) {
            // Manual recording button
            Button(action: {
                Task {
                    await recordingService.startManualRecording()
                }
            }) {
                HStack {
                    Image(systemName: "mic.fill")
                    Text("Start Recording")
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.blue)
                )
            }

            // Keyword listening toggle
            Button(action: {
                Task {
                    if recordingService.isListeningForKeywords {
                        recordingService.stopKeywordListening()
                    } else {
                        await recordingService.startKeywordListening()
                    }
                }
            }) {
                HStack {
                    Image(systemName: "headphones")
                    Text("Enable Keyword Listening")
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.green)
                )
            }
        }
    }

    private var listeningButtons: some View {
        VStack(spacing: 20) {
            // Stop listening button
            Button(action: {
                recordingService.stopKeywordListening()
            }) {
                HStack {
                    Image(systemName: "stop.fill")
                    Text("Stop Listening")
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.red)
                )
            }

            // Manual recording button (still available)
            Button(action: {
                Task {
                    await recordingService.startManualRecording()
                }
            }) {
                HStack {
                    Image(systemName: "mic.fill")
                    Text("Manual Recording")
                }
                .font(.subheadline)
                .foregroundColor(.white)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.blue.opacity(0.7))
                )
            }
        }
    }

    private var recordingButtons: some View {
        Button(action: {
            recordingService.stopCurrentRecording()
        }) {
            HStack {
                Image(systemName: "stop.fill")
                Text("Stop Recording")
            }
            .font(.headline)
            .foregroundColor(.white)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.red)
            )
        }
    }

    // MARK: - Helper Methods

    private func setupView() {
        // Update keywords
        recordingService.updateKeywords(Array(keywords))

        // Start breathing animation
        breathingAnimation = true

        // Auto-start keyword listening if enabled in preferences
        if preferences?.backgroundMonitorEnabled == true {
            Task {
                await recordingService.startKeywordListening()
            }
        }
    }

    private func cleanup() {
        recordingService.stopKeywordListening()
        recordingService.stopCurrentRecording()
    }

    private func handleStateChange(_ newState: RecordingSessionState) {
        switch newState {
        case .recordingAfterKeyword, .recordingManual:
            // Trigger haptic feedback when recording starts
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
        case .processing:
            // Show processing state
            break
        case .cooldown:
            // Show cooldown state
            break
        default:
            break
        }
    }

    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}

// MARK: - Preview

#Preview {
    UnifiedRecordingView2(
        isPresented: .constant(true),
        dataManager: DataManager(modelContext: ModelContext(try! ModelContainer(for: Note.self, Keyword.self, UserPreferences.self)))
    )
    .modelContainer(for: [Note.self, Keyword.self, UserPreferences.self], inMemory: true)
}
