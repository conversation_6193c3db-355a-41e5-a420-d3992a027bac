//
//  ProfileView.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI
import SwiftData
import PhotosUI

struct ProfileView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var userPreferences: [UserPreferences]
    @StateObject private var themeManager = ThemeManager.shared

    @State private var showingBackgroundThemeSelector = false
    @State private var showingAppearanceModeSelector = false
    @State private var showingPhotoPicker = false
    @State private var selectedPhotoItem: PhotosPickerItem?
    @State private var profileImage: UIImage?
    @State private var showingSiriShortcuts = false
    
    private var dataManager: DataManager {
        DataManager(modelContext: modelContext)
    }
    
    private var preferences: UserPreferences {
        if let existing = userPreferences.first {
            return existing
        } else {
            return dataManager.createOrUpdateUserPreferences()
        }
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                themeManager.applyBackground().ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Header
                    headerView
                    
                    ScrollView {
                        VStack(spacing: 20) {
                            // Profile section
                            profileSection
                            
                            // App preferences
                            appPreferencesSection
                            
                            // Recording options
                            recordingOptionsSection

                            // Privacy & Security
                            privacySection

                            // Subscription & Account
                            subscriptionSection
                            
                            Spacer(minLength: 100) // Space for tab bar
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                    }
                }
            }
        }
        .navigationBarHidden(true)

        .fullScreenCover(isPresented: $showingBackgroundThemeSelector) {
            BackgroundThemeSelector()
        }
        .fullScreenCover(isPresented: $showingAppearanceModeSelector) {
            AppearanceModeSelector()
        }
        .fullScreenCover(isPresented: $showingSiriShortcuts) {
            SiriShortcutsView()
        }
        .photosPicker(isPresented: $showingPhotoPicker, selection: $selectedPhotoItem, matching: .images)
        .onChange(of: selectedPhotoItem) { oldValue, newValue in
            loadSelectedPhoto()
        }
        .onAppear {
            loadSavedProfileImage()

            // Initialize migration service
            Task {
                await BackgroundMonitoringMigrationService.shared.initialize(with: dataManager)

                // Start monitoring if enabled and not already monitoring
                if false && !BackgroundMonitoringMigrationService.shared.isMonitoring { // Background monitoring disabled
                    print("🔄 ProfileView: Restarting background monitoring (was enabled but not running)")
                    await BackgroundMonitoringMigrationService.shared.startMonitoring()
                }
            }
        }
    }
    
    private var headerView: some View {
        HStack {
            Text("Profile")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Spacer()
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
    }
    
    private var profileSection: some View {
        VStack(spacing: 15) {
            // Profile image with photo picker
            Button(action: {
                showingPhotoPicker = true
            }) {
                ZStack {
                    Circle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 80, height: 80)

                    if let profileImage = profileImage {
                        Image(uiImage: profileImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 80, height: 80)
                            .clipShape(Circle())
                    } else {
                        Image(systemName: "person.fill")
                            .font(.system(size: 40))
                            .foregroundColor(.gray)
                    }

                    // Camera overlay indicator
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            Image(systemName: "camera.fill")
                                .font(.system(size: 12))
                                .foregroundColor(.white)
                                .padding(6)
                                .background(Circle().fill(Color.black.opacity(0.6)))
                                .offset(x: -5, y: -5)
                        }
                    }
                }
            }
            .buttonStyle(PlainButtonStyle())
            .accessibilityLabel("Profile photo")
            .accessibilityHint("Tap to change profile photo")
            
            Text("EchoNote User")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Button("Edit Profile") {
                // Edit profile action
            }
            .foregroundColor(.green)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.gray.opacity(0.1))
        )
    }
    
    private var appPreferencesSection: some View {
        VStack(alignment: .leading, spacing: 0) {
            sectionHeader("App Preferences")
            
            VStack(spacing: 0) {
                Button(action: {
                    showingAppearanceModeSelector = true
                }) {
                    SettingsRowContent(
                        icon: "moon.fill",
                        title: "Appearance",
                        subtitle: themeManager.appearanceMode.displayName
                    )
                }
                .buttonStyle(PlainButtonStyle())
                
                Divider().background(Color.gray.opacity(0.3))
                
                SettingsRow(
                    icon: "iphone.radiowaves.left.and.right",
                    title: "Haptic Feedback",
                    subtitle: preferences.hapticFeedbackEnabled ? "On" : "Off",
                    action: {
                        preferences.hapticFeedbackEnabled.toggle()
                        dataManager.updateUserPreferences(preferences)
                    }
                )
                
                Divider().background(Color.gray.opacity(0.3))
                
                SettingsRow(
                    icon: "headphones.circle.fill",
                    title: "Headphone Animation",
                    subtitle: "On", // Default enabled
                    action: {
                        // Animation toggle functionality disabled
                        dataManager.updateUserPreferences(preferences)
                    }
                )

                Divider().background(Color.gray.opacity(0.3))

                // Breathing Speed Setting
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "waveform.path")
                            .foregroundColor(.green)
                            .frame(width: 24)

                        VStack(alignment: .leading, spacing: 2) {
                            Text("Breathing Speed")
                                .font(.body)
                                .foregroundColor(.white)
                            Text(breathingSpeedDescription(1.0)) // Default speed
                                .font(.caption)
                                .foregroundColor(.gray)
                        }

                        Spacer()
                    }

                    // Speed Slider
                    VStack(spacing: 8) {
                        HStack {
                            Text("Slow")
                                .font(.caption)
                                .foregroundColor(.gray)
                            Spacer()
                            Text("Fast")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }

                        Slider(
                            value: Binding(
                                get: { preferences.headphoneBreathingSpeed },
                                set: { newValue in
                                    preferences.headphoneBreathingSpeed = newValue
                                    dataManager.updateUserPreferences(preferences)
                                }
                            ),
                            in: 0.5...2.0,
                            step: 0.1
                        )
                        .accentColor(.green)
                    }
                }
                .padding(16)
                .background(Color.clear)

                Divider().background(Color.gray.opacity(0.3))
                
                SettingsRow(
                    icon: "chart.bar.fill",
                    title: "Show Notes Overview",
                    subtitle: preferences.showNotesOverview ? "On" : "Off",
                    action: {
                        preferences.showNotesOverview.toggle()
                        dataManager.updateUserPreferences(preferences)
                    }
                )

                Divider().background(Color.gray.opacity(0.3))

                Button(action: {
                    showingBackgroundThemeSelector = true
                }) {
                    SettingsRowContent(
                        icon: "paintbrush.fill",
                        title: "Background Theme",
                        subtitle: "Customizable"
                    )
                }
                .buttonStyle(PlainButtonStyle())

                Divider().background(Color.gray.opacity(0.3))

                SettingsRow(
                    icon: "globe",
                    title: "Language",
                    subtitle: "English",
                    action: {}
                )
            }
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.1))
            )
        }
    }
    
    private var recordingOptionsSection: some View {
        VStack(alignment: .leading, spacing: 0) {
            sectionHeader("Recording Options")
            
            VStack(spacing: 0) {
                // Auto Stop Duration Setting
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "stop.circle.fill")
                            .foregroundColor(.red)
                            .frame(width: 24)

                        VStack(alignment: .leading, spacing: 2) {
                            Text("Auto Stop")
                                .font(.body)
                                .foregroundColor(.white)
                            Text(formatAutoStopDuration(Double(preferences.autoStopDuration)))
                                .font(.caption)
                                .foregroundColor(.gray)
                        }

                        Spacer()
                    }

                    // Duration Slider
                    VStack(spacing: 8) {
                        HStack {
                            Text("1s")
                                .font(.caption)
                                .foregroundColor(.gray)
                            Spacer()
                            Text("30s")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }

                        Slider(
                            value: Binding(
                                get: { preferences.autoStopDuration },
                                set: { newValue in
                                    preferences.autoStopDuration = newValue
                                    dataManager.updateUserPreferences(preferences)
                                }
                            ),
                            in: 1.0...30.0,
                            step: 1.0
                        )
                        .accentColor(.red)
                    }
                }
                .padding(16)
                .background(Color.clear)

                Divider().background(Color.gray.opacity(0.3))
                
                // Maximum Recording Duration Setting
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "timer")
                            .foregroundColor(.orange)
                            .frame(width: 24)

                        VStack(alignment: .leading, spacing: 2) {
                            Text("Maximum Recording Duration")
                                .font(.body)
                                .foregroundColor(.white)
                            Text(formatMaxDuration(preferences.maxRecordingDuration))
                                .font(.caption)
                                .foregroundColor(.gray)
                        }

                        Spacer()
                    }

                    // Duration Slider
                    VStack(spacing: 8) {
                        HStack {
                            Text("1 min")
                                .font(.caption)
                                .foregroundColor(.gray)
                            Spacer()
                            Text("30 min")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }

                        Slider(
                            value: Binding(
                                get: { preferences.maxRecordingDuration / 60.0 },
                                set: { newValue in
                                    preferences.maxRecordingDuration = newValue * 60.0
                                    dataManager.updateUserPreferences(preferences)
                                }
                            ),
                            in: 1.0...30.0,
                            step: 1.0
                        )
                        .accentColor(.orange)
                    }
                }
                .padding(16)
                .background(Color.clear)
                
                Divider().background(Color.gray.opacity(0.3))

                // Background Monitor Setting
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "ear.and.waveform")
                            .foregroundColor(.purple)
                            .frame(width: 24)

                        VStack(alignment: .leading, spacing: 2) {
                            Text("Background Monitor")
                                .font(.body)
                                .foregroundColor(.white)
                            Text(backgroundMonitorDescription(preferences.backgroundMonitorEnabled))
                                .font(.caption)
                                .foregroundColor(.gray)
                        }

                        Spacer()

                        Toggle("", isOn: Binding(
                            get: { preferences.backgroundMonitorEnabled },
                            set: { newValue in
                                preferences.backgroundMonitorEnabled = newValue
                                dataManager.updateUserPreferences(preferences)
                                Task {
                                    if newValue {
                                        await startBackgroundMonitoring()
                                    } else {
                                        await stopBackgroundMonitoring()
                                    }
                                }
                            }
                        ))
                        .toggleStyle(SwitchToggleStyle(tint: .purple))
                        .scaleEffect(0.8)
                    }

                    if preferences.backgroundMonitorEnabled {
                        VStack(alignment: .leading, spacing: 8) {
                            // Debug: Test keyword detection
                            Button("🎤 Test Keyword Detection") {
                                Task {
                                    // Simulate keyword detection through the new architecture
                                    if let coordinator = await ServiceManager.shared.getRecordingCoordinator() {
                                        try? await coordinator.handleKeywordDetection(keyword: "test")
                                    }
                                }
                            }
                            .foregroundColor(.green)
                            .font(.caption)
                            .padding(.bottom, 4)

                            Text("⚠️ Privacy Notice")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.yellow)

                            Text("Background monitoring will listen for keywords and automatically create notes. Audio is processed locally and not stored unless keywords are detected.")
                                .font(.caption)
                                .foregroundColor(.gray)
                                .fixedSize(horizontal: false, vertical: true)
                        }
                        .padding(.top, 8)
                    }
                }
                .padding(16)
                .background(Color.clear)

                Divider().background(Color.gray.opacity(0.3))

                // Record Audio When Creating Note Setting
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "mic.circle.fill")
                            .foregroundColor(.blue)
                            .frame(width: 24)

                        VStack(alignment: .leading, spacing: 2) {
                            Text("Record Audio for New Notes")
                                .font(.body)
                                .foregroundColor(.white)
                            Text(recordAudioDescription(preferences.recordAudioWhenCreatingNote))
                                .font(.caption)
                                .foregroundColor(.gray)
                        }

                        Spacer()

                        Toggle("", isOn: Binding(
                            get: { preferences.recordAudioWhenCreatingNote },
                            set: { newValue in
                                preferences.recordAudioWhenCreatingNote = newValue
                                dataManager.updateUserPreferences(preferences)
                            }
                        ))
                        .toggleStyle(SwitchToggleStyle(tint: .blue))
                        .scaleEffect(0.8)
                    }

                    if !preferences.recordAudioWhenCreatingNote {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("ℹ️ Note")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.blue)

                            Text("When disabled, new notes will only contain text content. You can still manually record audio by using the recording feature.")
                                .font(.caption)
                                .foregroundColor(.gray)
                                .fixedSize(horizontal: false, vertical: true)
                        }
                        .padding(.top, 8)
                    }
                }
                .padding(16)
                .background(Color.clear)

                Divider().background(Color.gray.opacity(0.3))

                SettingsRow(
                    icon: "shortcuts",
                    title: "Add to Siri Shortcuts",
                    subtitle: "Voice commands",
                    hasChevron: true,
                    action: {
                        showingSiriShortcuts = true
                    }
                )
            }
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.1))
            )
        }
    }

    private var privacySection: some View {
        VStack(alignment: .leading, spacing: 0) {
            sectionHeader("Privacy & Security")

            VStack(spacing: 0) {
                NavigationLink(destination: PrivacySettingsView()) {
                    SettingsRow(
                        icon: "lock.shield.fill",
                        title: "Privacy Settings",
                        subtitle: "Manage permissions and data privacy",
                        hasChevron: true
                    )
                }
                .buttonStyle(PlainButtonStyle())

                Divider()
                    .background(Color.gray.opacity(0.3))
                    .padding(.leading, 50)

                SettingsRow(
                    icon: "key.fill",
                    title: "App Permissions",
                    subtitle: "Microphone, Speech Recognition",
                    hasChevron: true,
                    action: {
                        // Open system settings
                        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                            UIApplication.shared.open(settingsUrl)
                        }
                    }
                )

                Divider()
                    .background(Color.gray.opacity(0.3))
                    .padding(.leading, 50)

                SettingsRow(
                    icon: "shield.checkerboard",
                    title: "Data Security",
                    subtitle: "Local encryption enabled",
                    hasChevron: false
                )
            }
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.gray.opacity(0.1))
            )
        }
    }

    private var subscriptionSection: some View {
        VStack(alignment: .leading, spacing: 0) {
            sectionHeader("Subscription & Account")
            
            VStack(spacing: 0) {
                SettingsRow(
                    icon: "crown.fill",
                    title: "View Subscription Status",
                    subtitle: "Free Plan",
                    action: {}
                )
                
                Divider().background(Color.gray.opacity(0.3))
                
                SettingsRow(
                    icon: "creditcard.fill",
                    title: "Manage Subscription",
                    subtitle: "Billing & plans",
                    action: {}
                )
                
                Divider().background(Color.gray.opacity(0.3))
                
                SettingsRow(
                    icon: "gift.fill",
                    title: "Redeem Promo Code",
                    subtitle: "Enter code",
                    action: {}
                )
                
                Divider().background(Color.gray.opacity(0.3))
                
                SettingsRow(
                    icon: "person.2.fill",
                    title: "Referral & Invite Friends",
                    subtitle: "Share the app",
                    action: {}
                )
            }
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.1))
            )
        }
    }
    
    private func sectionHeader(_ title: String) -> some View {
        Text(title)
            .font(.headline)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .padding(.bottom, 10)
    }

    // MARK: - Photo Handling

    private func loadSelectedPhoto() {
        guard let selectedPhotoItem = selectedPhotoItem else { return }

        Task {
            do {
                if let data = try await selectedPhotoItem.loadTransferable(type: Data.self),
                   let uiImage = UIImage(data: data) {

                    // Resize image for better performance
                    let resizedImage = resizeImage(uiImage, to: CGSize(width: 200, height: 200))

                    await MainActor.run {
                        self.profileImage = resizedImage
                        saveProfileImage(resizedImage)
                    }
                }
            } catch {
                LoggingService.shared.error("Failed to load selected photo: \(error)", category: .ui)
            }
        }
    }

    private func resizeImage(_ image: UIImage, to size: CGSize) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: size))
        }
    }

    private func saveProfileImage(_ image: UIImage) {
        guard let data = image.jpegData(compressionQuality: 0.8) else { return }

        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let imagePath = documentsPath.appendingPathComponent("profile_image.jpg")

        do {
            try data.write(to: imagePath)
            UserDefaults.standard.set(imagePath.path, forKey: "ProfileImagePath")
            LoggingService.shared.info("Profile image saved successfully", category: .ui)
        } catch {
            LoggingService.shared.error("Failed to save profile image: \(error)", category: .ui)
        }
    }

    private func loadSavedProfileImage() {
        guard let imagePath = UserDefaults.standard.string(forKey: "ProfileImagePath"),
              let data = try? Data(contentsOf: URL(fileURLWithPath: imagePath)),
              let image = UIImage(data: data) else { return }

        self.profileImage = image
    }

    private func breathingSpeedDescription(_ speed: Double) -> String {
        switch speed {
        case 0.5..<0.8:
            return "Very Slow"
        case 0.8..<1.2:
            return "Normal"
        case 1.2..<1.6:
            return "Fast"
        case 1.6...2.0:
            return "Very Fast"
        default:
            return "Normal"
        }
    }

    private func formatAutoStopDuration(_ duration: Double) -> String {
        let seconds = Int(duration)
        if seconds == 1 {
            return "1 second of silence"
        } else {
            return "\(seconds) seconds of silence"
        }
    }

    private func formatMaxDuration(_ duration: Double) -> String {
        let minutes = Int(duration / 60)
        if minutes == 1 {
            return "1 minute maximum"
        } else {
            return "\(minutes) minutes maximum"
        }
    }

    private func backgroundMonitorDescription(_ enabled: Bool) -> String {
        if enabled {
            return "Listening for keywords"
        } else {
            return "Tap to enable automatic note creation"
        }
    }

    private func recordAudioDescription(_ enabled: Bool) -> String {
        if enabled {
            return "Audio will be recorded with new notes"
        } else {
            return "Only text content will be saved"
        }
    }

    private func startBackgroundMonitoring() async {
        print("🚀 Starting background monitoring from ProfileView")

        // Get keywords from database
        let keywords = try? await dataManager.fetchKeywords()
        let keywordTexts = keywords?.map { $0.text } ?? []

        // Start monitoring with migration service
        await BackgroundMonitoringMigrationService.shared.startMonitoring(keywords: keywordTexts)
        print("✅ Background monitoring start command sent")
    }

    private func stopBackgroundMonitoring() async {
        // Stop background monitoring service
        await BackgroundMonitoringMigrationService.shared.stopMonitoring()
        print("🛑 Background monitoring stopped")
    }
}

struct SettingsRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let hasChevron: Bool
    let action: () -> Void

    init(icon: String, title: String, subtitle: String, hasChevron: Bool = true, action: @escaping () -> Void = {}) {
        self.icon = icon
        self.title = title
        self.subtitle = subtitle
        self.hasChevron = hasChevron
        self.action = action
    }

    var body: some View {
        Button(action: action) {
            HStack(spacing: 15) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(.green)
                    .frame(width: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.white)

                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.gray)
                }

                Spacer()

                if hasChevron {
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct SettingsRowContent: View {
    let icon: String
    let title: String
    let subtitle: String

    var body: some View {
        HStack(spacing: 15) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.green)
                .frame(width: 24)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.white)

                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.gray)
            }

            Spacer()

            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.gray)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
    }
}





#Preview {
    ProfileView()
        .modelContainer(for: [Note.self, Keyword.self, UserPreferences.self], inMemory: true)
}
