//
//  UnifiedRecordingService.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import AVFoundation
import Speech
import SwiftUI

// MARK: - Recording Session State

enum RecordingSessionState {
    case idle                    // Not recording, not listening
    case listeningForKeywords   // Listening for keywords
    case recordingAfterKeyword  // Recording after keyword detected
    case recordingManual        // Manual recording (headphone button)
    case processing             // Processing and saving note
    case cooldown              // Cooldown period after recording
}

// MARK: - Recording Trigger

enum RecordingTrigger {
    case keyword(String)        // Triggered by keyword detection
    case manual                 // Triggered by headphone button
}

// MARK: - Recording Session

class RecordingSession: ObservableObject {
    let id = UUID()
    let trigger: RecordingTrigger
    let startTime = Date()
    
    @Published var transcribedText = ""
    @Published var detectedKeywords: [String] = []
    @Published var duration: TimeInterval = 0
    @Published var audioLevels: [Float] = []
    @Published var isActive = true
    
    var audioURL: URL?
    
    init(trigger: RecordingTrigger) {
        self.trigger = trigger
    }
    
    func getKeywordForNote() -> String? {
        switch trigger {
        case .keyword(let keyword):
            return keyword
        case .manual:
            return detectedKeywords.first
        }
    }
}

// MARK: - Unified Recording Service

@MainActor
class UnifiedRecordingService: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var currentState: RecordingSessionState = .idle
    @Published var currentSession: RecordingSession?
    @Published var isListeningForKeywords = false
    @Published var audioLevel: Float = 0.0
    
    // MARK: - Private Properties
    
    private var audioEngine = AVAudioEngine()
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var audioRecorder: AVAudioRecorder?
    
    // Configuration
    @Published var registeredKeywords: [String] = []
    private var autoStopDuration: TimeInterval = 5.0
    private var maxRecordingDuration: TimeInterval = 300.0
    private var cooldownPeriod: TimeInterval = 2.0
    private var confidenceThreshold: Float = 0.1  // Lower threshold for better detection
    
    // Timers
    private var sessionTimer: Timer?
    private var silenceTimer: Timer?
    private var cooldownTimer: Timer?
    private var levelTimer: Timer?
    
    // State tracking
    private var lastSpeechTime = Date()
    private var cooldownEndTime: Date?
    private var accumulatedText = ""
    
    // Services
    private let dataManager: DataManager
    private let keywordManager: KeywordManager
    
    // MARK: - Initialization
    
    init(dataManager: DataManager) {
        self.dataManager = dataManager
        // We'll initialize KeywordManager later when we have access to modelContext
        self.keywordManager = KeywordManager(modelContext: dataManager.getModelContext(), dataManager: dataManager)
        
        super.init()
        
        setupSpeechRecognizer()
        setupAudioSession()
        loadConfiguration()
        
        // Load keywords
        Task {
            await loadKeywords()
        }
    }
    
    // MARK: - Public API
    
    /// Start listening for keywords (background monitoring)
    func startKeywordListening() async {
        guard currentState == .idle || currentState == .cooldown else {
            print("⚠️ Cannot start keyword listening - current state: \(currentState)")
            return
        }
        
        // Check cooldown
        if let cooldownEnd = cooldownEndTime, Date() < cooldownEnd {
            let remaining = cooldownEnd.timeIntervalSince(Date())
            print("❄️ Still in cooldown period - \(String(format: "%.1f", remaining))s remaining")
            return
        }
        
        print("🎧 Starting keyword listening...")
        currentState = .listeningForKeywords
        isListeningForKeywords = true
        
        await startSpeechRecognition(mode: .keywordDetection)
    }
    
    /// Stop listening for keywords
    func stopKeywordListening() {
        guard currentState == .listeningForKeywords else { return }
        
        print("🛑 Stopping keyword listening")
        currentState = .idle
        isListeningForKeywords = false
        
        stopSpeechRecognition()
    }
    
    /// Start manual recording (headphone button)
    func startManualRecording() async {
        guard currentState == .idle || currentState == .listeningForKeywords else {
            print("⚠️ Cannot start manual recording - current state: \(currentState)")
            return
        }
        
        print("🎤 Starting manual recording...")
        
        // Stop keyword listening if active
        if currentState == .listeningForKeywords {
            stopSpeechRecognition()
        }
        
        let session = RecordingSession(trigger: .manual)
        currentSession = session
        currentState = .recordingManual
        
        await startRecordingSession(session)
    }
    
    /// Stop current recording
    func stopCurrentRecording() {
        guard let session = currentSession else { return }
        
        print("🛑 Stopping current recording")
        finishRecordingSession(session)
    }
    
    /// Update keywords from database
    func updateKeywords(_ keywords: [Keyword]) {
        registeredKeywords = keywords.filter { $0.isEnabled }.map { $0.text }
        print("🔑 Updated keywords: \(registeredKeywords)")
    }
    
    // MARK: - Configuration
    
    private func loadConfiguration() {
        // Load user preferences
        let preferences = dataManager.getUserPreferences()
        autoStopDuration = preferences?.autoStopDuration ?? 5.0
        maxRecordingDuration = preferences?.maxRecordingDuration ?? 300.0
        
        print("⚙️ Configuration loaded:")
        print("   - Auto stop: \(autoStopDuration)s")
        print("   - Max duration: \(maxRecordingDuration)s")
        print("   - Cooldown: \(cooldownPeriod)s")
    }
    
    private func loadKeywords() async {
        keywordManager.refreshKeywords()
        let keywords = keywordManager.getEnabledKeywords()
        updateKeywords(keywords)
    }
    
    // MARK: - Audio Session Setup
    
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true)
            print("✅ Audio session configured")
        } catch {
            print("❌ Audio session setup failed: \(error)")
        }
    }
    
    private func setupSpeechRecognizer() {
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "en-US"))
        speechRecognizer?.delegate = self
        print("✅ Speech recognizer configured")
    }

    // MARK: - Speech Recognition

    private enum SpeechRecognitionMode {
        case keywordDetection    // Listening for keywords
        case recording          // Recording and transcribing
    }

    private func startSpeechRecognition(mode: SpeechRecognitionMode) async {
        guard let speechRecognizer = speechRecognizer, speechRecognizer.isAvailable else {
            print("❌ Speech recognizer unavailable")
            return
        }

        // Stop any existing recognition
        stopSpeechRecognition()

        // Configure audio session
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.record, mode: .measurement, options: [.duckOthers])
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
        } catch {
            print("❌ Audio session configuration failed: \(error)")
            return
        }

        // Create recognition request
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            print("❌ Failed to create recognition request")
            return
        }

        // Configure request based on mode
        recognitionRequest.shouldReportPartialResults = true
        recognitionRequest.requiresOnDeviceRecognition = false

        switch mode {
        case .keywordDetection:
            recognitionRequest.taskHint = .search
            print("🔍 Starting keyword detection mode")
        case .recording:
            recognitionRequest.taskHint = .dictation
            print("📝 Starting recording transcription mode")
        }

        // Setup audio engine
        let inputNode = audioEngine.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)

        // Remove existing tap
        inputNode.removeTap(onBus: 0)

        // Install new tap
        inputNode.installTap(onBus: 0, bufferSize: 2048, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)
        }

        audioEngine.prepare()

        do {
            try audioEngine.start()
            print("✅ Audio engine started for \(mode)")
        } catch {
            print("❌ Audio engine start failed: \(error)")
            return
        }

        // Start recognition task
        recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { [weak self] result, error in
            guard let self = self else { return }

            Task { @MainActor in
                if let result = result {
                    let transcription = result.bestTranscription.formattedString
                    let confidence = result.bestTranscription.segments.first?.confidence ?? 0.0

                    // Add debug info for all transcriptions
                    if !transcription.isEmpty {
                        print("🎤 Speech recognized: '\(transcription)' (confidence: \(confidence), mode: \(mode))")
                    }

                    await self.handleSpeechRecognitionResult(
                        transcription: transcription,
                        confidence: confidence,
                        mode: mode
                    )
                }

                if let error = error {
                    print("❌ Speech recognition error (\(mode)): \(error)")
                    await self.handleSpeechRecognitionError(error, mode: mode)
                }
            }
        }

        print("✅ Recognition task started for \(mode)")
    }

    private func stopSpeechRecognition() {
        print("🛑 Stopping speech recognition")

        // Stop audio engine
        if audioEngine.isRunning {
            audioEngine.stop()
        }

        // Remove tap
        audioEngine.inputNode.removeTap(onBus: 0)

        // Clean up recognition
        recognitionRequest?.endAudio()
        recognitionRequest = nil
        recognitionTask?.cancel()
        recognitionTask = nil
    }

    private func handleSpeechRecognitionResult(transcription: String, confidence: Float, mode: SpeechRecognitionMode) async {
        switch mode {
        case .keywordDetection:
            await handleKeywordDetectionResult(transcription: transcription, confidence: confidence)
        case .recording:
            await handleRecordingTranscriptionResult(transcription: transcription)
        }
    }

    private func handleKeywordDetectionResult(transcription: String, confidence: Float) async {
        guard currentState == .listeningForKeywords else {
            print("🔍 Not in listening state, current: \(currentState)")
            return
        }

        let lowercaseTranscription = transcription.lowercased()
        print("🔍 Checking transcription: '\(transcription)' (confidence: \(confidence))")
        print("🔍 Registered keywords: \(registeredKeywords)")
        print("🔍 Confidence threshold: \(confidenceThreshold)")

        // Lower confidence threshold for testing
        let effectiveThreshold: Float = 0.1 // Very low threshold for testing

        // Check for keywords
        for keyword in registeredKeywords {
            let keywordLower = keyword.lowercased()
            let contains = lowercaseTranscription.contains(keywordLower)
            let confidenceOK = confidence >= effectiveThreshold

            print("🔍 Checking '\(keyword)': contains=\(contains), confidence=\(confidenceOK) (threshold: \(effectiveThreshold))")

            if contains && confidenceOK {
                print("🎯 Keyword detected: '\(keyword)' with confidence: \(confidence)")
                await startKeywordTriggeredRecording(keyword: keyword, initialText: transcription)
                return
            }
        }

        print("🔍 No keywords detected in: '\(transcription)'")
    }

    private func handleRecordingTranscriptionResult(transcription: String) async {
        guard let session = currentSession else { return }

        // Update session with new transcription
        session.transcribedText = transcription
        accumulatedText = transcription

        // Reset silence timer if we have new speech
        if !transcription.isEmpty {
            lastSpeechTime = Date()
            resetSilenceTimer()
        }

        print("📝 Transcription updated: \(transcription.prefix(50))...")
    }

    private func handleSpeechRecognitionError(_ error: Error, mode: SpeechRecognitionMode) async {
        let nsError = error as NSError
        print("❌ Speech recognition error (\(mode)): \(error)")

        // Handle specific errors
        if nsError.domain == "kAFAssistantErrorDomain" && nsError.code == 1101 {
            print("🔄 Code 1101: iOS Simulator speech recognition limitation detected")

            // Check if running on simulator
            #if targetEnvironment(simulator)
            print("📱 Running on simulator - enabling simulator compatibility mode")
            await enableSimulatorMode(mode: mode)
            #else
            print("📱 Running on device - retrying speech recognition")
            // Retry after delay on real device
            try? await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
            if currentState == .listeningForKeywords {
                await startSpeechRecognition(mode: .keywordDetection)
            } else if currentState == .recordingAfterKeyword || currentState == .recordingManual {
                await startSpeechRecognition(mode: .recording)
            }
            #endif
        }
    }

    #if targetEnvironment(simulator)
    private func enableSimulatorMode(mode: SpeechRecognitionMode) async {
        print("🎮 Enabling simulator mode for \(mode)")

        switch mode {
        case .keywordDetection:
            // Simulate keyword detection after a delay
            print("🎮 Simulator mode: Will simulate keyword detection in 5 seconds")
            print("🎮 Available keywords: \(registeredKeywords.joined(separator: ", "))")

            // Start a timer to simulate keyword detection
            Timer.scheduledTimer(withTimeInterval: 5.0, repeats: false) { [weak self] _ in
                Task { @MainActor in
                    guard let self = self, self.currentState == .listeningForKeywords else { return }

                    // Simulate detecting the first available keyword
                    if let firstKeyword = self.registeredKeywords.first {
                        print("🎮 Simulator: Simulating detection of keyword '\(firstKeyword)'")
                        await self.handleKeywordDetectionResult(transcription: firstKeyword, confidence: 0.9)
                    }
                }
            }

        case .recording:
            // Simulate transcription during recording
            print("🎮 Simulator mode: Will simulate transcription during recording")

            // Start a timer to simulate transcription updates
            var transcriptionCounter = 0
            Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { [weak self] timer in
                Task { @MainActor in
                    guard let self = self,
                          let session = self.currentSession,
                          session.isActive else {
                        timer.invalidate()
                        return
                    }

                    transcriptionCounter += 1
                    let simulatedText = "This is simulated transcription part \(transcriptionCounter)"
                    print("🎮 Simulator: Simulating transcription: '\(simulatedText)'")
                    await self.handleRecordingTranscriptionResult(transcription: simulatedText)
                }
            }
        }
    }
    #endif

    // MARK: - Recording Session Management

    private func startKeywordTriggeredRecording(keyword: String, initialText: String) async {
        print("🎯 Starting keyword-triggered recording for: \(keyword)")

        // Stop keyword listening
        stopSpeechRecognition()

        // Create new recording session
        let session = RecordingSession(trigger: .keyword(keyword))
        session.detectedKeywords = [keyword]
        session.transcribedText = initialText

        currentSession = session
        currentState = .recordingAfterKeyword
        accumulatedText = ""  // Start fresh for recording content

        await startRecordingSession(session)
    }

    private func startRecordingSession(_ session: RecordingSession) async {
        print("🎙️ Starting recording session: \(session.id)")

        // Setup audio recorder
        setupAudioRecorder(for: session)

        // Start audio recording
        audioRecorder?.record()

        // Start speech recognition for transcription
        await startSpeechRecognition(mode: .recording)

        // Start session timers
        startSessionTimers(for: session)

        // Start audio level monitoring
        startAudioLevelMonitoring()

        // Send notification
        NotificationCenter.default.post(
            name: NSNotification.Name("RecordingSessionStarted"),
            object: nil,
            userInfo: [
                "sessionId": session.id.uuidString,
                "trigger": session.trigger,
                "keyword": session.getKeywordForNote() ?? ""
            ]
        )
    }

    private func finishRecordingSession(_ session: RecordingSession) {
        print("🏁 Finishing recording session: \(session.id)")

        // Stop all timers
        stopAllTimers()

        // Stop audio recording
        audioRecorder?.stop()
        audioRecorder = nil

        // Stop speech recognition
        stopSpeechRecognition()

        // Stop audio level monitoring
        stopAudioLevelMonitoring()

        // Mark session as inactive
        session.isActive = false
        session.duration = Date().timeIntervalSince(session.startTime)

        // Update state
        currentState = .processing

        // Create note from session
        Task {
            await createNoteFromSession(session)
            await startCooldownPeriod()
        }

        // Send notification
        NotificationCenter.default.post(
            name: NSNotification.Name("RecordingSessionFinished"),
            object: nil,
            userInfo: [
                "sessionId": session.id.uuidString,
                "text": session.transcribedText,
                "duration": session.duration,
                "keyword": session.getKeywordForNote() ?? ""
            ]
        )
    }

    private func createNoteFromSession(_ session: RecordingSession) async {
        guard !session.transcribedText.isEmpty else {
            print("⚠️ No transcribed text to save")
            return
        }

        print("💾 Creating note from session")

        // Generate title
        let title = generateNoteTitle(from: session)

        // Get keyword for note
        let keywordText = session.getKeywordForNote()

        // Create note using KeywordManager
        if let keywordText = keywordText {
            let note = keywordManager.createNoteWithKeywords(
                title: title,
                content: session.transcribedText,
                audioURL: session.audioURL?.path,
                keywordTexts: [keywordText]
            )
            note.duration = session.duration
            print("✅ Note created with keyword: \(keywordText)")
        } else {
            // Create note without specific keyword
            let note = Note(
                title: title,
                content: session.transcribedText,
                audioURL: session.audioURL?.path,
                keywords: []
            )
            note.duration = session.duration
            dataManager.getModelContext().insert(note)
            try? dataManager.getModelContext().save()
            print("✅ Note created without keyword")
        }
    }

    private func generateNoteTitle(from session: RecordingSession) -> String {
        if let keyword = session.getKeywordForNote() {
            return "\(keyword.capitalized) Note"
        } else if !session.transcribedText.isEmpty {
            let words = session.transcribedText.components(separatedBy: .whitespacesAndNewlines)
            let firstWords = words.prefix(3).joined(separator: " ")
            return firstWords.isEmpty ? "Voice Note" : firstWords
        } else {
            return "Voice Note"
        }
    }

    private func startCooldownPeriod() async {
        print("❄️ Starting cooldown period: \(cooldownPeriod)s")

        currentState = .cooldown
        cooldownEndTime = Date().addingTimeInterval(cooldownPeriod)
        currentSession = nil

        cooldownTimer = Timer.scheduledTimer(withTimeInterval: cooldownPeriod, repeats: false) { [weak self] _ in
            Task { @MainActor in
                await self?.endCooldownPeriod()
            }
        }
    }

    private func endCooldownPeriod() async {
        print("✅ Cooldown period ended")

        cooldownEndTime = nil
        cooldownTimer?.invalidate()
        cooldownTimer = nil

        currentState = .idle

        // Automatically restart keyword listening if it was active before
        await startKeywordListening()
    }

    // MARK: - Audio Recording

    private func setupAudioRecorder(for session: RecordingSession) {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioFilename = documentsPath.appendingPathComponent("recording_\(session.id.uuidString).m4a")
        session.audioURL = audioFilename

        let settings = [
            AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
            AVSampleRateKey: 44100,
            AVNumberOfChannelsKey: 1,
            AVEncoderAudioQualityKey: AVAudioQuality.medium.rawValue,
            AVEncoderBitRateKey: 64000
        ]

        do {
            audioRecorder = try AVAudioRecorder(url: audioFilename, settings: settings)
            audioRecorder?.isMeteringEnabled = true
            audioRecorder?.prepareToRecord()
            print("✅ Audio recorder setup for session: \(session.id)")
        } catch {
            print("❌ Audio recorder setup failed: \(error)")
        }
    }

    // MARK: - Timer Management

    private func startSessionTimers(for session: RecordingSession) {
        // Maximum duration timer
        let maxDuration = maxRecordingDuration
        let sessionId = session.id
        sessionTimer = Timer.scheduledTimer(withTimeInterval: maxDuration, repeats: false) { [weak self] _ in
            print("⏰ Maximum recording duration reached")
            Task { @MainActor in
                if let currentSession = self?.currentSession, currentSession.id == sessionId {
                    self?.finishRecordingSession(currentSession)
                }
            }
        }

        // Initial silence timer
        resetSilenceTimer()

        print("⏰ Session timers started - Max: \(maxRecordingDuration)s, Silence: \(autoStopDuration)s")
    }

    private func resetSilenceTimer() {
        silenceTimer?.invalidate()
        let silenceDuration = autoStopDuration
        silenceTimer = Timer.scheduledTimer(withTimeInterval: silenceDuration, repeats: false) { [weak self] _ in
            print("🔇 Silence detected for \(silenceDuration)s")
            Task { @MainActor in
                if let currentSession = self?.currentSession {
                    self?.finishRecordingSession(currentSession)
                }
            }
        }
    }

    private func stopAllTimers() {
        sessionTimer?.invalidate()
        sessionTimer = nil
        silenceTimer?.invalidate()
        silenceTimer = nil
        levelTimer?.invalidate()
        levelTimer = nil
    }

    // MARK: - Audio Level Monitoring

    private func startAudioLevelMonitoring() {
        levelTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateAudioLevel()
            }
        }
    }

    private func stopAudioLevelMonitoring() {
        levelTimer?.invalidate()
        levelTimer = nil
        audioLevel = 0.0
    }

    private func updateAudioLevel() {
        guard let recorder = audioRecorder, recorder.isRecording else {
            audioLevel = 0.0
            return
        }

        recorder.updateMeters()
        let averagePower = recorder.averagePower(forChannel: 0)
        audioLevel = pow(10.0, averagePower / 20.0)

        // Update current session audio levels
        currentSession?.audioLevels.append(audioLevel)
        if let session = currentSession, session.audioLevels.count > 50 {
            session.audioLevels.removeFirst()
        }

        // Update session duration
        if let session = currentSession {
            session.duration = Date().timeIntervalSince(session.startTime)
        }
    }

    // MARK: - Cleanup

    deinit {
        // Clean up timers directly (not MainActor isolated)
        sessionTimer?.invalidate()
        silenceTimer?.invalidate()
        cooldownTimer?.invalidate()
        levelTimer?.invalidate()

        // Clean up audio
        audioRecorder?.stop()

        // Clean up audio engine
        if audioEngine.isRunning {
            audioEngine.stop()
        }
        audioEngine.inputNode.removeTap(onBus: 0)

        // Clean up recognition
        recognitionRequest?.endAudio()
        recognitionTask?.cancel()
    }
}

// MARK: - SFSpeechRecognizerDelegate

extension UnifiedRecordingService: SFSpeechRecognizerDelegate {
    nonisolated func speechRecognizer(_ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool) {
        Task { @MainActor in
            if !available && (currentState == .listeningForKeywords || currentState == .recordingAfterKeyword || currentState == .recordingManual) {
                print("⚠️ Speech recognizer became unavailable")
                // Handle gracefully - could retry or fallback
            }
        }
    }
}
