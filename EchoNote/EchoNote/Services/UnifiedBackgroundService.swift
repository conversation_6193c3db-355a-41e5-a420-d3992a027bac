//
//  UnifiedBackgroundService.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import SwiftUI

/// Unified background service that manages the UnifiedRecordingService for background keyword monitoring
@MainActor
class UnifiedBackgroundService: ObservableObject {
    
    static let shared = UnifiedBackgroundService()
    
    // MARK: - Published Properties
    
    @Published var isEnabled = false
    @Published var isMonitoring = false
    @Published var lastError: String?
    
    // MARK: - Private Properties
    
    private var recordingService: UnifiedRecordingService?
    private var dataManager: DataManager?
    
    // MARK: - Initialization
    
    private init() {
        setupAppLifecycleObservers()
    }
    
    // MARK: - Public API
    
    /// Initialize the service with a data manager
    func initialize(with dataManager: DataManager) {
        self.dataManager = dataManager
        self.recordingService = UnifiedRecordingService(dataManager: dataManager)

        // Set up state monitoring
        setupStateMonitoring()

        // Load user preferences
        loadUserPreferences()

        print("🔧 UnifiedBackgroundService initialized")
    }

    private func setupStateMonitoring() {
        // Monitor recording service state changes
        Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            guard let self = self, let recordingService = self.recordingService else { return }

            Task { @MainActor in
                let actuallyMonitoring = (recordingService.currentState == .listeningForKeywords)
                if self.isMonitoring != actuallyMonitoring {
                    print("🔄 Syncing background service state: \(self.isMonitoring) → \(actuallyMonitoring)")
                    self.isMonitoring = actuallyMonitoring
                }
            }
        }
    }
    
    /// Start background monitoring
    func startMonitoring() {
        guard let recordingService = recordingService else {
            print("❌ Recording service not initialized")
            return
        }

        // Check if already monitoring
        if recordingService.currentState == .listeningForKeywords {
            print("✅ Already monitoring keywords")
            isMonitoring = true
            return
        }

        print("🎯 Starting unified background monitoring")

        Task {
            await recordingService.startKeywordListening()
            // Update state based on actual recording service state
            DispatchQueue.main.async {
                self.isMonitoring = (recordingService.currentState == .listeningForKeywords)
                self.lastError = nil
                print("📊 Background monitoring state updated: \(self.isMonitoring)")
            }
        }
    }
    
    /// Stop background monitoring
    func stopMonitoring() {
        guard let recordingService = recordingService else { return }
        
        print("🛑 Stopping unified background monitoring")
        
        recordingService.stopKeywordListening()
        isMonitoring = false
    }
    
    /// Toggle monitoring state
    func toggleMonitoring() {
        if isMonitoring {
            stopMonitoring()
        } else {
            startMonitoring()
        }
    }
    
    /// Update keywords from database
    func updateKeywords(_ keywords: [Keyword]) {
        recordingService?.updateKeywords(keywords)
    }
    
    /// Check if actively monitoring (for UI display)
    var isActivelyMonitoring: Bool {
        return isMonitoring && (recordingService?.currentState == .listeningForKeywords)
    }
    
    /// Get current recording state
    var currentRecordingState: RecordingSessionState? {
        return recordingService?.currentState
    }
    
    // MARK: - Configuration
    
    private func loadUserPreferences() {
        guard let dataManager = dataManager else { return }
        
        let preferences = dataManager.getUserPreferences()
        isEnabled = preferences?.backgroundMonitorEnabled ?? false
        
        print("📋 Background monitoring enabled: \(isEnabled)")
        
        // Auto-start if enabled
        if isEnabled {
            startMonitoring()
        }
    }
    
    /// Update background monitoring preference
    func updateBackgroundMonitoringPreference(_ enabled: Bool) {
        isEnabled = enabled
        
        if enabled {
            startMonitoring()
        } else {
            stopMonitoring()
        }
        
        // Update user preferences in database
        if let dataManager = dataManager {
            let preferences = dataManager.getUserPreferences() ?? UserPreferences()
            preferences.backgroundMonitorEnabled = enabled
            dataManager.updateUserPreferences(preferences)
        }
    }
    
    // MARK: - App Lifecycle
    
    private func setupAppLifecycleObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidBecomeActive),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
    }
    
    @objc private func appDidEnterBackground() {
        print("📱 App entered background - maintaining monitoring state")
        // Keep monitoring active in background if enabled
    }
    
    @objc private func appWillEnterForeground() {
        print("📱 App will enter foreground - checking monitoring state")
        // Restore monitoring if it was active
        if isEnabled && !isMonitoring {
            startMonitoring()
        }
    }
    
    @objc private func appDidBecomeActive() {
        print("📱 App became active - refreshing state")
        // Refresh monitoring state
        if let recordingService = recordingService {
            isMonitoring = recordingService.isListeningForKeywords
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - Compatibility Extensions

extension UnifiedBackgroundService {
    
    /// Compatibility method for existing code
    var isStarting: Bool {
        return recordingService?.currentState == .processing
    }
    
    /// Compatibility method for existing code
    var isRecordingAfterKeyword: Bool {
        return recordingService?.currentState == .recordingAfterKeyword
    }
    
    /// Get glow color for UI (compatibility)
    var glowColor: Color {
        switch currentRecordingState {
        case .listeningForKeywords:
            return .green
        case .recordingAfterKeyword, .recordingManual:
            return .red
        case .processing:
            return .orange
        case .cooldown:
            return .purple
        default:
            return .gray
        }
    }
}
