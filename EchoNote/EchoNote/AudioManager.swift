//
//  AudioManager.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import Foundation
import AVFoundation
import Speech
import SwiftUI

// MARK: - Speech Recognition Errors

enum SpeechRecognitionError: Error {
    case recognizerUnavailable
    case requestCreationFailed
    case audioEngineStartFailed(Error)
    case recognitionFailed(Error)
    case permissionDenied
    case networkUnavailable

    var localizedDescription: String {
        switch self {
        case .recognizerUnavailable:
            return "Speech recognizer is not available"
        case .requestCreationFailed:
            return "Failed to create recognition request"
        case .audioEngineStartFailed(let error):
            return "Audio engine failed to start: \(error.localizedDescription)"
        case .recognitionFailed(let error):
            return "Speech recognition failed: \(error.localizedDescription)"
        case .permissionDenied:
            return "Speech recognition permission denied"
        case .networkUnavailable:
            return "Network unavailable for speech recognition"
        }
    }
}

@MainActor
class AudioManager: NSObject, ObservableObject, SFSpeechRecognizerDelegate, AVAudioPlayerDelegate {
    private var audioEngine = AVAudioEngine()
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var audioRecorder: AVAudioRecorder?
    private var audioPlayer: AVAudioPlayer?

    // State
    @Published var isRecording = false
    @Published var isRecordingPaused = false
    @Published var isListening = false
    @Published var isPlaying = false
    @Published var transcribedText = ""
    @Published var detectedKeyword = ""
    @Published var recordingDuration: TimeInterval = 0
    @Published var playbackDuration: TimeInterval = 0
    @Published var playbackCurrentTime: TimeInterval = 0
    @Published var audioLevels: [Float] = []
    @Published var currentAudioURL: URL?

    // Audio metering
    @Published var recordingAudioLevel: Float = 0.0
    @Published var playbackAudioLevel: Float = 0.0
    
    // Permissions service
    private let permissionsService = PermissionsService()

    // Secure data manager
    private let secureDataManager: SecureDataManager?

    // Configuration
    private var registeredKeywords: [String] = ["keyword", "note", "record", "start"]
    private var recognitionLanguage = "en-US"
    private var recognitionConfidenceThreshold: Float = 0.7
    private var useOnDeviceRecognition = false

    // Timers
    private var recordingTimer: Timer?
    private var levelTimer: Timer?

    // Computed properties for permissions
    var hasMicrophonePermission: Bool {
        AVAudioSession.sharedInstance().recordPermission == .granted
    }

    var hasSpeechRecognitionPermission: Bool {
        SFSpeechRecognizer.authorizationStatus() == .authorized
    }

    override init() {
        // Initialize secure data manager if privacy is enabled
        if UserDefaults.standard.bool(forKey: "privacyModeEnabled") {
            do {
                secureDataManager = try SecureDataManager()
            } catch {
                print("Failed to initialize SecureDataManager: \(error)")
                secureDataManager = nil
            }
        } else {
            secureDataManager = nil
        }

        super.init()
        
        configureSpeechRecognizer()
        setupAudio()
    }

    private func configureSpeechRecognizer() {
        // Configure speech recognizer with locale
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: recognitionLanguage))

        guard let speechRecognizer = speechRecognizer else {
            return
        }

        // Enable on-device recognition if available and requested
        if useOnDeviceRecognition && speechRecognizer.supportsOnDeviceRecognition {
            speechRecognizer.defaultTaskHint = .dictation
        }

        // Set delegate for monitoring availability
        speechRecognizer.delegate = self
    }
    
    // MARK: - Setup
    
    private func setupAudio() {
        requestPermissions()
        setupAudioSession()
    }
    
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true)

            // Set preferred sample rate and buffer duration for better performance
            try audioSession.setPreferredSampleRate(44100.0)
            try audioSession.setPreferredIOBufferDuration(0.005)

        } catch {
            handleAudioError(error)
        }
    }

    private func ensureAudioSessionActive() {
        let audioSession = AVAudioSession.sharedInstance()

        do {
            // Force deactivate first, then reactivate
            try audioSession.setActive(false, options: .notifyOthersOnDeactivation)

            // Wait a moment
            Thread.sleep(forTimeInterval: 0.1)

            // Reactivate audio session for speech recognition
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true, options: [])

        } catch {
            // Try alternative approach
            do {
                try audioSession.setCategory(.record, mode: .measurement, options: [])
                try audioSession.setActive(true, options: [])
            } catch {
                handleAudioError(error)
            }
        }
    }

    func configureAudioSessionForRecording() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.record, mode: .measurement, options: [])
            try audioSession.setActive(true)
        } catch {
            handleAudioError(error)
        }
    }

    func configureAudioSessionForPlayback() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playback, mode: .default, options: [])
            try audioSession.setActive(true)
        } catch {
            handleAudioError(error)
        }
    }

    func configureAudioSessionForPlayAndRecord() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true)
        } catch {
            handleAudioError(error)
        }
    }
    
    private func requestPermissions() {
        // Use the new permissions service for initial check
        Task {
            await permissionsService.checkAllPermissions()
        }
    }

    func requestPermissionsAsync() async {
        // Public async method for requesting permissions
        await permissionsService.checkAllPermissions()
        let _ = await permissionsService.requestAllRequiredPermissions()
    }

    func getPermissionsService() -> PermissionsService {
        return permissionsService
    }
    
    // MARK: - Keyword Detection
    
    func startListening() {
        // Request permissions if not granted
        if !hasMicrophonePermission || !hasSpeechRecognitionPermission {
            Task {
                await requestPermissionsAsync()

                // Try again after requesting permissions
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    self.startListeningAfterPermissions()
                }
            }
            return
        }

        // Ensure audio session is active before starting
        ensureAudioSessionActive()
        startListeningAfterPermissions()
    }

    private func startListeningAfterPermissions() {
        guard hasMicrophonePermission && hasSpeechRecognitionPermission else {
            return
        }

        // Double-check audio session is active
        ensureAudioSessionActive()

        guard !isListening else {
            return
        }

        isListening = true
        detectedKeyword = ""
        transcribedText = ""

        startSpeechRecognition()
    }
    
    func stopListening() {
        isListening = false
        stopSpeechRecognition()
    }

    private func startSpeechRecognition() {
        print("🎤 Starting speech recognition...")

        // Cancel any previous task
        recognitionTask?.cancel()
        recognitionTask = nil

        // Check if speech recognizer is available
        guard let speechRecognizer = speechRecognizer, speechRecognizer.isAvailable else {
            print("❌ Speech recognizer unavailable")
            handleSpeechRecognitionError(SpeechRecognitionError.recognizerUnavailable)
            return
        }

        // Configure audio session specifically for speech recognition
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.record, mode: .measurement, options: [.duckOthers])
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
            print("✅ Audio session configured for speech recognition")
        } catch {
            print("❌ Audio session configuration failed: \(error)")
            handleSpeechRecognitionError(SpeechRecognitionError.audioEngineStartFailed(error))
            return
        }

        // Create recognition request
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            print("❌ Failed to create recognition request")
            handleSpeechRecognitionError(SpeechRecognitionError.requestCreationFailed)
            return
        }

        // Configure recognition request for better real-time performance
        recognitionRequest.shouldReportPartialResults = true
        recognitionRequest.taskHint = .search // Better for keyword detection

        // Disable on-device recognition to avoid Code 1101 error
        recognitionRequest.requiresOnDeviceRecognition = false
        print("🔧 Recognition request configured (cloud-based for stability)")

        // Check if audio engine is already running
        if audioEngine.isRunning {
            audioEngine.stop()
            audioEngine.inputNode.removeTap(onBus: 0)
        }

        // Start audio engine
        let inputNode = audioEngine.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)
        print("🎧 Audio format: \(recordingFormat)")

        // Remove any existing tap
        inputNode.removeTap(onBus: 0)

        // Install tap with larger buffer for better stability
        inputNode.installTap(onBus: 0, bufferSize: 2048, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)
        }

        audioEngine.prepare()

        do {
            try audioEngine.start()
            print("✅ Audio engine started successfully")
        } catch {
            print("❌ Audio engine start failed: \(error)")
            handleSpeechRecognitionError(SpeechRecognitionError.audioEngineStartFailed(error))
            return
        }

        // Start recognition task
        recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { [weak self] result, error in
            guard let self = self else { return }

            DispatchQueue.main.async {
                if let result = result {
                    let transcription = result.bestTranscription.formattedString
                    print("🗣️ Transcription: \(transcription)")

                    // Update transcribed text for real-time display
                    self.transcribedText = transcription

                    // Check for keywords in lowercase
                    let lowercaseTranscription = transcription.lowercased()

                    // Get confidence score
                    let confidence = result.bestTranscription.segments.first?.confidence ?? 0.0
                    print("📊 Confidence: \(confidence)")

                    // Check for keywords only if we're listening (not recording)
                    if self.isListening && !self.isRecording {
                        print("🔍 Checking keywords. Registered: \(self.registeredKeywords)")
                        print("🔍 Transcription: '\(lowercaseTranscription)'")
                        print("🔍 Confidence threshold: \(self.recognitionConfidenceThreshold)")

                        for keyword in self.registeredKeywords {
                            let keywordLower = keyword.lowercased()
                            let contains = lowercaseTranscription.contains(keywordLower)
                            let confidenceOK = confidence >= self.recognitionConfidenceThreshold

                            print("🔍 Checking keyword '\(keyword)' -> contains: \(contains), confidence OK: \(confidenceOK)")

                            if contains && confidenceOK {
                                print("🎯 Keyword detected: \(keyword) with confidence: \(confidence)")
                                self.detectedKeyword = keyword

                                // Stop listening and start recording
                                self.stopListening()

                                // Start recording after a brief delay
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                    self.startRecording()
                                }
                                return
                            }
                        }
                    } else {
                        print("🔍 Not checking keywords - isListening: \(self.isListening), isRecording: \(self.isRecording)")
                    }
                }

                if let error = error {
                    let nsError = error as NSError
                    print("❌ Speech recognition error: \(error)")
                    print("   Domain: \(nsError.domain), Code: \(nsError.code)")

                    // Handle specific error codes
                    if nsError.domain == "kAFAssistantErrorDomain" {
                        switch nsError.code {
                        case 1101:
                            print("🔄 Code 1101: Speech service access denied - retrying with different configuration")
                            // Retry with different configuration
                            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                                if self.isListening {
                                    self.restartSpeechRecognition()
                                }
                            }
                            return
                        case 1110:
                            // No speech detected - this is normal, don't treat as error
                            if self.isRecording {
                                return
                            }
                        default:
                            break
                        }
                    }

                    self.handleSpeechRecognitionError(SpeechRecognitionError.recognitionFailed(error))
                }
            }
        }
    }

    private func stopSpeechRecognition() {
        print("🛑 Stopping speech recognition")
        audioEngine.stop()
        audioEngine.inputNode.removeTap(onBus: 0)
        recognitionRequest?.endAudio()
        recognitionRequest = nil
        recognitionTask?.cancel()
        recognitionTask = nil
    }

    private func restartSpeechRecognition() {
        print("🔄 Restarting speech recognition with fallback configuration")
        stopSpeechRecognition()

        // Wait a moment before restarting
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            if self.isListening {
                self.startSpeechRecognition()
            }
        }
    }

    // MARK: - Recording

    func startRecording() {
        PerformanceMonitoringService.shared.measurePerformance(of: {
            guard hasMicrophonePermission else { return }
            guard !isRecording else { return }

            // Stop listening for keywords when we start recording
            if isListening {
                stopListening()
            }

            // Configure audio session for recording
            configureAudioSessionForRecording()

            // Setup recorder
            setupRecorder()

            // Start recording
            audioRecorder?.record()
            isRecording = true
            recordingDuration = 0

            // Start recording timer
            startRecordingTimer()

            // Start transcription for real-time text
            startTranscription()

        }, eventType: .audioRecordingStart)
    }

    func pauseRecording() {
        audioRecorder?.pause()
        isRecordingPaused = true
        recordingTimer?.invalidate()
    }

    func resumeRecording() {
        audioRecorder?.record()
        isRecordingPaused = false
        startRecordingTimer()
    }

    func stopRecording() -> URL? {
        audioRecorder?.stop()
        isRecording = false
        isRecordingPaused = false
        recordingTimer?.invalidate()
        recordingTimer = nil

        // Stop transcription
        stopSpeechRecognition()

        // Return the recorded file URL
        return currentAudioURL
    }

    private func setupRecorder() {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioFilename = documentsPath.appendingPathComponent("recording_\(Date().timeIntervalSince1970).m4a")
        currentAudioURL = audioFilename

        // Optimized settings for better performance
        let settings = [
            AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
            AVSampleRateKey: 44100, // Standard sample rate
            AVNumberOfChannelsKey: 1, // Mono for efficiency
            AVEncoderAudioQualityKey: AVAudioQuality.medium.rawValue, // Balanced quality/performance
            AVEncoderBitRateKey: 64000 // Optimized bit rate for voice
        ]

        do {
            audioRecorder = try AVAudioRecorder(url: audioFilename, settings: settings)
            audioRecorder?.isMeteringEnabled = true
            audioRecorder?.prepareToRecord()
        } catch {
            // Handle recorder setup error silently
        }
    }

    private func startRecordingTimer() {
        // Optimized timer with lower frequency for better performance
        recordingTimer = Timer.scheduledTimer(withTimeInterval: 0.2, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            Task { @MainActor in
                self.recordingDuration += 0.2

                // Update audio levels for visualization
                if let recorder = self.audioRecorder, recorder.isRecording {
                    recorder.updateMeters()
                    let averagePower = recorder.averagePower(forChannel: 0)
                    self.recordingAudioLevel = pow(10.0, averagePower / 20.0)

                    // Update audio levels array for waveform
                    self.audioLevels.append(self.recordingAudioLevel)
                    if self.audioLevels.count > 50 {
                        self.audioLevels.removeFirst()
                    }
                }
            }
        }
    }

    private func startTranscription() {
        print("📝 Starting transcription for recording")

        // Don't start speech recognition if already listening
        // The recording will use a separate transcription approach
        guard !isListening else {
            print("⚠️ Already listening, skipping transcription setup")
            return
        }

        // Create a separate recognition request for recording transcription
        guard let speechRecognizer = speechRecognizer, speechRecognizer.isAvailable else {
            print("❌ Speech recognizer unavailable for transcription")
            return
        }

        // Configure audio session for recording with transcription
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true)
            print("✅ Audio session configured for recording with transcription")
        } catch {
            print("❌ Audio session configuration failed for transcription: \(error)")
            return
        }

        // Create new recognition request for transcription
        let transcriptionRequest = SFSpeechAudioBufferRecognitionRequest()
        transcriptionRequest.shouldReportPartialResults = true
        transcriptionRequest.taskHint = .dictation
        transcriptionRequest.requiresOnDeviceRecognition = false

        // Start transcription task
        recognitionTask = speechRecognizer.recognitionTask(with: transcriptionRequest) { [weak self] result, error in
            guard let self = self else { return }

            DispatchQueue.main.async {
                if let result = result {
                    let transcription = result.bestTranscription.formattedString
                    print("📝 Recording transcription: \(transcription)")
                    self.transcribedText = transcription
                }

                if let error = error {
                    print("❌ Transcription error: \(error)")
                }
            }
        }

        // Set up audio tap for transcription
        if !audioEngine.isRunning {
            let inputNode = audioEngine.inputNode
            let recordingFormat = inputNode.outputFormat(forBus: 0)

            inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
                transcriptionRequest.append(buffer)
            }

            audioEngine.prepare()
            do {
                try audioEngine.start()
                print("✅ Audio engine started for transcription")
            } catch {
                print("❌ Failed to start audio engine for transcription: \(error)")
            }
        }
    }

    // MARK: - Playback

    func playRecording(url: URL) {
        PerformanceMonitoringService.shared.measurePerformance(of: {
            do {
                // Stop any current playback
                stopPlayback()

                // Create audio player
                audioPlayer = try AVAudioPlayer(contentsOf: url)
                audioPlayer?.delegate = self
                audioPlayer?.isMeteringEnabled = true

                // Prepare to play
                audioPlayer?.prepareToPlay()

                // Update state
                isPlaying = true
                playbackDuration = audioPlayer?.duration ?? 0
                playbackCurrentTime = 0

                // Start playback
                audioPlayer?.play()

                // Start metering timer
                startPlaybackMetering()

            } catch {
                handleAudioError(error)
            }
        }, eventType: .audioPlaybackStart)
    }

    func pausePlayback() {
        audioPlayer?.pause()
        isPlaying = false
        stopPlaybackMetering()
    }

    func resumePlayback() {
        audioPlayer?.play()
        isPlaying = true
        startPlaybackMetering()
    }

    func stopPlayback() {
        audioPlayer?.stop()
        audioPlayer = nil
        isPlaying = false
        playbackCurrentTime = 0
        playbackAudioLevel = 0.0
        stopPlaybackMetering()
    }

    func seekToTime(_ time: TimeInterval) {
        guard let player = audioPlayer else { return }
        player.currentTime = min(max(time, 0), player.duration)
        playbackCurrentTime = player.currentTime
    }

    private func startPlaybackMetering() {
        levelTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updatePlaybackMetering()
            }
        }
    }

    private func stopPlaybackMetering() {
        levelTimer?.invalidate()
        levelTimer = nil
        playbackAudioLevel = 0.0
    }

    private func updatePlaybackMetering() {
        guard let player = audioPlayer, player.isPlaying else { return }
        player.updateMeters()
        let averagePower = player.averagePower(forChannel: 0)
        playbackAudioLevel = pow(10.0, averagePower / 20.0)
    }

    // MARK: - Keyword Management

    func updateKeywords(from databaseKeywords: [Keyword]) {
        // Update keywords from database
        registeredKeywords = databaseKeywords
            .filter { $0.isEnabled }
            .map { $0.text }

        print("AudioManager: Updated keywords: \(registeredKeywords)")
    }

    func addKeyword(_ text: String) {
        if !registeredKeywords.contains(text) {
            registeredKeywords.append(text)
        }
    }

    func removeKeyword(_ text: String) {
        registeredKeywords.removeAll { $0 == text }
    }

    func getRegisteredKeywords() -> [String] {
        return registeredKeywords
    }

    // MARK: - Configuration

    func updateLanguage(_ language: String) {
        recognitionLanguage = language
        configureSpeechRecognizer()
    }

    func updateConfidenceThreshold(_ threshold: Float) {
        recognitionConfidenceThreshold = max(0.0, min(1.0, threshold))
    }

    func toggleOnDeviceRecognition(_ enabled: Bool) {
        useOnDeviceRecognition = enabled
        configureSpeechRecognizer()
    }

    func isSpeechRecognitionAvailable() -> Bool {
        return speechRecognizer?.isAvailable == true
    }

    func supportsOnDeviceRecognition() -> Bool {
        return speechRecognizer?.supportsOnDeviceRecognition == true
    }

    // MARK: - Utility

    func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    func getRecordingURL() -> URL {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioFilename = "recording_\(Date().timeIntervalSince1970).m4a"
        return documentsPath.appendingPathComponent(audioFilename)
    }

    // MARK: - Error Handling

    private func handleAudioError(_ error: Error) {
        DispatchQueue.main.async {
            // Reset states on error
            self.isRecording = false
            self.isListening = false
            self.isPlaying = false

            // Log error for debugging
            print("Audio error: \(error.localizedDescription)")
        }
    }

    private func handleSpeechRecognitionError(_ error: SpeechRecognitionError) {
        DispatchQueue.main.async {
            // Reset speech recognition states
            self.isListening = false
            self.detectedKeyword = ""

            // Log error for debugging
            print("Speech recognition error: \(error.localizedDescription)")
        }
    }

    // MARK: - Cleanup

    deinit {
        // Clean up timers on main actor
        Task { @MainActor in
            levelTimer?.invalidate()
            recordingTimer?.invalidate()

            // Clean up audio session and players
            audioRecorder?.stop()
            audioPlayer?.stop()
        }

        // Reset audio session
        try? AVAudioSession.sharedInstance().setActive(false)
    }

    // MARK: - AVAudioPlayerDelegate

    nonisolated func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        Task { @MainActor in
            self.isPlaying = false
            self.playbackCurrentTime = 0
            self.playbackAudioLevel = 0.0
            self.stopPlaybackMetering()
        }
    }

    nonisolated func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        if let error = error {
            Task { @MainActor in
                self.handleAudioError(error)
            }
        }
    }

    // MARK: - SFSpeechRecognizerDelegate

    nonisolated func speechRecognizer(_ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool) {
        DispatchQueue.main.async {
            if !available {
                if self.isListening {
                    self.stopListening()
                }
            }
        }
    }
}
