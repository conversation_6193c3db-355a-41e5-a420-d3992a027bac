//
//  MainTabView.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI
import Combine

// Environment key for hiding floating button
struct HideFloatingButtonKey: EnvironmentKey {
    static let defaultValue: Binding<Bool> = .constant(false)
}

extension EnvironmentValues {
    var hideFloatingButton: Binding<Bool> {
        get { self[HideFloatingButtonKey.self] }
        set { self[HideFloatingButtonKey.self] = newValue }
    }
}

struct MainTabView: View {
    @Environment(\.serviceManager) private var serviceManager: ServiceManager
    @State private var selectedTab = 0
    @State private var showingRecordingView = false
    @State private var hideFloatingButton = false
    @State private var recordingCoordinator: (any RecordingCoordinating)?
    @State private var isRecording = false
    @State private var cancellables = Set<AnyCancellable>()
    
    var body: some View {
        ZStack {
            TabView(selection: $selectedTab) {
                HomeView()
                    .tabItem {
                        Image(systemName: "house.fill")
                        Text("Home")
                    }
                    .tag(0)

                KeywordsView()
                    .tabItem {
                        Image(systemName: "tag.fill")
                        Text("Keywords")
                    }
                    .tag(1)

                // Empty view for center button
                Color.clear
                    .tabItem {
                        Image(systemName: "")
                        Text("")
                    }
                    .tag(2)

                NotesView()
                    .tabItem {
                        Image(systemName: "note.text")
                        Text("Notes")
                    }
                    .tag(3)

                ProfileView()
                    .tabItem {
                        Image(systemName: "person.fill")
                        Text("Profile")
                    }
                    .tag(4)
            }
            .accentColor(.green)
            .environment(\.hideFloatingButton, $hideFloatingButton)
            
            // Custom center recording button - hidden on Home tab and when editing
            if selectedTab != 0 && !hideFloatingButton {
                VStack {
                    Spacer()

                    HStack {
                        Spacer()

                        FloatingRecordingButton(
                            isRecording: isRecording,
                            recordingCoordinator: recordingCoordinator,
                            showingRecordingView: $showingRecordingView
                        )

                        Spacer()
                    }
                    .padding(.bottom, 30)
                }
                .transition(.opacity.combined(with: .scale))
                .animation(.easeInOut(duration: 0.3), value: selectedTab)
            }
        }
        .fullScreenCover(isPresented: $showingRecordingView) {
            RecordingView(isPresented: $showingRecordingView)
        }
        .onAppear {
            Task {
                recordingCoordinator = await serviceManager.getRecordingCoordinator()
                setupRecordingStateBindings()
            }
        }
    }

    // MARK: - Private Methods

    private func setupRecordingStateBindings() {
        guard let coordinator = recordingCoordinator else { return }

        coordinator.recordingState
            .sink { [weak coordinator] state in
                switch state {
                case .recording:
                    isRecording = true
                case .idle, .listening, .processing, .error:
                    isRecording = false
                }
            }
            .store(in: &cancellables)
    }
}

// MARK: - Floating Recording Button

struct FloatingRecordingButton: View {
    let isRecording: Bool
    let recordingCoordinator: (any RecordingCoordinating)?
    @Binding var showingRecordingView: Bool
    @StateObject private var themeManager = ThemeManager.shared

    var body: some View {
        Button(action: handleButtonTap) {
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: buttonColors),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 70, height: 70)
                    .shadow(color: shadowColor, radius: 10, x: 0, y: 5)

                Image(systemName: buttonIcon)
                    .font(.system(size: 30, weight: .bold))
                    .foregroundColor(.yellow)
                    .rotationEffect(.degrees(isRecording ? 360 : 0))
                    .animation(.linear(duration: 2).repeatForever(autoreverses: false), value: isRecording)
            }
        }
        .scaleEffect(showingRecordingView ? 0.9 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: showingRecordingView)
        .disabled(recordingCoordinator == nil)
    }

    private var buttonColors: [Color] {
        if isRecording {
            return [Color.red, Color.red.opacity(0.8)]
        } else {
            return [Color.green, Color.green.opacity(0.8)]
        }
    }

    private var shadowColor: Color {
        isRecording ? .red.opacity(0.5) : .green.opacity(0.5)
    }

    private var buttonIcon: String {
        isRecording ? "stop.circle.fill" : "headphones"
    }

    private func handleButtonTap() {
        guard let coordinator = recordingCoordinator else { return }

        Task {
            if isRecording {
                // Stop current recording
                try? await coordinator.stopManualRecording()
            } else {
                // Start new recording
                showingRecordingView = true
            }
        }
    }
}

#Preview {
    MainTabView()
        .modelContainer(for: [Note.self, Keyword.self, UserPreferences.self], inMemory: true)
}
