//
//  MainTabView.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import SwiftUI

// Environment key for hiding floating button
struct HideFloatingButtonKey: EnvironmentKey {
    static let defaultValue: Binding<Bool> = .constant(false)
}

extension EnvironmentValues {
    var hideFloatingButton: Binding<Bool> {
        get { self[HideFloatingButtonKey.self] }
        set { self[HideFloatingButtonKey.self] = newValue }
    }
}

struct MainTabView: View {
    @State private var selectedTab = 0
    @State private var showingRecordingView = false
    @State private var hideFloatingButton = false
    
    var body: some View {
        ZStack {
            TabView(selection: $selectedTab) {
                HomeView()
                    .tabItem {
                        Image(systemName: "house.fill")
                        Text("Home")
                    }
                    .tag(0)

                KeywordsView()
                    .tabItem {
                        Image(systemName: "tag.fill")
                        Text("Keywords")
                    }
                    .tag(1)

                // Empty view for center button
                Color.clear
                    .tabItem {
                        Image(systemName: "")
                        Text("")
                    }
                    .tag(2)

                NotesView()
                    .tabItem {
                        Image(systemName: "note.text")
                        Text("Notes")
                    }
                    .tag(3)

                ProfileView()
                    .tabItem {
                        Image(systemName: "person.fill")
                        Text("Profile")
                    }
                    .tag(4)
            }
            .accentColor(.green)
            .environment(\.hideFloatingButton, $hideFloatingButton)
            
            // Custom center recording button - hidden on Home tab and when editing
            if selectedTab != 0 && !hideFloatingButton {
                VStack {
                    Spacer()

                    HStack {
                        Spacer()

                        Button(action: {
                            showingRecordingView = true
                        }) {
                            ZStack {
                                Circle()
                                    .fill(
                                        LinearGradient(
                                            gradient: Gradient(colors: [Color.green, Color.green.opacity(0.8)]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .frame(width: 70, height: 70)
                                    .shadow(color: .green.opacity(0.5), radius: 10, x: 0, y: 5)

                                Image(systemName: "headphones")
                                    .font(.system(size: 30, weight: .bold))
                                    .foregroundColor(.yellow)
                            }
                        }
                        .scaleEffect(showingRecordingView ? 0.9 : 1.0)
                        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: showingRecordingView)

                        Spacer()
                    }
                    .padding(.bottom, 30)
                }
                .transition(.opacity.combined(with: .scale))
                .animation(.easeInOut(duration: 0.3), value: selectedTab)
            }
        }
        .fullScreenCover(isPresented: $showingRecordingView) {
            UnifiedRecordingView(isPresented: $showingRecordingView)
        }
    }
}

#Preview {
    MainTabView()
        .modelContainer(for: [Note.self, Keyword.self, UserPreferences.self], inMemory: true)
}
