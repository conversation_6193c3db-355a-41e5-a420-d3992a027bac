//
//  KeywordManager.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import Combine

// MARK: - Keyword Managing Protocol

protocol KeywordManaging: ObservableObject {
    var keywords: [Keyword] { get }
    var keywordTexts: [String] { get }
    var keywordsPublisher: AnyPublisher<[Keyword], Never> { get }
    var keywordTextsPublisher: AnyPublisher<[String], Never> { get }
    
    func loadKeywords() async throws
    func addKeyword(_ keyword: String) async throws -> Keyword
    func removeKeyword(_ keyword: Keyword) async throws
    func removeKeyword(withText text: String) async throws
    func updateKeyword(_ keyword: Keyword) async throws
    func updateKeywords(_ keywords: [Keyword]) async throws
    func validateKeyword(_ text: String) -> KeywordValidationResult
    func searchKeywords(_ query: String) -> [Keyword]
}

// MARK: - Keyword Validation

enum KeywordValidationResult {
    case valid
    case tooShort
    case tooLong
    case containsInvalidCharacters
    case alreadyExists
    case empty
    
    var isValid: Bool {
        if case .valid = self { return true }
        return false
    }
    
    var errorMessage: String {
        switch self {
        case .valid:
            return ""
        case .tooShort:
            return "Keyword must be at least 2 characters long"
        case .tooLong:
            return "Keyword must be no more than 50 characters long"
        case .containsInvalidCharacters:
            return "Keyword can only contain letters, numbers, and spaces"
        case .alreadyExists:
            return "This keyword already exists"
        case .empty:
            return "Keyword cannot be empty"
        }
    }
}

// MARK: - Keyword Manager

@MainActor
class KeywordManager: KeywordManaging {
    
    // MARK: - Published Properties
    @Published private(set) var keywords: [Keyword] = []
    
    var keywordTexts: [String] {
        keywords.map { $0.text }
    }
    
    var keywordsPublisher: AnyPublisher<[Keyword], Never> {
        $keywords.eraseToAnyPublisher()
    }
    
    var keywordTextsPublisher: AnyPublisher<[String], Never> {
        $keywords
            .map { keywords in keywords.map { $0.text } }
            .eraseToAnyPublisher()
    }
    
    // MARK: - Private Properties
    private let dataManager: DataManaging
    private let keywordDetectionService: KeywordDetectionServicing?
    private let eventBus = EventBus.shared
    private var cancellables = Set<AnyCancellable>()
    
    // Validation constants
    private let minKeywordLength = 2
    private let maxKeywordLength = 50
    private let validCharacterSet = CharacterSet.alphanumerics.union(.whitespaces)
    
    // MARK: - Initialization
    
    init(
        dataManager: DataManaging,
        keywordDetectionService: KeywordDetectionServicing? = nil
    ) {
        self.dataManager = dataManager
        self.keywordDetectionService = keywordDetectionService
        
        setupEventSubscriptions()
        
        // Load initial keywords
        Task {
            do {
                try await loadKeywords()
            } catch {
                print("❌ KeywordManager: Failed to load initial keywords: \(error)")
                eventBus.publishError(error: error, context: "KeywordManager initialization")
            }
        }
    }
    
    // MARK: - Public Methods
    
    func loadKeywords() async throws {
        do {
            let loadedKeywords = try await dataManager.fetchKeywords()
            keywords = loadedKeywords
            
            // Update keyword detection service
            if let detectionService = keywordDetectionService {
                await detectionService.updateKeywords(keywordTexts)
            }
            
            // Publish event
            eventBus.publish(KeywordsUpdatedEvent(keywords: keywords))
            
            print("✅ KeywordManager: Loaded \(keywords.count) keywords")
        } catch {
            print("❌ KeywordManager: Failed to load keywords: \(error)")
            throw error
        }
    }
    
    func addKeyword(_ text: String) async throws -> Keyword {
        // Validate keyword
        let validation = validateKeyword(text)
        guard validation.isValid else {
            throw KeywordError.validationFailed(validation.errorMessage)
        }
        
        // Create new keyword
        let keyword = Keyword(text: text.trimmingCharacters(in: .whitespacesAndNewlines))
        
        do {
            // Save to data store
            var updatedKeywords = keywords
            updatedKeywords.append(keyword)
            try await dataManager.saveKeywords(updatedKeywords)
            
            // Update in-memory cache
            keywords = updatedKeywords
            
            // Update keyword detection service
            if let detectionService = keywordDetectionService {
                await detectionService.updateKeywords(keywordTexts)
            }
            
            // Publish events
            eventBus.publish(KeywordsUpdatedEvent(keywords: keywords))
            
            print("✅ KeywordManager: Added keyword '\(keyword.text)'")
            return keyword
        } catch {
            print("❌ KeywordManager: Failed to add keyword '\(text)': \(error)")
            throw error
        }
    }
    
    func removeKeyword(_ keyword: Keyword) async throws {
        guard let index = keywords.firstIndex(where: { $0.id == keyword.id }) else {
            throw KeywordError.keywordNotFound
        }
        
        do {
            // Remove from data store
            var updatedKeywords = keywords
            updatedKeywords.remove(at: index)
            try await dataManager.saveKeywords(updatedKeywords)
            
            // Update in-memory cache
            keywords = updatedKeywords
            
            // Update keyword detection service
            if let detectionService = keywordDetectionService {
                await detectionService.updateKeywords(keywordTexts)
            }
            
            // Publish events
            eventBus.publish(KeywordsUpdatedEvent(keywords: keywords))
            
            print("✅ KeywordManager: Removed keyword '\(keyword.text)'")
        } catch {
            print("❌ KeywordManager: Failed to remove keyword '\(keyword.text)': \(error)")
            throw error
        }
    }
    
    func removeKeyword(withText text: String) async throws {
        guard let keyword = keywords.first(where: { $0.text == text }) else {
            throw KeywordError.keywordNotFound
        }
        
        try await removeKeyword(keyword)
    }
    
    func updateKeyword(_ keyword: Keyword) async throws {
        guard let index = keywords.firstIndex(where: { $0.id == keyword.id }) else {
            throw KeywordError.keywordNotFound
        }
        
        // Validate updated keyword
        let validation = validateKeyword(keyword.text)
        guard validation.isValid else {
            throw KeywordError.validationFailed(validation.errorMessage)
        }
        
        do {
            // Update in data store
            var updatedKeywords = keywords
            updatedKeywords[index] = keyword
            try await dataManager.saveKeywords(updatedKeywords)
            
            // Update in-memory cache
            keywords = updatedKeywords
            
            // Update keyword detection service
            if let detectionService = keywordDetectionService {
                await detectionService.updateKeywords(keywordTexts)
            }
            
            // Publish events
            eventBus.publish(KeywordsUpdatedEvent(keywords: keywords))
            
            print("✅ KeywordManager: Updated keyword '\(keyword.text)'")
        } catch {
            print("❌ KeywordManager: Failed to update keyword '\(keyword.text)': \(error)")
            throw error
        }
    }
    
    func updateKeywords(_ newKeywords: [Keyword]) async throws {
        do {
            // Save to data store
            try await dataManager.saveKeywords(newKeywords)
            
            // Update in-memory cache
            keywords = newKeywords
            
            // Update keyword detection service
            if let detectionService = keywordDetectionService {
                await detectionService.updateKeywords(keywordTexts)
            }
            
            // Publish events
            eventBus.publish(KeywordsUpdatedEvent(keywords: keywords))
            
            print("✅ KeywordManager: Updated \(newKeywords.count) keywords")
        } catch {
            print("❌ KeywordManager: Failed to update keywords: \(error)")
            throw error
        }
    }
    
    func validateKeyword(_ text: String) -> KeywordValidationResult {
        let trimmedText = text.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Check if empty
        if trimmedText.isEmpty {
            return .empty
        }
        
        // Check length
        if trimmedText.count < minKeywordLength {
            return .tooShort
        }
        
        if trimmedText.count > maxKeywordLength {
            return .tooLong
        }
        
        // Check for invalid characters
        if trimmedText.rangeOfCharacter(from: validCharacterSet.inverted) != nil {
            return .containsInvalidCharacters
        }
        
        // Check if already exists
        if keywords.contains(where: { $0.text.lowercased() == trimmedText.lowercased() }) {
            return .alreadyExists
        }
        
        return .valid
    }
    
    func searchKeywords(_ query: String) -> [Keyword] {
        let lowercaseQuery = query.lowercased()
        
        return keywords.filter { keyword in
            keyword.text.lowercased().contains(lowercaseQuery)
        }.sorted { $0.text.localizedCaseInsensitiveCompare($1.text) == .orderedAscending }
    }
    
    // MARK: - Private Methods
    
    private func setupEventSubscriptions() {
        // Listen for keyword detection events to update usage statistics
        eventBus.keywordDetected
            .sink { [weak self] event in
                Task { @MainActor in
                    await self?.handleKeywordDetected(event.keyword)
                }
            }
            .store(in: &cancellables)
    }
    
    private func handleKeywordDetected(_ detectedKeyword: String) async {
        // Update keyword usage statistics
        if let index = keywords.firstIndex(where: { $0.text == detectedKeyword }) {
            var keyword = keywords[index]
            keyword.lastUsed = Date()
            keyword.usageCount += 1
            
            do {
                try await updateKeyword(keyword)
            } catch {
                print("❌ KeywordManager: Failed to update keyword usage: \(error)")
            }
        }
    }
}

// MARK: - Keyword Errors

enum KeywordError: LocalizedError {
    case validationFailed(String)
    case keywordNotFound
    case dataStorageError(Error)
    case detectionServiceError(Error)
    
    var errorDescription: String? {
        switch self {
        case .validationFailed(let message):
            return "Keyword validation failed: \(message)"
        case .keywordNotFound:
            return "Keyword not found"
        case .dataStorageError(let error):
            return "Data storage error: \(error.localizedDescription)"
        case .detectionServiceError(let error):
            return "Keyword detection service error: \(error.localizedDescription)"
        }
    }
}

// MARK: - Keyword Extensions

extension Keyword {
    var isRecentlyUsed: Bool {
        guard let lastUsed = lastUsed else { return false }
        return Date().timeIntervalSince(lastUsed) < 86400 // 24 hours
    }
    
    var isFrequentlyUsed: Bool {
        return usageCount >= 10
    }
    
    var usageFrequency: KeywordUsageFrequency {
        switch usageCount {
        case 0:
            return .never
        case 1...5:
            return .rarely
        case 6...20:
            return .occasionally
        case 21...50:
            return .frequently
        default:
            return .veryFrequently
        }
    }
}

enum KeywordUsageFrequency: String, CaseIterable {
    case never = "Never"
    case rarely = "Rarely"
    case occasionally = "Occasionally"
    case frequently = "Frequently"
    case veryFrequently = "Very Frequently"
    
    var color: Color {
        switch self {
        case .never:
            return .gray
        case .rarely:
            return .blue
        case .occasionally:
            return .green
        case .frequently:
            return .orange
        case .veryFrequently:
            return .red
        }
    }
}
