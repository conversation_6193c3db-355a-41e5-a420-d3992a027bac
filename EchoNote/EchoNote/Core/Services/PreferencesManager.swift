//
//  PreferencesManager.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import Combine

// MARK: - Recording Quality

enum RecordingQuality: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    
    var displayName: String {
        switch self {
        case .low:
            return "Low Quality"
        case .medium:
            return "Medium Quality"
        case .high:
            return "High Quality"
        }
    }
    
    var sampleRate: Double {
        switch self {
        case .low:
            return 8000
        case .medium:
            return 22050
        case .high:
            return 44100
        }
    }
    
    var bitRate: Int {
        switch self {
        case .low:
            return 64000
        case .medium:
            return 128000
        case .high:
            return 256000
        }
    }
    
    var fileSize: String {
        switch self {
        case .low:
            return "~0.5 MB/min"
        case .medium:
            return "~1 MB/min"
        case .high:
            return "~2 MB/min"
        }
    }
}

// MARK: - Auto Stop Duration

enum AutoStopDuration: Int, CaseIterable, Codable {
    case seconds5 = 5
    case seconds10 = 10
    case seconds15 = 15
    case seconds30 = 30
    case minute1 = 60
    case minute2 = 120
    case never = -1
    
    var displayName: String {
        switch self {
        case .seconds5:
            return "5 seconds"
        case .seconds10:
            return "10 seconds"
        case .seconds15:
            return "15 seconds"
        case .seconds30:
            return "30 seconds"
        case .minute1:
            return "1 minute"
        case .minute2:
            return "2 minutes"
        case .never:
            return "Never"
        }
    }
    
    var timeInterval: TimeInterval? {
        return rawValue > 0 ? TimeInterval(rawValue) : nil
    }
}

// MARK: - Maximum Recording Duration

enum MaxRecordingDuration: Int, CaseIterable, Codable {
    case minute1 = 60
    case minute2 = 120
    case minute5 = 300
    case minute10 = 600
    case minute15 = 900
    case minute30 = 1800
    case unlimited = -1
    
    var displayName: String {
        switch self {
        case .minute1:
            return "1 minute"
        case .minute2:
            return "2 minutes"
        case .minute5:
            return "5 minutes"
        case .minute10:
            return "10 minutes"
        case .minute15:
            return "15 minutes"
        case .minute30:
            return "30 minutes"
        case .unlimited:
            return "Unlimited"
        }
    }
    
    var timeInterval: TimeInterval? {
        return rawValue > 0 ? TimeInterval(rawValue) : nil
    }
}

// MARK: - User Preferences

struct AppUserPreferences: Codable, Equatable {
    var recordingQuality: RecordingQuality
    var backgroundMonitoringEnabled: Bool
    var transcriptionEnabled: Bool
    var autoStopDuration: AutoStopDuration
    var maxRecordingDuration: MaxRecordingDuration
    var saveAudioEnabled: Bool
    var darkModeEnabled: Bool?
    var selectedTheme: String
    var animationSpeed: Double
    var notificationsEnabled: Bool
    var hapticFeedbackEnabled: Bool
    
    static let `default` = AppUserPreferences(
        recordingQuality: .medium,
        backgroundMonitoringEnabled: true,
        transcriptionEnabled: true,
        autoStopDuration: .seconds15,
        maxRecordingDuration: .minute10,
        saveAudioEnabled: true,
        darkModeEnabled: nil, // System default
        selectedTheme: "default",
        animationSpeed: 1.0,
        notificationsEnabled: true,
        hapticFeedbackEnabled: true
    )
}

// MARK: - Preferences Managing Protocol

protocol PreferencesManaging: ObservableObject {
    var preferences: AppUserPreferences { get }
    var preferencesPublisher: AnyPublisher<AppUserPreferences, Never> { get }
    
    // Recording preferences
    func updateRecordingQuality(_ quality: RecordingQuality) async
    func setBackgroundMonitoring(enabled: Bool) async
    func setTranscription(enabled: Bool) async
    func setAutoStopDuration(_ duration: AutoStopDuration) async
    func setMaxRecordingDuration(_ duration: MaxRecordingDuration) async
    func setSaveAudio(enabled: Bool) async
    
    // UI preferences
    func setDarkMode(enabled: Bool?) async
    func setTheme(_ theme: String) async
    func setAnimationSpeed(_ speed: Double) async
    
    // System preferences
    func setNotifications(enabled: Bool) async
    func setHapticFeedback(enabled: Bool) async
    
    // Bulk operations
    func updatePreferences(_ preferences: AppUserPreferences) async
    func resetToDefaults() async
}

// MARK: - Preferences Manager

@MainActor
class PreferencesManager: PreferencesManaging {
    
    // MARK: - Published Properties
    @Published private(set) var preferences: AppUserPreferences
    
    var preferencesPublisher: AnyPublisher<AppUserPreferences, Never> {
        $preferences.eraseToAnyPublisher()
    }
    
    // MARK: - Private Properties
    private let userDefaults: UserDefaults
    private let keywordDetectionService: KeywordDetectionServicing?
    private let eventBus = EventBus.shared
    private let preferencesKey = "appUserPreferences"
    private let legacyPreferencesKey = "userPreferences"
    
    // MARK: - Initialization
    
    init(
        userDefaults: UserDefaults = .standard,
        keywordDetectionService: KeywordDetectionServicing? = nil
    ) {
        self.userDefaults = userDefaults
        self.keywordDetectionService = keywordDetectionService
        
        // Load preferences with migration support
        self.preferences = Self.loadPreferences(from: userDefaults)
        
        // Save preferences to ensure they're persisted in the new format
        savePreferences()
        
        print("✅ PreferencesManager: Initialized with preferences")
    }
    
    // MARK: - Recording Preferences
    
    func updateRecordingQuality(_ quality: RecordingQuality) async {
        preferences.recordingQuality = quality
        savePreferences()
        
        print("🎵 PreferencesManager: Updated recording quality to \(quality.displayName)")
    }
    
    func setBackgroundMonitoring(enabled: Bool) async {
        let wasEnabled = preferences.backgroundMonitoringEnabled
        preferences.backgroundMonitoringEnabled = enabled
        savePreferences()
        
        // Update keyword detection service if state changed
        if wasEnabled != enabled {
            if enabled {
                // Background monitoring will be started by the coordinator when needed
                print("🎯 PreferencesManager: Enabled background monitoring")
            } else {
                await keywordDetectionService?.stopMonitoring()
                print("🛑 PreferencesManager: Disabled background monitoring")
            }
        }
    }
    
    func setTranscription(enabled: Bool) async {
        preferences.transcriptionEnabled = enabled
        savePreferences()
        
        print("📝 PreferencesManager: \(enabled ? "Enabled" : "Disabled") transcription")
    }
    
    func setAutoStopDuration(_ duration: AutoStopDuration) async {
        preferences.autoStopDuration = duration
        savePreferences()
        
        print("⏱️ PreferencesManager: Updated auto-stop duration to \(duration.displayName)")
    }
    
    func setMaxRecordingDuration(_ duration: MaxRecordingDuration) async {
        preferences.maxRecordingDuration = duration
        savePreferences()
        
        print("⏰ PreferencesManager: Updated max recording duration to \(duration.displayName)")
    }
    
    func setSaveAudio(enabled: Bool) async {
        preferences.saveAudioEnabled = enabled
        savePreferences()
        
        print("💾 PreferencesManager: \(enabled ? "Enabled" : "Disabled") audio saving")
    }
    
    // MARK: - UI Preferences
    
    func setDarkMode(enabled: Bool?) async {
        preferences.darkModeEnabled = enabled
        savePreferences()
        
        let modeDescription = enabled == nil ? "system" : (enabled! ? "dark" : "light")
        print("🌙 PreferencesManager: Updated dark mode to \(modeDescription)")
    }
    
    func setTheme(_ theme: String) async {
        preferences.selectedTheme = theme
        savePreferences()
        
        print("🎨 PreferencesManager: Updated theme to \(theme)")
    }
    
    func setAnimationSpeed(_ speed: Double) async {
        preferences.animationSpeed = max(0.1, min(3.0, speed)) // Clamp between 0.1 and 3.0
        savePreferences()
        
        print("⚡ PreferencesManager: Updated animation speed to \(preferences.animationSpeed)")
    }
    
    // MARK: - System Preferences
    
    func setNotifications(enabled: Bool) async {
        preferences.notificationsEnabled = enabled
        savePreferences()
        
        print("🔔 PreferencesManager: \(enabled ? "Enabled" : "Disabled") notifications")
    }
    
    func setHapticFeedback(enabled: Bool) async {
        preferences.hapticFeedbackEnabled = enabled
        savePreferences()
        
        print("📳 PreferencesManager: \(enabled ? "Enabled" : "Disabled") haptic feedback")
    }
    
    // MARK: - Bulk Operations
    
    func updatePreferences(_ newPreferences: AppUserPreferences) async {
        let oldPreferences = preferences
        preferences = newPreferences
        savePreferences()
        
        // Handle background monitoring state change
        if oldPreferences.backgroundMonitoringEnabled != newPreferences.backgroundMonitoringEnabled {
            if newPreferences.backgroundMonitoringEnabled {
                print("🎯 PreferencesManager: Background monitoring enabled via bulk update")
            } else {
                await keywordDetectionService?.stopMonitoring()
                print("🛑 PreferencesManager: Background monitoring disabled via bulk update")
            }
        }
        
        print("📦 PreferencesManager: Updated all preferences")
    }
    
    func resetToDefaults() async {
        let wasBackgroundMonitoringEnabled = preferences.backgroundMonitoringEnabled
        preferences = .default
        savePreferences()
        
        // Handle background monitoring state change
        if wasBackgroundMonitoringEnabled && !preferences.backgroundMonitoringEnabled {
            await keywordDetectionService?.stopMonitoring()
        }
        
        print("🔄 PreferencesManager: Reset to default preferences")
    }
    
    // MARK: - Private Methods
    
    private func savePreferences() {
        do {
            let data = try JSONEncoder().encode(preferences)
            userDefaults.set(data, forKey: preferencesKey)
            
            // Publish preferences update event
            eventBus.publish(PreferencesUpdatedEvent(preferences: preferences))
        } catch {
            print("❌ PreferencesManager: Failed to save preferences: \(error)")
            eventBus.publishError(error: error, context: "PreferencesManager.savePreferences")
        }
    }
    
    private static func loadPreferences(from userDefaults: UserDefaults) -> AppUserPreferences {
        // Try to load from new format first
        if let data = userDefaults.data(forKey: "appUserPreferences"),
           let preferences = try? JSONDecoder().decode(AppUserPreferences.self, from: data) {
            print("✅ PreferencesManager: Loaded preferences from new format")
            return preferences
        }
        
        // Try to migrate from legacy format
        if let legacyPreferences = migrateLegacyPreferences(from: userDefaults) {
            print("🔄 PreferencesManager: Migrated preferences from legacy format")
            return legacyPreferences
        }
        
        // Use defaults
        print("🆕 PreferencesManager: Using default preferences")
        return .default
    }
    
    private static func migrateLegacyPreferences(from userDefaults: UserDefaults) -> AppUserPreferences? {
        // Try to load legacy UserPreferences format
        if let data = userDefaults.data(forKey: "userPreferences"),
           let legacyPrefs = try? JSONDecoder().decode(LegacyUserPreferences.self, from: data) {
            
            return AppUserPreferences(
                recordingQuality: .medium, // Default for legacy
                backgroundMonitoringEnabled: legacyPrefs.backgroundMonitorEnabled,
                transcriptionEnabled: true, // Default for legacy
                autoStopDuration: .seconds15, // Default for legacy
                maxRecordingDuration: .minute10, // Default for legacy
                saveAudioEnabled: true, // Default for legacy
                darkModeEnabled: legacyPrefs.darkModeEnabled,
                selectedTheme: legacyPrefs.selectedTheme ?? "default",
                animationSpeed: 1.0, // Default for legacy
                notificationsEnabled: true, // Default for legacy
                hapticFeedbackEnabled: true // Default for legacy
            )
        }
        
        return nil
    }
}

// MARK: - Legacy Preferences Support

private struct LegacyUserPreferences: Codable {
    let backgroundMonitorEnabled: Bool
    let darkModeEnabled: Bool?
    let selectedTheme: String?
}

// MARK: - Preferences Events

struct PreferencesUpdatedEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let preferences: AppUserPreferences
    
    init(preferences: AppUserPreferences) {
        self.timestamp = Date()
        self.eventId = UUID()
        self.preferences = preferences
    }
}

// MARK: - Preferences Extensions

extension AppUserPreferences {
    var isDarkModeEnabled: Bool {
        return darkModeEnabled ?? false // Default to system preference
    }
    
    var effectiveAnimationDuration: Double {
        return 0.3 * animationSpeed
    }
    
    var shouldShowAnimations: Bool {
        return animationSpeed > 0.1
    }
}
