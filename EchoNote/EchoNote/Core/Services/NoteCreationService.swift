//
//  NoteCreationService.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import SwiftData

// MARK: - Creation Models

struct NoteCreationRequest {
    let title: String?
    let content: String
    let keywords: [String]
    let audioURL: String?
    let duration: TimeInterval
    let source: NoteSource
    
    enum NoteSource {
        case manual
        case background
        case siri
    }
}

struct NoteCreationResult {
    let note: Note
    let success: Bool
    let error: Error?
}

// MARK: - Data Managing Protocol (using unified definition)

// MARK: - Service Protocol

protocol NoteCreationServicing {
    func createNote(from recording: RecordingResult, transcription: String) async throws -> Note
    func createNote(from request: NoteCreationRequest) async throws -> Note
    func createTextNote(title: String, content: String, keywords: [String]) async throws -> Note
    func saveNote(_ note: Note) async throws
    func deleteNote(_ note: Note) async throws
    func updateNote(_ note: Note) async throws
}

// Legacy protocol for backward compatibility
typealias NoteCreationServiceProtocol = NoteCreationServicing

// MARK: - Implementation

@MainActor
class NoteCreationService: NoteCreationServicing {

    // MARK: - Private Properties
    private let dataManager: DataManaging
    private let keywordManager: KeywordManagerProtocol
    private let userPreferences: UserPreferencesService
    private let fileManager: FileManager

    // MARK: - Initialization
    init(
        dataManager: DataManaging,
        keywordManager: KeywordManagerProtocol = KeywordManager.shared,
        userPreferences: UserPreferencesService = UserPreferencesService.shared,
        fileManager: FileManager = .default
    ) {
        self.dataManager = dataManager
        self.keywordManager = keywordManager
        self.userPreferences = userPreferences
        self.fileManager = fileManager
    }

    convenience init(dataManager: DataManager) {
        self.init(dataManager: dataManager as DataManaging)
    }
    
    // MARK: - Public Methods

    func createNote(from recording: RecordingResult, transcription: String) async throws -> Note {
        let keywords = extractKeywords(from: transcription)

        // Generate meaningful title from transcription
        let title = generateTitle(from: transcription)

        // Determine if audio should be included based on user preferences
        let preferences = await userPreferences.getCurrentPreferences()
        let shouldIncludeAudio = preferences?.recordAudioWhenCreatingNote ?? true
        let finalAudioURL = shouldIncludeAudio ? recording.audioURL.path : nil

        // Create note with proper metadata
        let note = Note(
            title: title,
            content: transcription,
            audioURL: finalAudioURL,
            duration: shouldIncludeAudio ? recording.duration : 0,
            createdAt: recording.session.startTime,
            modifiedAt: Date(),
            isFavorite: false,
            keywords: keywords
        )

        // Save the note
        try await saveNote(note)

        // Handle audio file management
        if shouldIncludeAudio {
            try await moveAudioFileToFinalLocation(from: recording.audioURL, for: note)
        } else {
            // Clean up temporary audio file if not needed
            try? fileManager.removeItem(at: recording.audioURL)
        }

        print("✅ NoteCreationService: Created note '\(title)' with \(keywords.count) keywords")
        return note
    }

    func createNote(from recordingResult: RecordingResult, keywords: [String]) async throws -> Note {
        let preferences = await userPreferences.getCurrentPreferences()
        
        // Determine if audio should be included
        let shouldIncludeAudio = preferences?.recordAudioWhenCreatingNote ?? true
        let finalAudioURL = shouldIncludeAudio ? recordingResult.audioURL.path : nil
        
        // Generate title if not provided
        let title = generateTitle(
            from: recordingResult.transcription,
            keywords: keywords,
            source: mapRecordingModeToSource(recordingResult.session.mode)
        )
        
        let request = NoteCreationRequest(
            title: title,
            content: recordingResult.transcription,
            keywords: keywords,
            audioURL: finalAudioURL,
            duration: shouldIncludeAudio ? recordingResult.duration : 0,
            source: mapRecordingModeToSource(recordingResult.session.mode)
        )
        
        return try await createNote(from: request)
    }
    
    func createNote(from request: NoteCreationRequest) async throws -> Note {
        // Validate content
        guard !request.content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw NoteCreationError.emptyContent
        }
        
        // Find or create keyword objects
        let keywordObjects = try await findOrCreateKeywords(request.keywords)
        
        // Generate final title
        let finalTitle = request.title ?? generateTitle(
            from: request.content,
            keywords: request.keywords,
            source: request.source
        )
        
        // Create note using DataManager
        let note = dataManager.createNote(
            title: finalTitle,
            content: request.content,
            audioURL: request.audioURL,
            keywords: keywordObjects
        )
        
        // Set duration if provided
        if request.duration > 0 {
            note.duration = request.duration
        }
        
        // Update keyword usage statistics
        for keyword in keywordObjects {
            keyword.incrementUsage()
        }
        
        // Log creation for analytics
        logNoteCreation(note: note, source: request.source)
        
        return note
    }
    
    func createTextNote(title: String, content: String, keywords: [String]) async throws -> Note {
        let request = NoteCreationRequest(
            title: title,
            content: content,
            keywords: keywords,
            audioURL: nil,
            duration: 0,
            source: .siri
        )
        
        return try await createNote(from: request)
    }
    
    func updateNote(_ note: Note, with content: String) async throws {
        guard !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw NoteCreationError.emptyContent
        }
        
        note.content = content
        note.updateModifiedDate()
        
        // Save changes
        do {
            try dataManager.modelContext.save()
        } catch {
            throw NoteCreationError.saveFailed(underlying: error)
        }
    }
    
    func deleteNote(_ note: Note) async throws {
        // Clean up audio file if exists
        if let audioURL = note.audioURL {
            try? FileManager.default.removeItem(atPath: audioURL)
        }
        
        // Remove note
        dataManager.modelContext.delete(note)
        
        // Save changes
        do {
            try dataManager.modelContext.save()
        } catch {
            throw NoteCreationError.deleteFailed(underlying: error)
        }
    }
}

// MARK: - Private Methods
private extension NoteCreationService {
    
    func findOrCreateKeywords(_ keywordTexts: [String]) async throws -> [Keyword] {
        var keywords: [Keyword] = []
        
        for keywordText in keywordTexts {
            let keyword = dataManager.findOrCreateKeyword(text: keywordText)
            keywords.append(keyword)
        }
        
        return keywords
    }
    
    func generateTitle(from content: String, keywords: [String], source: NoteCreationRequest.NoteSource) -> String {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .short)
        
        // Use first keyword if available
        if let firstKeyword = keywords.first {
            return "\(firstKeyword.capitalized) - \(timestamp)"
        }
        
        // Extract first few words from content
        let words = content.components(separatedBy: .whitespacesAndNewlines)
            .filter { !$0.isEmpty }
        
        if words.count >= 3 {
            let firstWords = Array(words.prefix(3)).joined(separator: " ")
            return "\(firstWords) - \(timestamp)"
        } else if !words.isEmpty {
            return "\(words.first!) - \(timestamp)"
        }
        
        // Fallback based on source
        switch source {
        case .manual:
            return "Voice Note - \(timestamp)"
        case .background:
            return "Auto Note - \(timestamp)"
        case .siri:
            return "Siri Note - \(timestamp)"
        }
    }
    
    func mapRecordingModeToSource(_ mode: RecordingSession.RecordingMode) -> NoteCreationRequest.NoteSource {
        switch mode {
        case .manual:
            return .manual
        case .background:
            return .background
        case .siri:
            return .siri
        }
    }
    
    func logNoteCreation(note: Note, source: NoteCreationRequest.NoteSource) {
        let metadata: [String: Any] = [
            "noteId": note.id.uuidString,
            "source": String(describing: source),
            "hasAudio": note.audioURL != nil,
            "duration": note.duration,
            "keywordCount": note.keywords.count,
            "contentLength": note.content.count
        ]
        
        LoggingService.shared.info(
            "Note created",
            category: .userAction,
            metadata: metadata
        )
        
        // Track performance metrics
        PerformanceMonitoringService.shared.recordEvent(
            .noteCreation,
            metadata: metadata
        )
    }

    // MARK: - Additional Service Methods

    func saveNote(_ note: Note) async throws {
        dataManager.insert(note)
        try await dataManager.save()
        print("💾 NoteCreationService: Saved note '\(note.title)'")
    }

    func deleteNote(_ note: Note) async throws {
        // Clean up audio file if it exists
        if let audioURL = note.audioURL {
            let url = URL(fileURLWithPath: audioURL)
            try? fileManager.removeItem(at: url)
        }

        try await dataManager.delete(note)
        print("🗑️ NoteCreationService: Deleted note '\(note.title)'")
    }

    func updateNote(_ note: Note) async throws {
        note.modifiedAt = Date()
        try await dataManager.save()
        print("✅ NoteCreationService: Updated note '\(note.title)'")
    }

    // MARK: - Private Helper Methods

    private func generateTitle(from transcription: String) -> String {
        // Extract meaningful title from transcription content
        let words = transcription.split(separator: " ")
            .prefix(5)
            .map { String($0) }
            .filter { !$0.isEmpty }

        if words.isEmpty {
            return "Untitled Note"
        }

        let title = words.joined(separator: " ")
        return title.count > 50 ? String(title.prefix(47)) + "..." : title
    }

    private func extractKeywords(from transcription: String) -> [String] {
        // Simple keyword extraction - in a real implementation,
        // this could use NLP techniques or predefined keyword matching
        let words = transcription.lowercased()
            .components(separatedBy: .whitespacesAndNewlines)
            .filter { $0.count > 3 } // Filter out short words
            .prefix(5) // Limit to 5 keywords

        return Array(words)
    }

    private func moveAudioFileToFinalLocation(from tempURL: URL, for note: Note) async throws {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let audioDirectory = documentsPath.appendingPathComponent("Audio")

        // Create audio directory if it doesn't exist
        try fileManager.createDirectory(at: audioDirectory, withIntermediateDirectories: true)

        let finalURL = audioDirectory.appendingPathComponent("\(note.id.uuidString).m4a")

        // Move file to final location
        try fileManager.moveItem(at: tempURL, to: finalURL)

        // Update note with final audio URL
        note.audioURL = finalURL.path
    }
}

// MARK: - Supporting Services

protocol UserPreferencesService {
    func getCurrentPreferences() async -> UserPreferences?
}

extension UserPreferencesService {
    static let shared: UserPreferencesService = UserPreferencesServiceImpl()
}

private class UserPreferencesServiceImpl: UserPreferencesService {
    func getCurrentPreferences() async -> UserPreferences? {
        // This would be implemented to fetch current user preferences
        // For now, return nil to use defaults
        return nil
    }
}

protocol KeywordManagerProtocol {
    func findOrCreateKeyword(text: String) async throws -> Keyword
    func getAllKeywords() async -> [Keyword]
}

extension KeywordManagerProtocol {
    static var shared: KeywordManagerProtocol {
        // This would return the actual KeywordManager instance
        fatalError("KeywordManager.shared not implemented")
    }
}

// MARK: - DataManager Extension

extension DataManager: DataManaging {
    func insert<T: PersistentModel>(_ model: T) async {
        modelContext.insert(model)
    }

    func fetch<T: PersistentModel>(_ type: T.Type) async throws -> [T] {
        let descriptor = FetchDescriptor<T>()
        return try modelContext.fetch(descriptor)
    }
}

// MARK: - Errors
enum NoteCreationError: LocalizedError {
    case emptyContent
    case keywordNotFound(String)
    case saveFailed(underlying: Error)
    case deleteFailed(underlying: Error)
    case invalidData
    
    var errorDescription: String? {
        switch self {
        case .emptyContent:
            return "Note content cannot be empty"
        case .keywordNotFound(let keyword):
            return "Keyword '\(keyword)' not found"
        case .saveFailed(let error):
            return "Failed to save note: \(error.localizedDescription)"
        case .deleteFailed(let error):
            return "Failed to delete note: \(error.localizedDescription)"
        case .invalidData:
            return "Invalid note data provided"
        }
    }
}
