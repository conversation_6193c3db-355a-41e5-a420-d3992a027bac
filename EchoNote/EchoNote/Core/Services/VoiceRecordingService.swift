//
//  VoiceRecordingService.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import AVFoundation
import Speech
import Combine

// MARK: - Recording Models

struct RecordingSession {
    let id: UUID
    let startTime: Date
    let audioURL: URL
    let mode: RecordingMode
    
    enum RecordingMode {
        case manual
        case background
        case siri
    }
}

struct RecordingResult {
    let session: RecordingSession
    let duration: TimeInterval
    let transcription: String
    let audioURL: URL
    let audioLevels: [Float]
}

// MARK: - Recording State (using unified definition from RecordingCoordinator)

// MARK: - Service Protocol

protocol VoiceRecordingServicing: ObservableObject {
    var isRecording: Bool { get }
    var recordingState: CurrentValueSubject<RecordingState, Never> { get }
    var currentSession: RecordingSession? { get }
    var recordingDuration: TimeInterval { get }
    var transcribedText: String { get }
    var audioLevels: [Float] { get }
    var recordingProgress: AnyPublisher<RecordingProgress, Never> { get }

    func startRecording(mode: RecordingSession.RecordingMode) async throws -> RecordingSession
    func stopRecording() async throws -> RecordingResult
    func pauseRecording() async throws
    func resumeRecording() async throws
    func cancelRecording() async
    func transcribeAudio(from url: URL) async throws -> String
}

// Legacy protocol for backward compatibility
typealias VoiceRecordingServiceProtocol = VoiceRecordingServicing

struct RecordingProgress {
    let duration: TimeInterval
    let transcription: String
    let audioLevels: [Float]
    let isActive: Bool
}

// MARK: - Implementation

@MainActor
class VoiceRecordingService: NSObject, VoiceRecordingServicing {

    // MARK: - Published Properties
    @Published private(set) var isRecording = false
    @Published private(set) var currentSession: RecordingSession?
    @Published private(set) var recordingDuration: TimeInterval = 0
    @Published private(set) var transcribedText = ""
    @Published private(set) var audioLevels: [Float] = []

    // MARK: - State Management
    let recordingState = CurrentValueSubject<RecordingState, Never>(.idle)

    // MARK: - Private Properties
    private var audioRecorder: AVAudioRecorder?
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var audioEngine: AVAudioEngine?

    private var recordingTimer: Timer?
    private var levelTimer: Timer?
    private let progressSubject = PassthroughSubject<RecordingProgress, Never>()
    private var cancellables = Set<AnyCancellable>()

    private let audioSessionManager: AudioSessionManaging
    private let permissionsService: PermissionsServiceProtocol
    
    // MARK: - Computed Properties
    var recordingProgress: AnyPublisher<RecordingProgress, Never> {
        progressSubject.eraseToAnyPublisher()
    }
    
    // MARK: - Initialization
    init(
        audioSessionManager: AudioSessionManaging,
        permissionsService: PermissionsServiceProtocol
    ) {
        self.audioSessionManager = audioSessionManager
        self.permissionsService = permissionsService
        super.init()
        setupSpeechRecognizer()
        setupStateBindings()
    }

    convenience init() {
        self.init(
            audioSessionManager: AudioSessionManager.shared,
            permissionsService: PermissionsService.shared
        )
    }
    
    // MARK: - Public Methods
    func startRecording(mode: RecordingSession.RecordingMode) async throws -> RecordingSession {
        guard !isRecording else {
            updateRecordingState(.failed(RecordingError.alreadyRecording))
            throw RecordingError.alreadyRecording
        }

        // Request permissions
        let hasPermissions = await permissionsService.requestAllPermissions()
        guard hasPermissions else {
            updateRecordingState(.failed(RecordingError.permissionDenied))
            throw RecordingError.permissionDenied
        }

        // Setup audio session
        let recordingMode: RecordingMode = mode == .background ? .backgroundMonitoring : .manual
        try await audioSessionManager.configureSession(for: recordingMode)

        // Create session
        let session = RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: generateAudioURL(),
            mode: mode
        )

        // Start recording
        try setupAudioRecorder(url: session.audioURL)
        try startSpeechRecognition()

        currentSession = session
        isRecording = true
        recordingDuration = 0
        transcribedText = ""
        audioLevels = []

        updateRecordingState(.recording(session: session))
        startTimers()

        return session
    }

    func transcribeAudio(from url: URL) async throws -> String {
        guard let speechRecognizer = speechRecognizer,
              speechRecognizer.isAvailable else {
            throw RecordingError.speechRecognitionUnavailable
        }

        updateRecordingState(.processing)

        return try await withCheckedThrowingContinuation { continuation in
            let request = SFSpeechURLRecognitionRequest(url: url)
            request.shouldReportPartialResults = false

            speechRecognizer.recognitionTask(with: request) { result, error in
                if let error = error {
                    continuation.resume(throwing: error)
                    return
                }

                if let result = result, result.isFinal {
                    continuation.resume(returning: result.bestTranscription.formattedString)
                }
            }
        }
    }
    
    func stopRecording() async throws -> RecordingResult {
        guard let session = currentSession, isRecording else {
            updateRecordingState(.error("Not recording"))
            throw RecordingError.notRecording
        }

        updateRecordingState(.processing)

        stopTimers()
        stopSpeechRecognition()
        audioRecorder?.stop()

        let result = RecordingResult(
            session: session,
            duration: recordingDuration,
            transcription: transcribedText,
            audioURL: session.audioURL,
            audioLevels: audioLevels
        )

        // Reset state
        isRecording = false
        currentSession = nil
        updateRecordingState(.idle)

        return result
    }
    
    func pauseRecording() async throws {
        guard isRecording else {
            throw RecordingError.notRecording
        }
        
        audioRecorder?.pause()
        stopTimers()
        
        // Note: Speech recognition continues during pause
    }
    
    func resumeRecording() async throws {
        guard let session = currentSession, !isRecording else {
            throw RecordingError.invalidState
        }
        
        audioRecorder?.record()
        startTimers()
    }
    
    func cancelRecording() async {
        stopTimers()
        stopSpeechRecognition()
        audioRecorder?.stop()

        // Clean up audio file
        if let session = currentSession {
            try? FileManager.default.removeItem(at: session.audioURL)
        }

        // Reset state
        isRecording = false
        currentSession = nil
        recordingDuration = 0
        transcribedText = ""
        audioLevels = []
        updateRecordingState(.idle)
    }
}

// MARK: - Private Methods
private extension VoiceRecordingService {
    
    func setupSpeechRecognizer() {
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "en-US"))
        speechRecognizer?.delegate = self
    }
    
    func setupAudioRecorder(url: URL) throws {
        let settings: [String: Any] = [
            AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
            AVSampleRateKey: 44100,
            AVNumberOfChannelsKey: 1,
            AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
        ]
        
        audioRecorder = try AVAudioRecorder(url: url, settings: settings)
        audioRecorder?.isMeteringEnabled = true
        audioRecorder?.prepareToRecord()
        audioRecorder?.record()
    }
    
    func startSpeechRecognition() throws {
        guard let speechRecognizer = speechRecognizer,
              speechRecognizer.isAvailable else {
            throw RecordingError.speechRecognitionUnavailable
        }
        
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            throw RecordingError.speechRecognitionSetupFailed
        }
        
        recognitionRequest.shouldReportPartialResults = true
        
        audioEngine = AVAudioEngine()
        let inputNode = audioEngine!.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)
        
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)
        }
        
        audioEngine!.prepare()
        try audioEngine!.start()
        
        recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { [weak self] result, error in
            DispatchQueue.main.async {
                if let result = result {
                    self?.transcribedText = result.bestTranscription.formattedString
                }
                
                if error != nil || result?.isFinal == true {
                    self?.stopSpeechRecognition()
                }
            }
        }
    }
    
    func stopSpeechRecognition() {
        audioEngine?.stop()
        audioEngine?.inputNode.removeTap(onBus: 0)
        recognitionRequest?.endAudio()
        recognitionTask?.cancel()
        
        audioEngine = nil
        recognitionRequest = nil
        recognitionTask = nil
    }
    
    func startTimers() {
        recordingTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            self?.updateRecordingDuration()
        }
        
        levelTimer = Timer.scheduledTimer(withTimeInterval: 0.05, repeats: true) { [weak self] _ in
            self?.updateAudioLevels()
        }
    }
    
    func stopTimers() {
        recordingTimer?.invalidate()
        levelTimer?.invalidate()
        recordingTimer = nil
        levelTimer = nil
    }
    
    func updateRecordingDuration() {
        guard let session = currentSession else { return }
        recordingDuration = Date().timeIntervalSince(session.startTime)
        updateRecordingState(.recording(duration: recordingDuration))
        publishProgress()
    }

    func updateRecordingState(_ newState: RecordingState) {
        recordingState.send(newState)
    }

    func setupStateBindings() {
        // Bind recording state changes to published properties
        recordingState
            .sink { [weak self] state in
                switch state {
                case .idle:
                    self?.isRecording = false
                case .recording:
                    self?.isRecording = true
                case .processing:
                    self?.isRecording = false
                case .failed:
                    self?.isRecording = false
                }
            }
            .store(in: &cancellables)
    }
    
    func updateAudioLevels() {
        audioRecorder?.updateMeters()
        let level = audioRecorder?.averagePower(forChannel: 0) ?? -160
        let normalizedLevel = max(0, (level + 160) / 160)
        
        audioLevels.append(normalizedLevel)
        if audioLevels.count > 100 {
            audioLevels.removeFirst()
        }
        
        publishProgress()
    }
    
    func publishProgress() {
        let progress = RecordingProgress(
            duration: recordingDuration,
            transcription: transcribedText,
            audioLevels: audioLevels,
            isActive: isRecording
        )
        progressSubject.send(progress)
    }
    
    func generateAudioURL() -> URL {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioFilename = "recording_\(Date().timeIntervalSince1970).m4a"
        return documentsPath.appendingPathComponent(audioFilename)
    }
}

// MARK: - SFSpeechRecognizerDelegate
extension VoiceRecordingService: SFSpeechRecognizerDelegate {
    func speechRecognizer(_ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool) {
        if !available && isRecording {
            Task {
                await cancelRecording()
            }
        }
    }
}

// MARK: - Errors
enum RecordingError: LocalizedError {
    case alreadyRecording
    case notRecording
    case permissionDenied
    case speechRecognitionUnavailable
    case speechRecognitionSetupFailed
    case invalidState
    case audioSessionSetupFailed
    
    var errorDescription: String? {
        switch self {
        case .alreadyRecording:
            return "Recording is already in progress"
        case .notRecording:
            return "No recording in progress"
        case .permissionDenied:
            return "Microphone or speech recognition permission denied"
        case .speechRecognitionUnavailable:
            return "Speech recognition is not available"
        case .speechRecognitionSetupFailed:
            return "Failed to setup speech recognition"
        case .invalidState:
            return "Invalid recording state"
        case .audioSessionSetupFailed:
            return "Failed to setup audio session"
        }
    }
}
