//
//  BackgroundMonitoringMigrationService.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import Combine
import SwiftData

// MARK: - Background Monitoring Migration Service

@MainActor
class BackgroundMonitoringMigrationService: ObservableObject {
    
    // MARK: - Singleton
    static let shared = BackgroundMonitoringMigrationService()
    
    // MARK: - Published Properties
    @Published private(set) var isMonitoring = false
    @Published private(set) var isStarting = false
    @Published private(set) var lastError: String?
    @Published private(set) var detectedKeywords: [String] = []
    @Published private(set) var isRecordingAfterKeyword = false
    @Published private(set) var keywordDetectionEnabled = true
    
    // MARK: - Private Properties
    private var recordingCoordinator: RecordingCoordinator?
    private var keywordDetectionService: KeywordDetectionServicing?
    private var dataManager: DataManaging?
    private var cancellables = Set<AnyCancellable>()
    
    // Session state management
    private var activeSessionKeyword: String?
    private var sessionInProgress = false
    
    // Cooldown mechanism
    private var lastRecordingEndTime: Date?
    private let cooldownPeriod: TimeInterval = 2.0
    
    // App lifecycle management
    private var wasMonitoringBeforeBackground = false
    
    // Health check timer
    private var healthCheckTimer: Timer?
    private var lastSpeechRecognitionTime: Date?
    
    // MARK: - Initialization
    private init() {
        setupAppLifecycleObservers()
    }
    
    // MARK: - Public Methods
    
    func initialize(with dataManager: DataManaging) async {
        self.dataManager = dataManager
        
        // Get services from ServiceManager
        if let serviceManager = ServiceManager.shared as? ServiceManager {
            recordingCoordinator = await serviceManager.getRecordingCoordinator()
            keywordDetectionService = await serviceManager.getKeywordDetectionService()
            
            setupServiceBindings()
        }
    }
    
    func setDataManager(_ dataManager: DataManaging) {
        self.dataManager = dataManager
    }
    
    func startMonitoring(keywords: [String] = []) async {
        guard !isMonitoring && !isStarting else {
            print("⚠️ BackgroundMonitoringMigrationService: Already monitoring or starting")
            return
        }
        
        isStarting = true
        lastError = nil
        
        do {
            // Use the new KeywordDetectionService through RecordingCoordinator
            let keywordsToUse = keywords.isEmpty ? await getStoredKeywords() : keywords
            
            if let coordinator = recordingCoordinator {
                try await coordinator.enableBackgroundMonitoring(keywords: keywordsToUse)
                
                isMonitoring = true
                isStarting = false
                detectedKeywords = keywordsToUse
                
                print("✅ BackgroundMonitoringMigrationService: Started monitoring with new architecture")
            } else {
                throw BackgroundMonitoringError.serviceUnavailable
            }
        } catch {
            isStarting = false
            lastError = error.localizedDescription
            print("❌ BackgroundMonitoringMigrationService: Failed to start monitoring: \(error)")
        }
    }
    
    func stopMonitoring() async {
        guard isMonitoring else { return }
        
        await recordingCoordinator?.disableBackgroundMonitoring()
        
        isMonitoring = false
        isStarting = false
        detectedKeywords = []
        activeSessionKeyword = nil
        sessionInProgress = false
        
        print("🛑 BackgroundMonitoringMigrationService: Stopped monitoring")
    }
    
    func updateKeywords(_ keywords: [String]) async {
        detectedKeywords = keywords
        
        if isMonitoring {
            // Restart monitoring with new keywords
            await stopMonitoring()
            await startMonitoring(keywords: keywords)
        }
    }
    
    // MARK: - Legacy Compatibility Methods
    
    func startMonitoring() {
        Task {
            await startMonitoring(keywords: [])
        }
    }
    
    func requestPermissions() async -> Bool {
        // Delegate to PermissionsService through ServiceManager
        if let permissionsService = await ServiceManager.shared.getPermissionsService() {
            return await permissionsService.requestAllPermissions()
        }
        return false
    }
    
    // MARK: - Private Methods
    
    private func setupServiceBindings() {
        // Bind to KeywordDetectionService state
        keywordDetectionService?.detectionState
            .sink { [weak self] state in
                switch state {
                case .inactive:
                    self?.isMonitoring = false
                case .monitoring:
                    self?.isMonitoring = true
                case .keywordDetected(let keyword):
                    self?.handleKeywordDetected(keyword)
                case .failed(let error):
                    self?.lastError = error.localizedDescription
                    self?.isMonitoring = false
                }
            }
            .store(in: &cancellables)
        
        // Bind to RecordingCoordinator state
        recordingCoordinator?.recordingState
            .sink { [weak self] state in
                switch state {
                case .recording:
                    self?.isRecordingAfterKeyword = true
                case .idle, .processing:
                    self?.isRecordingAfterKeyword = false
                    self?.sessionInProgress = false
                    self?.activeSessionKeyword = nil
                case .listening:
                    self?.isRecordingAfterKeyword = false
                case .error:
                    self?.isRecordingAfterKeyword = false
                    self?.sessionInProgress = false
                }
            }
            .store(in: &cancellables)
    }
    
    private func handleKeywordDetected(_ keyword: String) {
        // Apply cooldown logic
        if let lastEndTime = lastRecordingEndTime,
           Date().timeIntervalSince(lastEndTime) < cooldownPeriod {
            print("🔄 BackgroundMonitoringMigrationService: Keyword '\(keyword)' detected but in cooldown period")
            return
        }
        
        // Prevent multiple sessions
        guard !sessionInProgress else {
            print("⚠️ BackgroundMonitoringMigrationService: Session already in progress, ignoring keyword '\(keyword)'")
            return
        }
        
        activeSessionKeyword = keyword
        sessionInProgress = true
        
        print("🎯 BackgroundMonitoringMigrationService: Keyword '\(keyword)' detected, starting recording session")
        
        // The RecordingCoordinator will handle the actual recording
        Task {
            do {
                try await recordingCoordinator?.handleKeywordDetection(keyword: keyword)
            } catch {
                print("❌ BackgroundMonitoringMigrationService: Failed to handle keyword detection: \(error)")
                sessionInProgress = false
                activeSessionKeyword = nil
            }
        }
    }
    
    private func getStoredKeywords() async -> [String] {
        guard let dataManager = dataManager else { return [] }
        
        do {
            let keywords = try await dataManager.fetchKeywords()
            return keywords.map { $0.text }
        } catch {
            print("❌ BackgroundMonitoringMigrationService: Failed to fetch keywords: \(error)")
            return []
        }
    }
    
    private func setupAppLifecycleObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
    }
    
    @objc private func appDidEnterBackground() {
        wasMonitoringBeforeBackground = isMonitoring
        print("📱 BackgroundMonitoringMigrationService: App entered background, monitoring: \(isMonitoring)")
    }
    
    @objc private func appWillEnterForeground() {
        print("📱 BackgroundMonitoringMigrationService: App entering foreground, was monitoring: \(wasMonitoringBeforeBackground)")
        
        if wasMonitoringBeforeBackground && !isMonitoring {
            // Restart monitoring if it was active before background
            Task {
                await startMonitoring()
            }
        }
    }
}

// MARK: - Background Monitoring Errors

enum BackgroundMonitoringError: LocalizedError {
    case serviceUnavailable
    case permissionDenied
    case configurationFailed
    
    var errorDescription: String? {
        switch self {
        case .serviceUnavailable:
            return "Background monitoring service is unavailable"
        case .permissionDenied:
            return "Microphone or speech recognition permission denied"
        case .configurationFailed:
            return "Failed to configure background monitoring"
        }
    }
}
