//
//  KeywordDetectionService.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import Speech
import AVFoundation
import Combine

// MARK: - Detection Models

struct KeywordDetection {
    let keyword: String
    let confidence: Float
    let timestamp: Date
    let context: String
}

// MARK: - Detection State

enum DetectionState: Equatable {
    case inactive
    case monitoring
    case keywordDetected(keyword: String)
    case failed(Error)

    static func == (lhs: DetectionState, rhs: DetectionState) -> Bool {
        switch (lhs, rhs) {
        case (.inactive, .inactive):
            return true
        case (.monitoring, .monitoring):
            return true
        case (.keywordDetected(let lhsKeyword), .keywordDetected(let rhsKeyword)):
            return lhsKeyword == rhsKeyword
        case (.failed, .failed):
            return true // Simplified comparison for errors
        default:
            return false
        }
    }
}

// MARK: - Service Protocol

protocol KeywordDetectionServicing: ObservableObject {
    var isMonitoring: Bool { get }
    var detectionState: CurrentValueSubject<DetectionState, Never> { get }
    var activeKeywords: [String] { get }
    var detectionStream: AnyPublisher<KeywordDetection, Never> { get }

    func startMonitoring(for keywords: [String]) async throws
    func stopMonitoring() async
    func updateKeywords(_ keywords: [String]) async
    func setDetectionSensitivity(_ sensitivity: Float) async
}

// Legacy protocol for backward compatibility
typealias KeywordDetectionServiceProtocol = KeywordDetectionServicing

// MARK: - Implementation

@MainActor
class KeywordDetectionService: NSObject, KeywordDetectionServicing {

    // MARK: - Published Properties
    @Published private(set) var isMonitoring = false
    @Published private(set) var activeKeywords: [String] = []

    // MARK: - State Management
    let detectionState = CurrentValueSubject<DetectionState, Never>(.inactive)

    // MARK: - Private Properties
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var audioEngine: AVAudioEngine?

    private let detectionSubject = PassthroughSubject<KeywordDetection, Never>()
    private var accumulatedText = ""
    private var detectionSensitivity: Float = 0.8
    private var lastDetectionTime: Date?
    private let cooldownPeriod: TimeInterval = 2.0
    private var cancellables = Set<AnyCancellable>()

    // Performance optimization properties
    private var bufferSize: AVAudioFrameCount = 1024
    private var sampleRate: Double = 16000.0 // Optimized for keyword detection

    private let audioSessionManager: any AudioSessionManaging
    private let permissionsService: PermissionsServiceProtocol
    
    // MARK: - Computed Properties
    var detectionStream: AnyPublisher<KeywordDetection, Never> {
        detectionSubject.eraseToAnyPublisher()
    }
    
    // MARK: - Initialization
    init(
        audioSessionManager: any AudioSessionManaging,
        permissionsService: PermissionsServiceProtocol
    ) {
        self.audioSessionManager = audioSessionManager
        self.permissionsService = permissionsService
        super.init()
        setupSpeechRecognizer()
        setupStateBindings()
    }

    convenience init() {
        self.init(
            audioSessionManager: AudioSessionManager.shared,
            permissionsService: PermissionsService()
        )
    }

    // MARK: - Public Methods
    func startMonitoring(for keywords: [String]) async throws {
        guard !isMonitoring else {
            updateDetectionState(.failed(KeywordDetectionError.alreadyListening))
            throw KeywordDetectionError.alreadyListening
        }

        guard !keywords.isEmpty else {
            updateDetectionState(.failed(KeywordDetectionError.noKeywords))
            throw KeywordDetectionError.noKeywords
        }

        // Request permissions
        let hasPermissions = await permissionsService.requestAllPermissions()
        guard hasPermissions else {
            updateDetectionState(.failed(KeywordDetectionError.permissionDenied))
            throw KeywordDetectionError.permissionDenied
        }

        // Setup audio session for background monitoring
        try await audioSessionManager.configureSession(for: .backgroundMonitoring)

        activeKeywords = keywords
        accumulatedText = ""
        lastDetectionTime = nil

        try startSpeechRecognition()
        isMonitoring = true
        updateDetectionState(.monitoring)

        print("🎯 KeywordDetectionService: Started monitoring for keywords: \(keywords)")
    }
    
    func stopMonitoring() async {
        guard isMonitoring else { return }

        stopSpeechRecognition()
        isMonitoring = false
        activeKeywords = []
        accumulatedText = ""
        updateDetectionState(.inactive)

        // Deactivate audio session to save battery
        try? await audioSessionManager.deactivateSession()

        print("🛑 KeywordDetectionService: Stopped monitoring")
    }

    func updateKeywords(_ keywords: [String]) async {
        activeKeywords = keywords
        print("🔄 KeywordDetectionService: Updated keywords to: \(keywords)")

        // If monitoring, restart with new keywords
        if isMonitoring {
            await stopMonitoring()
            try? await startMonitoring(for: keywords)
        }
    }

    func setDetectionSensitivity(_ sensitivity: Float) async {
        detectionSensitivity = max(0.0, min(1.0, sensitivity))
        print("🎚️ KeywordDetectionService: Set sensitivity to: \(detectionSensitivity)")
    }
}

// MARK: - Private Methods
private extension KeywordDetectionService {
    
    func setupSpeechRecognizer() {
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "en-US"))
        speechRecognizer?.delegate = self
    }
    
    func startSpeechRecognition() throws {
        guard let speechRecognizer = speechRecognizer,
              speechRecognizer.isAvailable else {
            throw KeywordDetectionError.speechRecognitionUnavailable
        }

        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            throw KeywordDetectionError.speechRecognitionSetupFailed
        }

        // Optimize for keyword detection
        recognitionRequest.shouldReportPartialResults = true
        recognitionRequest.requiresOnDeviceRecognition = true // Use on-device for better privacy and battery
        recognitionRequest.taskHint = .search // Optimize for short phrases

        audioEngine = AVAudioEngine()
        let inputNode = audioEngine!.inputNode

        // Use optimized format for keyword detection
        let recordingFormat = AVAudioFormat(
            standardFormatWithSampleRate: sampleRate,
            channels: 1
        )!

        // Install tap with optimized buffer size for battery efficiency
        inputNode.installTap(onBus: 0, bufferSize: bufferSize, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)
        }

        audioEngine!.prepare()
        try audioEngine!.start()

        recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { [weak self] result, error in
            DispatchQueue.main.async {
                self?.handleRecognitionResult(result, error: error)
            }
        }
    }
    
    func stopSpeechRecognition() {
        audioEngine?.stop()
        audioEngine?.inputNode.removeTap(onBus: 0)
        recognitionRequest?.endAudio()
        recognitionTask?.cancel()
        
        audioEngine = nil
        recognitionRequest = nil
        recognitionTask = nil
    }
    
    func handleRecognitionResult(_ result: SFSpeechRecognitionResult?, error: Error?) {
        if let error = error {
            print("❌ KeywordDetectionService: Recognition error: \(error)")
            Task {
                await stopMonitoring()
            }
            return
        }
        
        guard let result = result else { return }
        
        let newText = result.bestTranscription.formattedString
        accumulatedText = newText
        
        // Check for keywords in the new text
        checkForKeywords(in: newText)
        
        // Limit accumulated text to prevent memory issues
        if accumulatedText.count > 1000 {
            let startIndex = accumulatedText.index(accumulatedText.startIndex, offsetBy: 500)
            accumulatedText = String(accumulatedText[startIndex...])
        }
    }
    
    func checkForKeywords(in text: String) {
        let lowercaseText = text.lowercased()
        let currentTime = Date()

        // Apply cooldown to prevent rapid repeated detections
        if let lastTime = lastDetectionTime,
           currentTime.timeIntervalSince(lastTime) < cooldownPeriod {
            return
        }

        // Improved keyword matching with word boundaries
        let words = lowercaseText.components(separatedBy: .whitespacesAndNewlines)
            .filter { !$0.isEmpty }

        for keyword in activeKeywords {
            let lowercaseKeyword = keyword.lowercased()

            // Check for exact word match first (highest confidence)
            if words.contains(lowercaseKeyword) {
                let confidence = calculateConfidence(for: keyword, in: text, isExactMatch: true)

                if confidence >= detectionSensitivity {
                    let detection = KeywordDetection(
                        keyword: keyword,
                        confidence: confidence,
                        timestamp: currentTime,
                        context: extractContext(for: keyword, in: text)
                    )

                    lastDetectionTime = currentTime
                    updateDetectionState(.keywordDetected(keyword: keyword))
                    detectionSubject.send(detection)

                    print("✅ KeywordDetectionService: Detected '\(keyword)' with confidence \(confidence)")
                    return // Only detect one keyword at a time
                }
            }

            // Fallback to substring match with lower confidence
            else if lowercaseText.contains(lowercaseKeyword) {
                let confidence = calculateConfidence(for: keyword, in: text, isExactMatch: false)

                if confidence >= detectionSensitivity {
                    let detection = KeywordDetection(
                        keyword: keyword,
                        confidence: confidence,
                        timestamp: currentTime,
                        context: extractContext(for: keyword, in: text)
                    )

                    lastDetectionTime = currentTime
                    updateDetectionState(.keywordDetected(keyword: keyword))
                    detectionSubject.send(detection)

                    print("✅ KeywordDetectionService: Detected '\(keyword)' (partial) with confidence \(confidence)")
                    return
                }
            }
        }
    }
    
    func calculateConfidence(for keyword: String, in text: String, isExactMatch: Bool) -> Float {
        let lowercaseText = text.lowercased()
        let lowercaseKeyword = keyword.lowercased()

        // Base confidence based on match type
        var confidence: Float = isExactMatch ? 0.9 : 0.6

        // Boost confidence for exact word matches
        if isExactMatch {
            confidence += 0.05
        }

        // Boost confidence if keyword appears at the beginning
        if lowercaseText.hasPrefix(lowercaseKeyword) {
            confidence += 0.05
        }

        // Reduce confidence if text is very long (might be noise)
        let textLength = text.count
        if textLength > 200 {
            confidence -= 0.1
        } else if textLength > 100 {
            confidence -= 0.05
        }

        // Boost confidence for shorter, cleaner text
        if textLength < 50 {
            confidence += 0.05
        }

        // Check for surrounding context quality
        let words = lowercaseText.components(separatedBy: .whitespacesAndNewlines)
        let keywordIndex = words.firstIndex(of: lowercaseKeyword)

        if let index = keywordIndex {
            // Boost confidence if keyword is at the start of a sentence
            if index == 0 {
                confidence += 0.05
            }

            // Reduce confidence if surrounded by many other words (might be noise)
            if words.count > 10 {
                confidence -= 0.05
            }
        }

        return min(1.0, max(0.0, confidence))
    }
    
    func extractContext(for keyword: String, in text: String) -> String {
        let words = text.components(separatedBy: .whitespacesAndNewlines)
        guard let keywordIndex = words.firstIndex(where: { $0.lowercased().contains(keyword.lowercased()) }) else {
            return text
        }

        // Extract context around the keyword (3 words before and after)
        let startIndex = max(0, keywordIndex - 3)
        let endIndex = min(words.count, keywordIndex + 4)

        return words[startIndex..<endIndex].joined(separator: " ")
    }

    func updateDetectionState(_ newState: DetectionState) {
        detectionState.send(newState)
    }

    func setupStateBindings() {
        // Bind detection state changes to published properties
        detectionState
            .sink { [weak self] state in
                switch state {
                case .inactive:
                    self?.isMonitoring = false
                case .monitoring:
                    self?.isMonitoring = true
                case .keywordDetected:
                    // Keep monitoring state true, but note detection
                    break
                case .failed:
                    self?.isMonitoring = false
                }
            }
            .store(in: &cancellables)
    }
}

// MARK: - SFSpeechRecognizerDelegate
extension KeywordDetectionService: SFSpeechRecognizerDelegate {
    func speechRecognizer(_ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool) {
        if !available && isMonitoring {
            Task {
                await stopMonitoring()
            }
        }
    }
}

// MARK: - Errors
enum KeywordDetectionError: LocalizedError {
    case alreadyListening
    case noKeywords
    case permissionDenied
    case speechRecognitionUnavailable
    case speechRecognitionSetupFailed
    case audioSessionSetupFailed
    
    var errorDescription: String? {
        switch self {
        case .alreadyListening:
            return "Keyword detection is already active"
        case .noKeywords:
            return "No keywords provided for detection"
        case .permissionDenied:
            return "Microphone or speech recognition permission denied"
        case .speechRecognitionUnavailable:
            return "Speech recognition is not available"
        case .speechRecognitionSetupFailed:
            return "Failed to setup speech recognition"
        case .audioSessionSetupFailed:
            return "Failed to setup audio session"
        }
    }
}
