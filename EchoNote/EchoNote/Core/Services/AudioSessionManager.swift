//
//  AudioSessionManager.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import AVFoundation
import Combine

// MARK: - Recording Mode

enum RecordingMode {
    case manual
    case backgroundMonitoring
    case siriShortcut
    case playback

    var description: String {
        switch self {
        case .manual:
            return "Manual Recording"
        case .backgroundMonitoring:
            return "Background Monitoring"
        case .siriShortcut:
            return "Siri Shortcut"
        case .playback:
            return "Playback"
        }
    }
}

// MARK: - Audio Session State

enum AudioSessionState: Equatable {
    case inactive
    case active(RecordingMode)
    case interrupted
    case failed(Error)

    static func == (lhs: AudioSessionState, rhs: AudioSessionState) -> Bool {
        switch (lhs, rhs) {
        case (.inactive, .inactive):
            return true
        case (.active(let lhsMode), .active(let rhsMode)):
            return lhsMode == rhsMode
        case (.interrupted, .interrupted):
            return true
        case (.failed, .failed):
            return true
        default:
            return false
        }
    }
}

// MARK: - Audio Session Managing Protocol

protocol AudioSessionManaging: ObservableObject {
    var currentSessionState: AudioSessionState { get }
    var sessionStatePublisher: AnyPublisher<AudioSessionState, Never> { get }

    func configureSession(for mode: RecordingMode) async throws
    func activateSession() async throws
    func deactivateSession() async throws
    func handleInterruption(_ type: AVAudioSession.InterruptionType) async
    func handleRouteChange(_ reason: AVAudioSession.RouteChangeReason) async
}

// MARK: - Permissions Service Protocol

protocol PermissionsServiceProtocol {
    var hasMicrophonePermission: Bool { get }
    var hasSpeechRecognitionPermission: Bool { get }

    func requestMicrophonePermission() async -> Bool
    func requestSpeechRecognitionPermission() async -> Bool
    func requestAllPermissions() async -> Bool
}

// MARK: - Audio Session Manager Implementation

@MainActor
class AudioSessionManager: ObservableObject, AudioSessionManaging {

    // MARK: - Singleton
    static let shared = AudioSessionManager()

    // MARK: - Published Properties
    @Published private(set) var currentSessionState: AudioSessionState = .inactive

    // MARK: - Private Properties
    private let audioSession: AVAudioSession
    private let notificationCenter: NotificationCenter
    private let sessionStateSubject = PassthroughSubject<AudioSessionState, Never>()
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Computed Properties
    var sessionStatePublisher: AnyPublisher<AudioSessionState, Never> {
        sessionStateSubject.eraseToAnyPublisher()
    }

    var isActive: Bool {
        switch currentSessionState {
        case .active:
            return true
        default:
            return false
        }
    }

    var currentCategory: AVAudioSession.Category {
        audioSession.category
    }

    // MARK: - Initialization
    private init(
        audioSession: AVAudioSession = AVAudioSession.sharedInstance(),
        notificationCenter: NotificationCenter = NotificationCenter.default
    ) {
        self.audioSession = audioSession
        self.notificationCenter = notificationCenter
        setupNotifications()
        setupStatePublisher()
    }

    // MARK: - Public Methods

    func configureSession(for mode: RecordingMode) async throws {
        do {
            switch mode {
            case .manual:
                try await configureForManualRecording()
            case .backgroundMonitoring:
                try await configureForBackgroundMonitoring()
            case .siriShortcut:
                try await configureForSiriShortcut()
            case .playback:
                try await configureForPlayback()
            }

            updateState(.active(mode))
            print("🎧 AudioSessionManager: Configured for \(mode.description)")

        } catch {
            updateState(.failed(error))
            print("❌ AudioSessionManager: Failed to configure for \(mode.description): \(error)")
            throw AudioSessionError.configurationFailed(underlying: error)
        }
    }

    func activateSession() async throws {
        do {
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
            print("✅ AudioSessionManager: Session activated")
        } catch {
            updateState(.failed(error))
            throw AudioSessionError.activationFailed(underlying: error)
        }
    }

    func deactivateSession() async throws {
        do {
            try audioSession.setActive(false, options: .notifyOthersOnDeactivation)
            updateState(.inactive)
            print("🔇 AudioSessionManager: Session deactivated")
        } catch {
            updateState(.failed(error))
            throw AudioSessionError.deactivationFailed(underlying: error)
        }
    }

    func handleInterruption(_ type: AVAudioSession.InterruptionType) async {
        switch type {
        case .began:
            print("🔇 AudioSessionManager: Interruption began")
            updateState(.interrupted)

        case .ended:
            print("🔊 AudioSessionManager: Interruption ended")
            // Try to reactivate the session
            do {
                try await activateSession()
            } catch {
                print("❌ AudioSessionManager: Failed to reactivate after interruption: \(error)")
                updateState(.failed(error))
            }

        @unknown default:
            print("⚠️ AudioSessionManager: Unknown interruption type")
        }
    }

    func handleRouteChange(_ reason: AVAudioSession.RouteChangeReason) async {
        switch reason {
        case .newDeviceAvailable:
            print("🎧 AudioSessionManager: New audio device available")

        case .oldDeviceUnavailable:
            print("🎧 AudioSessionManager: Audio device unavailable")
            // Handle device disconnection gracefully

        case .categoryChange:
            print("🎧 AudioSessionManager: Category changed")

        case .override:
            print("🎧 AudioSessionManager: Route override")

        case .wakeFromSleep:
            print("🎧 AudioSessionManager: Wake from sleep")

        case .noSuitableRouteForCategory:
            print("❌ AudioSessionManager: No suitable route for category")
            updateState(.failed(AudioSessionError.noSuitableRoute))

        case .routeConfigurationChange:
            print("🎧 AudioSessionManager: Route configuration changed")

        @unknown default:
            print("⚠️ AudioSessionManager: Unknown route change reason")
        }
    }
}

// MARK: - Private Methods
private extension AudioSessionManager {

    func configureForManualRecording() async throws {
        try audioSession.setCategory(
            .playAndRecord,
            mode: .measurement,
            options: [.defaultToSpeaker, .allowBluetooth, .allowBluetoothA2DP]
        )

        try audioSession.setPreferredSampleRate(44100.0)
        try audioSession.setPreferredIOBufferDuration(0.01) // 10ms for low latency
        try await activateSession()
    }

    func configureForBackgroundMonitoring() async throws {
        try audioSession.setCategory(
            .record,
            mode: .measurement,
            options: [.allowBluetooth, .duckOthers]
        )

        try audioSession.setPreferredSampleRate(16000.0) // Lower rate for efficiency
        try audioSession.setPreferredIOBufferDuration(0.02) // 20ms for stability
        try await activateSession()
    }

    func configureForSiriShortcut() async throws {
        try audioSession.setCategory(
            .playAndRecord,
            mode: .measurement,
            options: [.defaultToSpeaker, .allowBluetooth]
        )

        try audioSession.setPreferredSampleRate(22050.0) // Balanced quality/efficiency
        try audioSession.setPreferredIOBufferDuration(0.015) // 15ms
        try await activateSession()
    }

    func configureForPlayback() async throws {
        try audioSession.setCategory(
            .playback,
            mode: .default,
            options: [.defaultToSpeaker]
        )

        try await activateSession()
    }

    func updateState(_ newState: AudioSessionState) {
        currentSessionState = newState
        sessionStateSubject.send(newState)
    }

    func setupStatePublisher() {
        $currentSessionState
            .sink { [weak self] state in
                self?.sessionStateSubject.send(state)
            }
            .store(in: &cancellables)
    }

    func setupNotifications() {
        notificationCenter.addObserver(
            forName: AVAudioSession.interruptionNotification,
            object: audioSession,
            queue: .main
        ) { [weak self] notification in
            Task { @MainActor in
                await self?.handleInterruptionNotification(notification)
            }
        }

        notificationCenter.addObserver(
            forName: AVAudioSession.routeChangeNotification,
            object: audioSession,
            queue: .main
        ) { [weak self] notification in
            Task { @MainActor in
                await self?.handleRouteChangeNotification(notification)
            }
        }
    }

    func handleInterruptionNotification(_ notification: Notification) async {
        guard let userInfo = notification.userInfo,
              let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
            return
        }

        await handleInterruption(type)
    }

    func handleRouteChangeNotification(_ notification: Notification) async {
        guard let userInfo = notification.userInfo,
              let reasonValue = userInfo[AVAudioSessionRouteChangeReasonKey] as? UInt,
              let reason = AVAudioSession.RouteChangeReason(rawValue: reasonValue) else {
            return
        }

        await handleRouteChange(reason)
    }
}

// MARK: - Audio Session Errors

enum AudioSessionError: LocalizedError, Equatable {
    case configurationFailed(underlying: Error)
    case activationFailed(underlying: Error)
    case deactivationFailed(underlying: Error)
    case permissionDenied
    case hardwareUnavailable
    case noSuitableRoute
    case interruptionHandlingFailed
    case invalidState

    var errorDescription: String? {
        switch self {
        case .configurationFailed(let error):
            return "Failed to configure audio session: \(error.localizedDescription)"
        case .activationFailed(let error):
            return "Failed to activate audio session: \(error.localizedDescription)"
        case .deactivationFailed(let error):
            return "Failed to deactivate audio session: \(error.localizedDescription)"
        case .permissionDenied:
            return "Microphone permission denied"
        case .hardwareUnavailable:
            return "Audio hardware unavailable"
        case .noSuitableRoute:
            return "No suitable audio route available"
        case .interruptionHandlingFailed:
            return "Failed to handle audio session interruption"
        case .invalidState:
            return "Invalid audio session state"
        }
    }

    static func == (lhs: AudioSessionError, rhs: AudioSessionError) -> Bool {
        switch (lhs, rhs) {
        case (.permissionDenied, .permissionDenied),
             (.hardwareUnavailable, .hardwareUnavailable),
             (.noSuitableRoute, .noSuitableRoute),
             (.interruptionHandlingFailed, .interruptionHandlingFailed),
             (.invalidState, .invalidState):
            return true
        case (.configurationFailed, .configurationFailed),
             (.activationFailed, .activationFailed),
             (.deactivationFailed, .deactivationFailed):
            return true // Simplified comparison for errors with underlying errors
        default:
            return false
        }
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let audioSessionStateChanged = Notification.Name("audioSessionStateChanged")
    static let audioSessionInterrupted = Notification.Name("audioSessionInterrupted")
    static let audioSessionResumed = Notification.Name("audioSessionResumed")
    static let audioDeviceChanged = Notification.Name("audioDeviceChanged")
}
