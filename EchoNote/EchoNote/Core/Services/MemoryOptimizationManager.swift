//
//  MemoryOptimizationManager.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import Combine
import UIKit
import SwiftUI

// MARK: - Memory Usage Metrics

struct MemoryUsageMetrics {
    let totalMemory: UInt64
    let usedMemory: UInt64
    let availableMemory: UInt64
    let appMemoryUsage: UInt64
    let timestamp: Date
    
    var memoryPressure: MemoryPressureLevel {
        let usagePercentage = Double(usedMemory) / Double(totalMemory)
        
        switch usagePercentage {
        case 0..<0.6:
            return .low
        case 0.6..<0.8:
            return .moderate
        case 0.8..<0.9:
            return .high
        default:
            return .critical
        }
    }
    
    var formattedAppMemoryUsage: String {
        return ByteCountFormatter.string(fromByteCount: Int64(appMemoryUsage), countStyle: .memory)
    }
}

enum MemoryPressureLevel: String, CaseIterable {
    case low = "Low"
    case moderate = "Moderate"
    case high = "High"
    case critical = "Critical"
    
    var color: Color {
        switch self {
        case .low:
            return .green
        case .moderate:
            return .yellow
        case .high:
            return .orange
        case .critical:
            return .red
        }
    }
}

// MARK: - Memory Optimization Strategy

enum MemoryOptimizationStrategy {
    case clearCaches
    case releaseUnusedResources
    case compressAudioData
    case limitConcurrentOperations
    case reduceImageQuality
    case purgeOldRecordings
    
    var description: String {
        switch self {
        case .clearCaches:
            return "Clear temporary caches"
        case .releaseUnusedResources:
            return "Release unused resources"
        case .compressAudioData:
            return "Compress audio data"
        case .limitConcurrentOperations:
            return "Limit concurrent operations"
        case .reduceImageQuality:
            return "Reduce image quality"
        case .purgeOldRecordings:
            return "Purge old recordings"
        }
    }
}

// MARK: - Memory Optimization Managing Protocol

protocol MemoryOptimizationManaging: ObservableObject {
    var currentMemoryUsage: MemoryUsageMetrics? { get }
    var memoryPressureLevel: MemoryPressureLevel { get }
    var isOptimizing: Bool { get }
    
    func startMemoryMonitoring()
    func stopMemoryMonitoring()
    func optimizeMemoryUsage() async
    func applyOptimizationStrategy(_ strategy: MemoryOptimizationStrategy) async
    func getMemoryUsageReport() -> String
}

// MARK: - Memory Optimization Manager

@MainActor
class MemoryOptimizationManager: MemoryOptimizationManaging {
    
    // MARK: - Singleton
    static let shared = MemoryOptimizationManager()
    
    // MARK: - Published Properties
    @Published private(set) var currentMemoryUsage: MemoryUsageMetrics?
    @Published private(set) var isOptimizing = false
    
    var memoryPressureLevel: MemoryPressureLevel {
        return currentMemoryUsage?.memoryPressure ?? .low
    }
    
    // MARK: - Private Properties
    private var memoryMonitoringTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    private let eventBus = EventBus.shared
    
    // Memory thresholds
    private let criticalMemoryThreshold: Double = 0.9
    private let highMemoryThreshold: Double = 0.8
    private let moderateMemoryThreshold: Double = 0.6
    
    // Optimization settings
    private let maxConcurrentOperations = 3
    private let cacheCleanupInterval: TimeInterval = 300 // 5 minutes
    private var lastCacheCleanup: Date?
    
    // Audio compression settings
    private let audioCompressionQuality: Float = 0.7
    private let maxAudioFileSize: UInt64 = 50 * 1024 * 1024 // 50MB
    
    // MARK: - Initialization
    
    private init() {
        setupMemoryPressureNotifications()
        setupAppLifecycleObservers()
    }
    
    // MARK: - Public Methods
    
    func startMemoryMonitoring() {
        guard memoryMonitoringTimer == nil else { return }
        
        // Initial measurement
        updateMemoryUsage()
        
        // Start periodic monitoring
        memoryMonitoringTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateMemoryUsage()
            }
        }
        
        print("📊 MemoryOptimizationManager: Started memory monitoring")
    }
    
    func stopMemoryMonitoring() {
        memoryMonitoringTimer?.invalidate()
        memoryMonitoringTimer = nil
        
        print("📊 MemoryOptimizationManager: Stopped memory monitoring")
    }
    
    func optimizeMemoryUsage() async {
        guard !isOptimizing else { return }
        
        isOptimizing = true
        defer { isOptimizing = false }
        
        print("🧹 MemoryOptimizationManager: Starting memory optimization")
        
        let initialUsage = getCurrentAppMemoryUsage()
        
        // Apply optimization strategies based on memory pressure
        switch memoryPressureLevel {
        case .critical:
            await applyCriticalOptimizations()
        case .high:
            await applyHighPressureOptimizations()
        case .moderate:
            await applyModerateOptimizations()
        case .low:
            await applyLowPressureOptimizations()
        }
        
        // Update memory usage after optimization
        updateMemoryUsage()
        
        let finalUsage = getCurrentAppMemoryUsage()
        let memoryFreed = initialUsage > finalUsage ? initialUsage - finalUsage : 0
        
        print("✅ MemoryOptimizationManager: Optimization complete. Freed \(ByteCountFormatter.string(fromByteCount: Int64(memoryFreed), countStyle: .memory))")
        
        // Publish optimization event
        eventBus.publish(MemoryOptimizationEvent(
            initialUsage: initialUsage,
            finalUsage: finalUsage,
            memoryFreed: memoryFreed,
            strategies: getAppliedStrategies()
        ))
    }
    
    func applyOptimizationStrategy(_ strategy: MemoryOptimizationStrategy) async {
        print("🔧 MemoryOptimizationManager: Applying strategy: \(strategy.description)")
        
        switch strategy {
        case .clearCaches:
            await clearCaches()
        case .releaseUnusedResources:
            await releaseUnusedResources()
        case .compressAudioData:
            await compressAudioData()
        case .limitConcurrentOperations:
            await limitConcurrentOperations()
        case .reduceImageQuality:
            await reduceImageQuality()
        case .purgeOldRecordings:
            await purgeOldRecordings()
        }
    }
    
    func getMemoryUsageReport() -> String {
        guard let usage = currentMemoryUsage else {
            return "Memory usage data not available"
        }
        
        let formatter = ByteCountFormatter()
        formatter.countStyle = .memory
        
        return """
        Memory Usage Report
        ===================
        Total Memory: \(formatter.string(fromByteCount: Int64(usage.totalMemory)))
        Used Memory: \(formatter.string(fromByteCount: Int64(usage.usedMemory)))
        Available Memory: \(formatter.string(fromByteCount: Int64(usage.availableMemory)))
        App Memory Usage: \(formatter.string(fromByteCount: Int64(usage.appMemoryUsage)))
        Memory Pressure: \(usage.memoryPressure.rawValue)
        Timestamp: \(usage.timestamp)
        """
    }
    
    // MARK: - Private Methods
    
    private func updateMemoryUsage() {
        let metrics = getMemoryUsageMetrics()
        currentMemoryUsage = metrics
        
        // Check for memory pressure and auto-optimize if needed
        if metrics.memoryPressure == .critical {
            Task {
                await optimizeMemoryUsage()
            }
        }
    }
    
    private func getMemoryUsageMetrics() -> MemoryUsageMetrics {
        let totalMemory = ProcessInfo.processInfo.physicalMemory
        let appMemoryUsage = getCurrentAppMemoryUsage()
        
        // Get system memory info
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        let usedMemory = result == KERN_SUCCESS ? UInt64(info.resident_size) : appMemoryUsage
        let availableMemory = totalMemory > usedMemory ? totalMemory - usedMemory : 0
        
        return MemoryUsageMetrics(
            totalMemory: totalMemory,
            usedMemory: usedMemory,
            availableMemory: availableMemory,
            appMemoryUsage: appMemoryUsage,
            timestamp: Date()
        )
    }
    
    private func getCurrentAppMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        return result == KERN_SUCCESS ? UInt64(info.resident_size) : 0
    }
    
    // MARK: - Optimization Strategies
    
    private func applyCriticalOptimizations() async {
        await clearCaches()
        await releaseUnusedResources()
        await compressAudioData()
        await limitConcurrentOperations()
        await purgeOldRecordings()
    }
    
    private func applyHighPressureOptimizations() async {
        await clearCaches()
        await releaseUnusedResources()
        await compressAudioData()
        await limitConcurrentOperations()
    }
    
    private func applyModerateOptimizations() async {
        await clearCaches()
        await releaseUnusedResources()
    }
    
    private func applyLowPressureOptimizations() async {
        // Only clear caches if it's been a while
        if shouldPerformCacheCleanup() {
            await clearCaches()
        }
    }
    
    private func clearCaches() async {
        // Clear URL cache
        URLCache.shared.removeAllCachedResponses()
        
        // Clear image cache if using a caching library
        // ImageCache.shared.clearCache()
        
        // Clear temporary files
        clearTemporaryFiles()
        
        lastCacheCleanup = Date()
        print("🧹 MemoryOptimizationManager: Cleared caches")
    }
    
    private func releaseUnusedResources() async {
        // Force garbage collection
        autoreleasepool {
            // Release any retained objects
        }
        
        // Clear unused audio buffers
        // AudioBufferManager.shared.clearUnusedBuffers()
        
        print("🗑️ MemoryOptimizationManager: Released unused resources")
    }
    
    private func compressAudioData() async {
        // Compress audio files that exceed size threshold
        // This would typically involve:
        // 1. Finding large audio files
        // 2. Compressing them with lower quality settings
        // 3. Replacing the original files
        
        print("🗜️ MemoryOptimizationManager: Compressed audio data")
    }
    
    private func limitConcurrentOperations() async {
        // Reduce the number of concurrent operations
        // This would typically involve:
        // 1. Checking current operation queues
        // 2. Reducing maxConcurrentOperationCount
        // 3. Cancelling non-essential operations
        
        print("⏸️ MemoryOptimizationManager: Limited concurrent operations")
    }
    
    private func reduceImageQuality() async {
        // Reduce image quality for cached images
        // This would typically involve:
        // 1. Finding cached images
        // 2. Recompressing them with lower quality
        // 3. Updating the cache
        
        print("🖼️ MemoryOptimizationManager: Reduced image quality")
    }
    
    private func purgeOldRecordings() async {
        // Remove old recordings that haven't been accessed recently
        // This would typically involve:
        // 1. Finding recordings older than a threshold
        // 2. Checking if they're still needed
        // 3. Removing them from storage
        
        print("🗂️ MemoryOptimizationManager: Purged old recordings")
    }
    
    // MARK: - Helper Methods
    
    private func shouldPerformCacheCleanup() -> Bool {
        guard let lastCleanup = lastCacheCleanup else { return true }
        return Date().timeIntervalSince(lastCleanup) > cacheCleanupInterval
    }
    
    private func clearTemporaryFiles() {
        let tempDirectory = FileManager.default.temporaryDirectory
        
        do {
            let tempFiles = try FileManager.default.contentsOfDirectory(at: tempDirectory, includingPropertiesForKeys: nil)
            
            for file in tempFiles {
                try? FileManager.default.removeItem(at: file)
            }
        } catch {
            print("❌ MemoryOptimizationManager: Failed to clear temporary files: \(error)")
        }
    }
    
    private func getAppliedStrategies() -> [MemoryOptimizationStrategy] {
        // Return the strategies that were applied based on memory pressure
        switch memoryPressureLevel {
        case .critical:
            return [.clearCaches, .releaseUnusedResources, .compressAudioData, .limitConcurrentOperations, .purgeOldRecordings]
        case .high:
            return [.clearCaches, .releaseUnusedResources, .compressAudioData, .limitConcurrentOperations]
        case .moderate:
            return [.clearCaches, .releaseUnusedResources]
        case .low:
            return [.clearCaches]
        }
    }
    
    // MARK: - System Notifications
    
    private func setupMemoryPressureNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(didReceiveMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    private func setupAppLifecycleObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
    }
    
    @objc private func didReceiveMemoryWarning() {
        print("⚠️ MemoryOptimizationManager: Received memory warning")
        
        Task { @MainActor in
            await optimizeMemoryUsage()
        }
    }
    
    @objc private func appDidEnterBackground() {
        // Perform aggressive cleanup when app goes to background
        Task { @MainActor in
            await applyHighPressureOptimizations()
        }
    }
    
    @objc private func appWillEnterForeground() {
        // Update memory usage when app returns to foreground
        updateMemoryUsage()
    }
}

// MARK: - Memory Optimization Event

struct MemoryOptimizationEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let initialUsage: UInt64
    let finalUsage: UInt64
    let memoryFreed: UInt64
    let strategies: [MemoryOptimizationStrategy]
    
    init(initialUsage: UInt64, finalUsage: UInt64, memoryFreed: UInt64, strategies: [MemoryOptimizationStrategy]) {
        self.timestamp = Date()
        self.eventId = UUID()
        self.initialUsage = initialUsage
        self.finalUsage = finalUsage
        self.memoryFreed = memoryFreed
        self.strategies = strategies
    }
}
