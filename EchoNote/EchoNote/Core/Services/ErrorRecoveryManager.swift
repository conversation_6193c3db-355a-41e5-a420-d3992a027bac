//
//  ErrorRecoveryManager.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import Combine

// MARK: - Recovery Strategy

enum RecoveryStrategy {
    case retry(maxAttempts: Int, delay: TimeInterval)
    case fallback(action: () async throws -> Void)
    case reset(component: String)
    case userIntervention(message: String)
    case ignore
    
    var description: String {
        switch self {
        case .retry(let maxAttempts, let delay):
            return "Retry up to \(maxAttempts) times with \(delay)s delay"
        case .fallback:
            return "Execute fallback action"
        case .reset(let component):
            return "Reset \(component) component"
        case .userIntervention(let message):
            return "User intervention required: \(message)"
        case .ignore:
            return "Ignore error"
        }
    }
}

// MARK: - Recovery Result

enum RecoveryResult {
    case success
    case partialSuccess(message: String)
    case failed(error: Error)
    case requiresUserAction(message: String)
    
    var isSuccessful: Bool {
        switch self {
        case .success, .partialSuccess:
            return true
        case .failed, .requiresUserAction:
            return false
        }
    }
}

// MARK: - Recovery Context

struct RecoveryContext {
    let originalError: Error
    let component: String
    let operation: String
    let timestamp: Date
    let attemptCount: Int
    let userContext: [String: Any]
    
    init(
        originalError: Error,
        component: String,
        operation: String,
        attemptCount: Int = 0,
        userContext: [String: Any] = [:]
    ) {
        self.originalError = originalError
        self.component = component
        self.operation = operation
        self.timestamp = Date()
        self.attemptCount = attemptCount
        self.userContext = userContext
    }
}

// MARK: - Error Recovery Managing Protocol

protocol ErrorRecoveryManaging {
    func registerRecoveryStrategy(for errorType: Error.Type, strategy: RecoveryStrategy)
    func attemptRecovery(for context: RecoveryContext) async -> RecoveryResult
    func canRecover(from error: Error) -> Bool
    func getRecoveryStrategy(for error: Error) -> RecoveryStrategy?
}

// MARK: - Error Recovery Manager

@MainActor
class ErrorRecoveryManager: ErrorRecoveryManaging {
    
    // MARK: - Singleton
    static let shared = ErrorRecoveryManager()
    
    // MARK: - Private Properties
    private var recoveryStrategies: [String: RecoveryStrategy] = [:]
    private var recoveryHistory: [RecoveryContext] = []
    private let eventBus = EventBus.shared
    private let maxHistorySize = 100
    
    // Services for recovery operations
    private weak var recordingCoordinator: RecordingCoordinating?
    private weak var keywordDetectionService: KeywordDetectionServicing?
    private weak var audioSessionManager: AudioSessionManaging?
    
    // MARK: - Initialization
    
    private init() {
        setupDefaultRecoveryStrategies()
        setupEventSubscriptions()
    }
    
    func setServices(
        recordingCoordinator: RecordingCoordinating?,
        keywordDetectionService: KeywordDetectionServicing?,
        audioSessionManager: AudioSessionManaging?
    ) {
        self.recordingCoordinator = recordingCoordinator
        self.keywordDetectionService = keywordDetectionService
        self.audioSessionManager = audioSessionManager
    }
    
    // MARK: - Public Methods
    
    func registerRecoveryStrategy(for errorType: Error.Type, strategy: RecoveryStrategy) {
        let key = String(describing: errorType)
        recoveryStrategies[key] = strategy
        print("🔧 ErrorRecoveryManager: Registered recovery strategy for \(key): \(strategy.description)")
    }
    
    func attemptRecovery(for context: RecoveryContext) async -> RecoveryResult {
        print("🚨 ErrorRecoveryManager: Attempting recovery for \(context.component).\(context.operation)")
        
        // Add to history
        addToHistory(context)
        
        // Get recovery strategy
        guard let strategy = getRecoveryStrategy(for: context.originalError) else {
            print("❌ ErrorRecoveryManager: No recovery strategy found for error: \(context.originalError)")
            return .failed(error: ErrorRecoveryError.noStrategyFound)
        }
        
        // Execute recovery strategy
        let result = await executeRecoveryStrategy(strategy, context: context)
        
        // Log result
        switch result {
        case .success:
            print("✅ ErrorRecoveryManager: Recovery successful for \(context.component).\(context.operation)")
        case .partialSuccess(let message):
            print("⚠️ ErrorRecoveryManager: Partial recovery for \(context.component).\(context.operation): \(message)")
        case .failed(let error):
            print("❌ ErrorRecoveryManager: Recovery failed for \(context.component).\(context.operation): \(error)")
        case .requiresUserAction(let message):
            print("👤 ErrorRecoveryManager: User action required for \(context.component).\(context.operation): \(message)")
        }
        
        return result
    }
    
    func canRecover(from error: Error) -> Bool {
        return getRecoveryStrategy(for: error) != nil
    }
    
    func getRecoveryStrategy(for error: Error) -> RecoveryStrategy? {
        // Check for specific error type
        let errorTypeName = String(describing: type(of: error))
        if let strategy = recoveryStrategies[errorTypeName] {
            return strategy
        }
        
        // Check for protocol conformance
        if error is RecordingError {
            return recoveryStrategies["RecordingError"]
        }
        
        if error is AudioSessionError {
            return recoveryStrategies["AudioSessionError"]
        }
        
        if error is KeywordError {
            return recoveryStrategies["KeywordError"]
        }
        
        // Default strategy
        return recoveryStrategies["DefaultError"]
    }
    
    // MARK: - Private Methods
    
    private func setupDefaultRecoveryStrategies() {
        // Recording errors
        registerRecoveryStrategy(
            for: RecordingError.self,
            strategy: .retry(maxAttempts: 3, delay: 1.0)
        )
        
        // Audio session errors
        registerRecoveryStrategy(
            for: AudioSessionError.self,
            strategy: .reset(component: "AudioSession")
        )
        
        // Keyword detection errors
        registerRecoveryStrategy(
            for: KeywordError.self,
            strategy: .fallback {
                // Fallback to basic keyword detection
                print("🔄 ErrorRecoveryManager: Falling back to basic keyword detection")
            }
        )
        
        // Permission errors
        recoveryStrategies["PermissionError"] = .userIntervention(
            message: "Please grant microphone permission in Settings"
        )
        
        // Network errors
        recoveryStrategies["NetworkError"] = .retry(maxAttempts: 5, delay: 2.0)
        
        // Default strategy
        recoveryStrategies["DefaultError"] = .retry(maxAttempts: 2, delay: 0.5)
    }
    
    private func setupEventSubscriptions() {
        // Listen for error events
        eventBus.errorOccurred
            .sink { [weak self] event in
                Task { @MainActor in
                    await self?.handleErrorEvent(event)
                }
            }
            .store(in: &cancellables)
    }
    
    private var cancellables = Set<AnyCancellable>()
    
    private func handleErrorEvent(_ event: ErrorOccurredEvent) async {
        // Determine if automatic recovery should be attempted
        guard shouldAttemptAutomaticRecovery(for: event.error, severity: event.severity) else {
            return
        }
        
        let context = RecoveryContext(
            originalError: event.error,
            component: event.context ?? "Unknown",
            operation: "AutoRecovery"
        )
        
        let result = await attemptRecovery(for: context)
        
        // Handle recovery result
        switch result {
        case .success:
            // Recovery successful, no further action needed
            break
        case .partialSuccess(let message):
            // Notify user of partial recovery
            eventBus.publishError(
                error: ErrorRecoveryError.partialRecovery(message),
                context: "ErrorRecoveryManager",
                severity: .warning
            )
        case .failed(let error):
            // Recovery failed, escalate error
            eventBus.publishError(
                error: error,
                context: "ErrorRecoveryManager",
                severity: .error
            )
        case .requiresUserAction(let message):
            // Show user intervention dialog
            eventBus.publishError(
                error: ErrorRecoveryError.userActionRequired(message),
                context: "ErrorRecoveryManager",
                severity: .critical
            )
        }
    }
    
    private func shouldAttemptAutomaticRecovery(for error: Error, severity: ErrorSeverity) -> Bool {
        // Don't auto-recover critical errors that require user intervention
        if severity == .critical {
            return false
        }
        
        // Don't auto-recover permission errors
        if error is RecordingError {
            if case .permissionDenied = error as? RecordingError {
                return false
            }
        }
        
        if error is AudioSessionError {
            if case .permissionDenied = error as? AudioSessionError {
                return false
            }
        }
        
        return true
    }
    
    private func executeRecoveryStrategy(_ strategy: RecoveryStrategy, context: RecoveryContext) async -> RecoveryResult {
        switch strategy {
        case .retry(let maxAttempts, let delay):
            return await executeRetryStrategy(maxAttempts: maxAttempts, delay: delay, context: context)
            
        case .fallback(let action):
            return await executeFallbackStrategy(action: action, context: context)
            
        case .reset(let component):
            return await executeResetStrategy(component: component, context: context)
            
        case .userIntervention(let message):
            return .requiresUserAction(message: message)
            
        case .ignore:
            return .success
        }
    }
    
    private func executeRetryStrategy(maxAttempts: Int, delay: TimeInterval, context: RecoveryContext) async -> RecoveryResult {
        for attempt in 1...maxAttempts {
            print("🔄 ErrorRecoveryManager: Retry attempt \(attempt)/\(maxAttempts) for \(context.component).\(context.operation)")
            
            // Wait before retry (except for first attempt)
            if attempt > 1 {
                try? await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
            }
            
            // Attempt to recover based on component
            let success = await attemptComponentRecovery(context: context)
            
            if success {
                return .success
            }
        }
        
        return .failed(error: ErrorRecoveryError.retryLimitExceeded)
    }
    
    private func executeFallbackStrategy(action: () async throws -> Void, context: RecoveryContext) async -> RecoveryResult {
        do {
            try await action()
            return .partialSuccess(message: "Fallback action executed successfully")
        } catch {
            return .failed(error: error)
        }
    }
    
    private func executeResetStrategy(component: String, context: RecoveryContext) async -> RecoveryResult {
        switch component.lowercased() {
        case "audiosession":
            return await resetAudioSession()
        case "recording":
            return await resetRecording()
        case "keyworddetection":
            return await resetKeywordDetection()
        default:
            return .failed(error: ErrorRecoveryError.unknownComponent(component))
        }
    }
    
    private func attemptComponentRecovery(context: RecoveryContext) async -> Bool {
        switch context.component.lowercased() {
        case "recording", "recordingcoordinator":
            return await recoverRecording(context: context)
        case "audiosession", "audiosessionmanager":
            return await recoverAudioSession(context: context)
        case "keyworddetection", "keyworddetectionservice":
            return await recoverKeywordDetection(context: context)
        default:
            return false
        }
    }
    
    private func recoverRecording(context: RecoveryContext) async -> Bool {
        guard let coordinator = recordingCoordinator else { return false }
        
        do {
            // Cancel any existing recording
            await coordinator.cancelCurrentRecording()
            
            // Wait a moment
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
            
            // Try to start a new recording if that was the original operation
            if context.operation.contains("start") {
                try await coordinator.startManualRecording()
            }
            
            return true
        } catch {
            print("❌ ErrorRecoveryManager: Failed to recover recording: \(error)")
            return false
        }
    }
    
    private func recoverAudioSession(context: RecoveryContext) async -> Bool {
        guard let audioManager = audioSessionManager else { return false }
        
        do {
            // Deactivate current session
            try await audioManager.deactivateSession()
            
            // Wait a moment
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
            
            // Reconfigure and activate
            try await audioManager.configureSession(for: .recording)
            try await audioManager.activateSession()
            
            return true
        } catch {
            print("❌ ErrorRecoveryManager: Failed to recover audio session: \(error)")
            return false
        }
    }
    
    private func recoverKeywordDetection(context: RecoveryContext) async -> Bool {
        guard let keywordService = keywordDetectionService else { return false }
        
        do {
            // Stop current monitoring
            await keywordService.stopMonitoring()
            
            // Wait a moment
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
            
            // Restart with current keywords
            try await keywordService.startMonitoring(for: keywordService.activeKeywords)
            
            return true
        } catch {
            print("❌ ErrorRecoveryManager: Failed to recover keyword detection: \(error)")
            return false
        }
    }
    
    private func resetAudioSession() async -> RecoveryResult {
        return await recoverAudioSession(context: RecoveryContext(
            originalError: ErrorRecoveryError.resetRequested,
            component: "AudioSession",
            operation: "reset"
        )) ? .success : .failed(error: ErrorRecoveryError.resetFailed("AudioSession"))
    }
    
    private func resetRecording() async -> RecoveryResult {
        return await recoverRecording(context: RecoveryContext(
            originalError: ErrorRecoveryError.resetRequested,
            component: "Recording",
            operation: "reset"
        )) ? .success : .failed(error: ErrorRecoveryError.resetFailed("Recording"))
    }
    
    private func resetKeywordDetection() async -> RecoveryResult {
        return await recoverKeywordDetection(context: RecoveryContext(
            originalError: ErrorRecoveryError.resetRequested,
            component: "KeywordDetection",
            operation: "reset"
        )) ? .success : .failed(error: ErrorRecoveryError.resetFailed("KeywordDetection"))
    }
    
    private func addToHistory(_ context: RecoveryContext) {
        recoveryHistory.append(context)
        
        // Trim history if needed
        if recoveryHistory.count > maxHistorySize {
            recoveryHistory.removeFirst(recoveryHistory.count - maxHistorySize)
        }
    }
}

// MARK: - Error Recovery Errors

enum ErrorRecoveryError: LocalizedError {
    case noStrategyFound
    case retryLimitExceeded
    case partialRecovery(String)
    case userActionRequired(String)
    case unknownComponent(String)
    case resetRequested
    case resetFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .noStrategyFound:
            return "No recovery strategy found for this error"
        case .retryLimitExceeded:
            return "Maximum retry attempts exceeded"
        case .partialRecovery(let message):
            return "Partial recovery: \(message)"
        case .userActionRequired(let message):
            return "User action required: \(message)"
        case .unknownComponent(let component):
            return "Unknown component for reset: \(component)"
        case .resetRequested:
            return "Component reset was requested"
        case .resetFailed(let component):
            return "Failed to reset component: \(component)"
        }
    }
}
