//
//  SiriIntegrationManager.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import Intents
import IntentsUI
import Combine

// MARK: - Siri Integration Managing Protocol

protocol SiriIntegrationManaging {
    func registerShortcuts() async
    func handleShortcut(_ shortcut: SiriShortcutType) async throws
    func donateRecordingIntent() async
    func donateQuickNoteIntent(content: String?) async
    func requestSiriAuthorization() async -> Bool
}

// MARK: - Siri Integration Manager

@MainActor
class SiriIntegrationManager: SiriIntegrationManaging {
    
    // MARK: - Singleton
    static let shared = SiriIntegrationManager()
    
    // MARK: - Private Properties
    private let recordingCoordinator: RecordingCoordinating?
    private let intentManager: INInteractionManager
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(
        recordingCoordinator: RecordingCoordinating? = nil,
        intentManager: INInteractionManager = INInteractionManager()
    ) {
        self.recordingCoordinator = recordingCoordinator
        self.intentManager = intentManager
    }
    
    // MARK: - Public Methods
    
    func registerShortcuts() async {
        guard await requestSiriAuthorization() else {
            print("❌ SiriIntegrationManager: Siri authorization denied")
            return
        }
        
        do {
            // Register start recording shortcut
            await registerStartRecordingShortcut()
            
            // Register quick note shortcut
            await registerQuickNoteShortcut()
            
            print("✅ SiriIntegrationManager: Shortcuts registered successfully")
        } catch {
            print("❌ SiriIntegrationManager: Failed to register shortcuts: \(error)")
        }
    }
    
    func handleShortcut(_ shortcut: SiriShortcutType) async throws {
        guard let coordinator = recordingCoordinator else {
            throw SiriIntegrationError.coordinatorUnavailable
        }
        
        do {
            try await coordinator.handleSiriShortcut(shortcutType: shortcut)
            print("✅ SiriIntegrationManager: Handled shortcut: \(shortcut)")
        } catch {
            print("❌ SiriIntegrationManager: Failed to handle shortcut: \(error)")
            throw SiriIntegrationError.shortcutHandlingFailed(underlying: error)
        }
    }
    
    func donateRecordingIntent() async {
        let intent = StartRecordingIntent()
        intent.suggestedInvocationPhrase = "Start recording a note"
        
        let interaction = INInteraction(intent: intent, response: nil)
        interaction.identifier = "start-recording-\(UUID().uuidString)"
        interaction.dateInterval = DateInterval(start: Date(), duration: 0)
        
        do {
            try await interaction.donate()
            print("✅ SiriIntegrationManager: Donated recording intent")
        } catch {
            print("❌ SiriIntegrationManager: Failed to donate recording intent: \(error)")
        }
    }
    
    func donateQuickNoteIntent(content: String?) async {
        let intent = CreateQuickNoteIntent()
        intent.content = content
        intent.suggestedInvocationPhrase = content != nil ? 
            "Create a note with \(content!)" : "Create a quick note"
        
        let interaction = INInteraction(intent: intent, response: nil)
        interaction.identifier = "quick-note-\(UUID().uuidString)"
        interaction.dateInterval = DateInterval(start: Date(), duration: 0)
        
        do {
            try await interaction.donate()
            print("✅ SiriIntegrationManager: Donated quick note intent")
        } catch {
            print("❌ SiriIntegrationManager: Failed to donate quick note intent: \(error)")
        }
    }
    
    func requestSiriAuthorization() async -> Bool {
        return await withCheckedContinuation { continuation in
            INPreferences.requestSiriAuthorization { status in
                let authorized = status == .authorized
                print("🎤 SiriIntegrationManager: Siri authorization status: \(status.rawValue)")
                continuation.resume(returning: authorized)
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func registerStartRecordingShortcut() async {
        let intent = StartRecordingIntent()
        intent.suggestedInvocationPhrase = "Start recording"
        
        let shortcut = INShortcut(intent: intent)
        
        // Create user activity for the shortcut
        let activity = NSUserActivity(activityType: "com.echonote.startRecording")
        activity.title = "Start Recording"
        activity.isEligibleForSearch = true
        activity.isEligibleForPrediction = true
        activity.persistentIdentifier = "com.echonote.startRecording"
        activity.userInfo = ["action": "startRecording"]
        
        // Donate the activity
        activity.becomeCurrent()
        
        print("📝 SiriIntegrationManager: Registered start recording shortcut")
    }
    
    private func registerQuickNoteShortcut() async {
        let intent = CreateQuickNoteIntent()
        intent.suggestedInvocationPhrase = "Create a quick note"
        
        let shortcut = INShortcut(intent: intent)
        
        // Create user activity for the shortcut
        let activity = NSUserActivity(activityType: "com.echonote.createQuickNote")
        activity.title = "Create Quick Note"
        activity.isEligibleForSearch = true
        activity.isEligibleForPrediction = true
        activity.persistentIdentifier = "com.echonote.createQuickNote"
        activity.userInfo = ["action": "createQuickNote"]
        
        // Donate the activity
        activity.becomeCurrent()
        
        print("📝 SiriIntegrationManager: Registered quick note shortcut")
    }
}

// MARK: - Custom Intent Definitions

class StartRecordingIntent: INIntent {
    override var suggestedInvocationPhrase: String? {
        get { return "Start recording" }
        set { }
    }
    
    override var identifier: String? {
        return "StartRecordingIntent"
    }
}

class CreateQuickNoteIntent: INIntent {
    @NSManaged public var content: String?
    
    override var suggestedInvocationPhrase: String? {
        get { return "Create a quick note" }
        set { }
    }
    
    override var identifier: String? {
        return "CreateQuickNoteIntent"
    }
}

// MARK: - Siri Integration Errors

enum SiriIntegrationError: LocalizedError {
    case coordinatorUnavailable
    case authorizationDenied
    case shortcutRegistrationFailed(underlying: Error)
    case shortcutHandlingFailed(underlying: Error)
    case intentDonationFailed(underlying: Error)
    
    var errorDescription: String? {
        switch self {
        case .coordinatorUnavailable:
            return "Recording coordinator is unavailable"
        case .authorizationDenied:
            return "Siri authorization was denied"
        case .shortcutRegistrationFailed(let error):
            return "Failed to register Siri shortcut: \(error.localizedDescription)"
        case .shortcutHandlingFailed(let error):
            return "Failed to handle Siri shortcut: \(error.localizedDescription)"
        case .intentDonationFailed(let error):
            return "Failed to donate intent to Siri: \(error.localizedDescription)"
        }
    }
}

// MARK: - User Activity Handling

extension SiriIntegrationManager {
    
    func handleUserActivity(_ userActivity: NSUserActivity) async -> Bool {
        guard let action = userActivity.userInfo?["action"] as? String else {
            return false
        }
        
        do {
            switch action {
            case "startRecording":
                try await handleShortcut(.startRecording)
                return true
                
            case "createQuickNote":
                let content = userActivity.userInfo?["content"] as? String
                try await handleShortcut(.createQuickNote(content: content))
                return true
                
            default:
                print("⚠️ SiriIntegrationManager: Unknown user activity action: \(action)")
                return false
            }
        } catch {
            print("❌ SiriIntegrationManager: Failed to handle user activity: \(error)")
            return false
        }
    }
}

// MARK: - Intent Response Handling

extension SiriIntegrationManager {
    
    func handleIntentResponse(_ intent: INIntent) async -> Bool {
        switch intent {
        case is StartRecordingIntent:
            do {
                try await handleShortcut(.startRecording)
                return true
            } catch {
                print("❌ SiriIntegrationManager: Failed to handle start recording intent: \(error)")
                return false
            }
            
        case let quickNoteIntent as CreateQuickNoteIntent:
            do {
                try await handleShortcut(.createQuickNote(content: quickNoteIntent.content))
                return true
            } catch {
                print("❌ SiriIntegrationManager: Failed to handle quick note intent: \(error)")
                return false
            }
            
        default:
            print("⚠️ SiriIntegrationManager: Unknown intent type: \(type(of: intent))")
            return false
        }
    }
}
