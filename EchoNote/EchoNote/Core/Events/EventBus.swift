//
//  EventBus.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import Combine

// MARK: - Event Protocol

protocol AppEvent {
    var timestamp: Date { get }
    var eventId: UUID { get }
}

// MARK: - Base Event Implementation

struct BaseEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    
    init() {
        self.timestamp = Date()
        self.eventId = UUID()
    }
}

// MARK: - Recording Events

struct RecordingStartedEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let session: RecordingSession
    
    init(session: RecordingSession) {
        self.timestamp = Date()
        self.eventId = UUID()
        self.session = session
    }
}

struct RecordingStoppedEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let result: RecordingResult
    
    init(result: RecordingResult) {
        self.timestamp = Date()
        self.eventId = UUID()
        self.result = result
    }
}

struct RecordingFailedEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let error: Error
    let sessionId: UUID?
    
    init(error: Error, sessionId: UUID? = nil) {
        self.timestamp = Date()
        self.eventId = UUID()
        self.error = error
        self.sessionId = sessionId
    }
}

struct RecordingCancelledEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let sessionId: UUID
    
    init(sessionId: UUID) {
        self.timestamp = Date()
        self.eventId = UUID()
        self.sessionId = sessionId
    }
}

// MARK: - Note Events

struct NoteCreatedEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let note: Note
    
    init(note: Note) {
        self.timestamp = Date()
        self.eventId = UUID()
        self.note = note
    }
}

struct NoteUpdatedEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let note: Note
    let previousVersion: Note?
    
    init(note: Note, previousVersion: Note? = nil) {
        self.timestamp = Date()
        self.eventId = UUID()
        self.note = note
        self.previousVersion = previousVersion
    }
}

struct NoteDeletedEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let noteId: UUID
    let note: Note?
    
    init(noteId: UUID, note: Note? = nil) {
        self.timestamp = Date()
        self.eventId = UUID()
        self.noteId = noteId
        self.note = note
    }
}

// MARK: - Keyword Events

struct KeywordDetectedEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let keyword: String
    let confidence: Float
    let context: String?
    
    init(keyword: String, confidence: Float, context: String? = nil) {
        self.timestamp = Date()
        self.eventId = UUID()
        self.keyword = keyword
        self.confidence = confidence
        self.context = context
    }
}

struct KeywordsUpdatedEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let keywords: [Keyword]
    
    init(keywords: [Keyword]) {
        self.timestamp = Date()
        self.eventId = UUID()
        self.keywords = keywords
    }
}

// MARK: - Background Monitoring Events

struct BackgroundMonitoringStartedEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let keywords: [String]
    
    init(keywords: [String]) {
        self.timestamp = Date()
        self.eventId = UUID()
        self.keywords = keywords
    }
}

struct BackgroundMonitoringStoppedEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    
    init() {
        self.timestamp = Date()
        self.eventId = UUID()
    }
}

// MARK: - Siri Events

struct SiriShortcutTriggeredEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let shortcutType: SiriShortcutType
    
    init(shortcutType: SiriShortcutType) {
        self.timestamp = Date()
        self.eventId = UUID()
        self.shortcutType = shortcutType
    }
}

// MARK: - Error Events

struct ErrorOccurredEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let error: Error
    let context: String?
    let severity: ErrorSeverity
    
    init(error: Error, context: String? = nil, severity: ErrorSeverity = .error) {
        self.timestamp = Date()
        self.eventId = UUID()
        self.error = error
        self.context = context
        self.severity = severity
    }
}

// MARK: - Event Bus

@MainActor
class EventBus: ObservableObject {
    
    // MARK: - Singleton
    static let shared = EventBus()
    
    // MARK: - Event Publishers
    
    // Recording Events
    let recordingStarted = PassthroughSubject<RecordingStartedEvent, Never>()
    let recordingStopped = PassthroughSubject<RecordingStoppedEvent, Never>()
    let recordingFailed = PassthroughSubject<RecordingFailedEvent, Never>()
    let recordingCancelled = PassthroughSubject<RecordingCancelledEvent, Never>()
    
    // Note Events
    let noteCreated = PassthroughSubject<NoteCreatedEvent, Never>()
    let noteUpdated = PassthroughSubject<NoteUpdatedEvent, Never>()
    let noteDeleted = PassthroughSubject<NoteDeletedEvent, Never>()
    
    // Keyword Events
    let keywordDetected = PassthroughSubject<KeywordDetectedEvent, Never>()
    let keywordsUpdated = PassthroughSubject<KeywordsUpdatedEvent, Never>()
    
    // Background Monitoring Events
    let backgroundMonitoringStarted = PassthroughSubject<BackgroundMonitoringStartedEvent, Never>()
    let backgroundMonitoringStopped = PassthroughSubject<BackgroundMonitoringStoppedEvent, Never>()
    
    // Siri Events
    let siriShortcutTriggered = PassthroughSubject<SiriShortcutTriggeredEvent, Never>()
    
    // Error Events
    let errorOccurred = PassthroughSubject<ErrorOccurredEvent, Never>()
    
    // MARK: - All Events Publisher
    
    var allEvents: AnyPublisher<AppEvent, Never> {
        Publishers.Merge8(
            recordingStarted.map { $0 as AppEvent }.eraseToAnyPublisher(),
            recordingStopped.map { $0 as AppEvent }.eraseToAnyPublisher(),
            recordingFailed.map { $0 as AppEvent }.eraseToAnyPublisher(),
            noteCreated.map { $0 as AppEvent }.eraseToAnyPublisher(),
            noteUpdated.map { $0 as AppEvent }.eraseToAnyPublisher(),
            keywordDetected.map { $0 as AppEvent }.eraseToAnyPublisher(),
            backgroundMonitoringStarted.map { $0 as AppEvent }.eraseToAnyPublisher(),
            siriShortcutTriggered.map { $0 as AppEvent }.eraseToAnyPublisher()
        ).eraseToAnyPublisher()
    }
    
    // MARK: - Initialization
    
    private init() {}
    
    // MARK: - Event Publishing Methods
    
    func publish(_ event: AppEvent) {
        switch event {
        case let event as RecordingStartedEvent:
            recordingStarted.send(event)
        case let event as RecordingStoppedEvent:
            recordingStopped.send(event)
        case let event as RecordingFailedEvent:
            recordingFailed.send(event)
        case let event as RecordingCancelledEvent:
            recordingCancelled.send(event)
        case let event as NoteCreatedEvent:
            noteCreated.send(event)
        case let event as NoteUpdatedEvent:
            noteUpdated.send(event)
        case let event as NoteDeletedEvent:
            noteDeleted.send(event)
        case let event as KeywordDetectedEvent:
            keywordDetected.send(event)
        case let event as KeywordsUpdatedEvent:
            keywordsUpdated.send(event)
        case let event as BackgroundMonitoringStartedEvent:
            backgroundMonitoringStarted.send(event)
        case let event as BackgroundMonitoringStoppedEvent:
            backgroundMonitoringStopped.send(event)
        case let event as SiriShortcutTriggeredEvent:
            siriShortcutTriggered.send(event)
        case let event as ErrorOccurredEvent:
            errorOccurred.send(event)
        default:
            print("⚠️ EventBus: Unknown event type: \(type(of: event))")
        }
    }
    
    // MARK: - Convenience Publishing Methods
    
    func publishRecordingStarted(session: RecordingSession) {
        publish(RecordingStartedEvent(session: session))
    }
    
    func publishRecordingStopped(result: RecordingResult) {
        publish(RecordingStoppedEvent(result: result))
    }
    
    func publishRecordingFailed(error: Error, sessionId: UUID? = nil) {
        publish(RecordingFailedEvent(error: error, sessionId: sessionId))
    }
    
    func publishNoteCreated(note: Note) {
        publish(NoteCreatedEvent(note: note))
    }
    
    func publishKeywordDetected(keyword: String, confidence: Float, context: String? = nil) {
        publish(KeywordDetectedEvent(keyword: keyword, confidence: confidence, context: context))
    }
    
    func publishError(error: Error, context: String? = nil, severity: ErrorSeverity = .error) {
        publish(ErrorOccurredEvent(error: error, context: context, severity: severity))
    }
}

// MARK: - Event Subscription Extensions

extension EventBus {
    
    // MARK: - Filtered Event Streams
    
    var recordingEvents: AnyPublisher<AppEvent, Never> {
        Publishers.Merge4(
            recordingStarted.map { $0 as AppEvent },
            recordingStopped.map { $0 as AppEvent },
            recordingFailed.map { $0 as AppEvent },
            recordingCancelled.map { $0 as AppEvent }
        ).eraseToAnyPublisher()
    }
    
    var noteEvents: AnyPublisher<AppEvent, Never> {
        Publishers.Merge3(
            noteCreated.map { $0 as AppEvent },
            noteUpdated.map { $0 as AppEvent },
            noteDeleted.map { $0 as AppEvent }
        ).eraseToAnyPublisher()
    }
    
    var keywordEvents: AnyPublisher<AppEvent, Never> {
        Publishers.Merge2(
            keywordDetected.map { $0 as AppEvent },
            keywordsUpdated.map { $0 as AppEvent }
        ).eraseToAnyPublisher()
    }
    
    // MARK: - Event Filtering Helpers
    
    func events<T: AppEvent>(ofType type: T.Type) -> AnyPublisher<T, Never> {
        allEvents
            .compactMap { $0 as? T }
            .eraseToAnyPublisher()
    }
    
    func eventsInTimeRange(from startTime: Date, to endTime: Date) -> AnyPublisher<AppEvent, Never> {
        allEvents
            .filter { event in
                event.timestamp >= startTime && event.timestamp <= endTime
            }
            .eraseToAnyPublisher()
    }
}
