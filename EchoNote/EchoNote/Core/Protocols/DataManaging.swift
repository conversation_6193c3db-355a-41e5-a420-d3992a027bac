//
//  DataManaging.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import SwiftData

// MARK: - Data Managing Protocol

protocol DataManaging {
    // MARK: - Note Management
    func saveNote(_ note: Note) async throws
    func fetchNotes() async throws -> [Note]
    func fetchNote(withId id: UUID) async throws -> Note?
    func updateNote(_ note: Note) async throws
    func deleteNote(_ note: Note) async throws
    
    // MARK: - Keyword Management
    func fetchKeywords() async throws -> [Keyword]
    func saveKeywords(_ keywords: [Keyword]) async throws
    
    // MARK: - Generic Data Operations
    func save() async throws
    func delete<T: PersistentModel>(_ model: T) async throws
    func fetch<T: PersistentModel>(_ type: T.Type) async throws -> [T]
    func insert<T: PersistentModel>(_ model: T) async
}
