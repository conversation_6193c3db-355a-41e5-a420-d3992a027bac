//
//  ServiceContainer.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import SwiftData

// MARK: - Service Provider Protocol

protocol ServiceProviding {
    func register<T>(_ type: T.Type, service: Any)
    func register<T>(_ type: T.Type, factory: @escaping () -> T)
    func resolve<T>(_ type: T.Type) -> T?
    func reset()
}

// MARK: - Service Registration Types

enum ServiceLifetime {
    case singleton
    case transient
}

private struct ServiceRegistration {
    let lifetime: ServiceLifetime
    let factory: () -> Any
    var instance: Any?
}

// MARK: - Thread-Safe Service Container

actor ServiceContainer: ServiceProviding {

    // MARK: - Singleton
    static let shared = ServiceContainer()

    // MARK: - Private Properties
    private var services: [String: ServiceRegistration] = [:]

    // MARK: - Initialization
    private init() {}

    // MARK: - Service Registration

    func register<T>(_ type: T.Type, service: Any) {
        let key = String(describing: type)
        let registration = ServiceRegistration(
            lifetime: .singleton,
            factory: { service },
            instance: service
        )
        services[key] = registration
        print("📦 ServiceContainer: Registered singleton service for \(key)")
    }

    func register<T>(_ type: T.Type, factory: @escaping () -> T) {
        let key = String(describing: type)
        let registration = ServiceRegistration(
            lifetime: .transient,
            factory: factory,
            instance: nil
        )
        services[key] = registration
        print("📦 ServiceContainer: Registered factory for \(key)")
    }

    func registerSingleton<T>(_ type: T.Type, factory: @escaping () -> T) {
        let key = String(describing: type)
        let registration = ServiceRegistration(
            lifetime: .singleton,
            factory: factory,
            instance: nil
        )
        services[key] = registration
        print("📦 ServiceContainer: Registered singleton factory for \(key)")
    }

    // MARK: - Service Resolution

    func resolve<T>(_ type: T.Type) -> T? {
        let key = String(describing: type)

        guard var registration = services[key] else {
            print("❌ ServiceContainer: Service not found for \(key)")
            return nil
        }

        switch registration.lifetime {
        case .singleton:
            if let instance = registration.instance as? T {
                return instance
            } else {
                let newInstance = registration.factory() as? T
                registration.instance = newInstance
                services[key] = registration
                print("✅ ServiceContainer: Created singleton instance for \(key)")
                return newInstance
            }

        case .transient:
            let instance = registration.factory() as? T
            print("✅ ServiceContainer: Created transient instance for \(key)")
            return instance
        }
    }

    // MARK: - Container Management

    func reset() {
        services.removeAll()
        print("🔄 ServiceContainer: Reset all services")
    }

    func isRegistered<T>(_ type: T.Type) -> Bool {
        let key = String(describing: type)
        return services[key] != nil
    }

    func getRegisteredServices() -> [String] {
        return Array(services.keys)
    }
}
// MARK: - MainActor Service Manager

@MainActor
class ServiceManager: ObservableObject {

    // MARK: - Singleton
    static let shared = ServiceManager()

    // MARK: - Private Properties
    private let container = ServiceContainer.shared
    private var _dataManager: DataManager?

    // MARK: - Initialization
    private init() {}

    // MARK: - Configuration
    func configure(with modelContext: ModelContext) async {
        _dataManager = DataManager(modelContext: modelContext)
        await setupServices()
        print("✅ ServiceManager: Configured with ModelContext")
    }

    // MARK: - Service Setup
    private func setupServices() async {
        guard let dataManager = _dataManager else {
            fatalError("DataManager not initialized")
        }

        // Register core services
        await container.register(AudioSessionManagerProtocol.self, service: AudioSessionManager.shared)
        await container.register(PermissionsServiceProtocol.self, service: PermissionsService.shared)
        await container.register(DataManager.self, service: dataManager)

        // Register business services with lazy initialization
        await container.register(VoiceRecordingServiceProtocol.self, factory: {
            // This will be resolved when needed
            return VoiceRecordingService(
                audioSessionManager: AudioSessionManager.shared,
                permissionsService: PermissionsService.shared
            )
        })

        await container.register(KeywordDetectionServiceProtocol.self, factory: {
            return KeywordDetectionService(
                audioSessionManager: AudioSessionManager.shared,
                permissionsService: PermissionsService.shared
            )
        })

        await container.register(NoteCreationServiceProtocol.self, factory: {
            return NoteCreationService(dataManager: dataManager)
        })

        // Register coordinator with lazy initialization
        await container.register(RecordingCoordinator.self, factory: {
            // Create instances directly for now - in a real implementation,
            // we'd resolve these from the container
            let voiceService = VoiceRecordingService(
                audioSessionManager: AudioSessionManager.shared,
                permissionsService: PermissionsService.shared
            )
            let keywordService = KeywordDetectionService(
                audioSessionManager: AudioSessionManager.shared,
                permissionsService: PermissionsService.shared
            )
            let noteService = NoteCreationService(dataManager: dataManager)

            return RecordingCoordinator(
                voiceRecordingService: voiceService,
                keywordDetectionService: keywordService,
                noteCreationService: noteService,
                dataManager: dataManager
            )
        })
    }

    // MARK: - Service Access Methods
    func getRecordingCoordinator() async -> RecordingCoordinator? {
        return await container.resolve(RecordingCoordinator.self)
    }

    func getVoiceRecordingService() async -> VoiceRecordingServiceProtocol? {
        return await container.resolve(VoiceRecordingServiceProtocol.self)
    }

    func getKeywordDetectionService() async -> KeywordDetectionServiceProtocol? {
        return await container.resolve(KeywordDetectionServiceProtocol.self)
    }

    func getNoteCreationService() async -> NoteCreationServiceProtocol? {
        return await container.resolve(NoteCreationServiceProtocol.self)
    }

    func getAudioSessionManager() async -> AudioSessionManagerProtocol? {
        return await container.resolve(AudioSessionManagerProtocol.self)
    }

    func getPermissionsService() async -> PermissionsServiceProtocol? {
        return await container.resolve(PermissionsServiceProtocol.self)
    }

    func getDataManager() async -> DataManager? {
        return await container.resolve(DataManager.self)
    }

    // MARK: - Testing Support
    func reset() async {
        await container.reset()
        _dataManager = nil
    }
}

// MARK: - SwiftUI Environment

struct ServiceManagerKey: EnvironmentKey {
    static let defaultValue = ServiceManager.shared
}

extension EnvironmentValues {
    var serviceManager: ServiceManager {
        get { self[ServiceManagerKey.self] }
        set { self[ServiceManagerKey.self] = newValue }
    }
}

// MARK: - View Extension

extension View {
    func withServiceManager(_ manager: ServiceManager = ServiceManager.shared) -> some View {
        environment(\.serviceManager, manager)
    }
}

// MARK: - Mock Services for Testing

#if DEBUG
protocol MockServiceProviding: ServiceProviding {
    func registerMock<T>(_ type: T.Type, mock: T)
}

actor MockServiceContainer: ServiceProviding, MockServiceProviding {
    private var services: [String: Any] = [:]

    func register<T>(_ type: T.Type, service: Any) {
        let key = String(describing: type)
        services[key] = service
    }

    func register<T>(_ type: T.Type, factory: @escaping () -> T) {
        let key = String(describing: type)
        services[key] = factory()
    }

    func resolve<T>(_ type: T.Type) -> T? {
        let key = String(describing: type)
        return services[key] as? T
    }

    func reset() {
        services.removeAll()
    }

    func registerMock<T>(_ type: T.Type, mock: T) {
        register(type, service: mock)
    }
}

@MainActor
class MockServiceManager: ServiceManager {
    private let mockContainer = MockServiceContainer()

    override init() {
        super.init()
    }

    func configureMocks() async {
        // This would be implemented with mock services for unit testing
        print("🧪 MockServiceManager: Configured with mock services")
    }

    func registerMock<T>(_ type: T.Type, mock: T) async {
        await mockContainer.registerMock(type, mock: mock)
    }
}
#endif
