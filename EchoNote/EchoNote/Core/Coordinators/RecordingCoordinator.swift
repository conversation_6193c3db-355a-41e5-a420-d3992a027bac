//
//  RecordingCoordinator.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import Combine
import SwiftUI

// MARK: - Siri Shortcut Types

enum SiriShortcutType {
    case startRecording
    case stopRecording
    case createQuickNote(content: String?)
}

// MARK: - Recording State (Unified)

enum RecordingState: Equatable {
    case idle
    case listening(keywords: [String])
    case recording(session: RecordingSession)
    case processing
    case error(String)

    var isActive: Bool {
        switch self {
        case .idle, .error:
            return false
        case .listening, .recording, .processing:
            return true
        }
    }

    static func == (lhs: RecordingState, rhs: RecordingState) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle):
            return true
        case (.listening(let lhsKeywords), .listening(let rhsKeywords)):
            return lhsKeywords == rhsKeywords
        case (.recording(let lhsSession), .recording(let rhsSession)):
            return lhsSession.id == rhsSession.id
        case (.processing, .processing):
            return true
        case (.error(let lhsError), .error(let rhsError)):
            return lhsError == rhsError
        default:
            return false
        }
    }
}

// MARK: - Recording Coordinating Protocol

protocol RecordingCoordinating: ObservableObject {
    var recordingState: CurrentValueSubject<RecordingState, Never> { get }
    var activeSession: RecordingSession? { get }
    var lastCreatedNote: Note? { get }

    func startManualRecording() async throws
    func stopManualRecording() async throws -> Note?
    func handleKeywordDetection(keyword: String) async throws
    func handleSiriShortcut(shortcutType: SiriShortcutType) async throws
    func cancelCurrentRecording() async
    func enableBackgroundMonitoring(keywords: [String]) async throws
    func disableBackgroundMonitoring() async
}

enum RecordingMode {
    case manual
    case background
    case siri
}
    
// MARK: - Recording Coordinator Implementation

@MainActor
class RecordingCoordinator: ObservableObject, RecordingCoordinating {

    // MARK: - Published Properties
    @Published private(set) var activeSession: RecordingSession?
    @Published private(set) var lastCreatedNote: Note?
    @Published private(set) var isBackgroundMonitoringEnabled = false

    // MARK: - State Management
    let recordingState = CurrentValueSubject<RecordingState, Never>(.idle)

    // MARK: - Private Properties
    private let voiceRecordingService: VoiceRecordingServicing
    private let keywordDetectionService: KeywordDetectionServicing
    private let noteCreationService: NoteCreationServicing
    private let dataManager: DataManaging

    private var cancellables = Set<AnyCancellable>()
    private var backgroundKeywords: [String] = []
    private let coordinatorQueue = DispatchQueue(label: "RecordingCoordinator", qos: .userInitiated)

    // MARK: - Initialization
    init(
        voiceRecordingService: VoiceRecordingServicing,
        keywordDetectionService: KeywordDetectionServicing,
        noteCreationService: NoteCreationServicing,
        dataManager: DataManaging
    ) {
        self.voiceRecordingService = voiceRecordingService
        self.keywordDetectionService = keywordDetectionService
        self.noteCreationService = noteCreationService
        self.dataManager = dataManager

        setupBindings()
        setupStateBindings()
    }
    
    // MARK: - Public Methods
    
    /// Start manual recording (triggered by headphone button)
    func startManualRecording() async {
        guard currentState == .idle else {
            await handleError("Cannot start manual recording: system is busy")
            return
        }
        
        do {
            let session = try await voiceRecordingService.startRecording(mode: .manual)
            activeSession = session
            currentState = .recording(session: session)
            
            print("🎙️ RecordingCoordinator: Started manual recording")
        } catch {
            await handleError("Failed to start manual recording: \(error.localizedDescription)")
        }
    }
    
    /// Stop current recording and create note
    func stopRecording() async {
        guard case .recording(let session) = currentState else {
            await handleError("No recording in progress")
            return
        }
        
        currentState = .processing
        
        do {
            let result = try await voiceRecordingService.stopRecording()
            
            // Determine keywords based on recording mode
            let keywords = await determineKeywords(for: result)
            
            // Create note
            let note = try await noteCreationService.createNote(from: result, keywords: keywords)
            
            lastCreatedNote = note
            activeSession = nil
            currentState = .idle
            
            print("✅ RecordingCoordinator: Created note '\(note.title)'")
            
            // Resume background monitoring if it was enabled
            if isBackgroundMonitoringEnabled {
                await startBackgroundMonitoring()
            }
            
        } catch {
            await handleError("Failed to create note: \(error.localizedDescription)")
        }
    }
    
    /// Cancel current recording without saving
    func cancelRecording() async {
        guard case .recording = currentState else { return }
        
        await voiceRecordingService.cancelRecording()
        activeSession = nil
        currentState = .idle
        
        print("❌ RecordingCoordinator: Cancelled recording")
        
        // Resume background monitoring if it was enabled
        if isBackgroundMonitoringEnabled {
            await startBackgroundMonitoring()
        }
    }
    
    /// Start background keyword monitoring
    func startBackgroundMonitoring() async {
        guard currentState == .idle else {
            print("⚠️ RecordingCoordinator: Cannot start background monitoring - system is busy")
            return
        }
        
        // Get enabled keywords from database
        let keywords = await getEnabledKeywords()
        guard !keywords.isEmpty else {
            await handleError("No keywords available for monitoring")
            return
        }
        
        do {
            try await keywordDetectionService.startListening(for: keywords)
            backgroundKeywords = keywords
            isBackgroundMonitoringEnabled = true
            currentState = .listening(keywords: keywords)
            
            print("👂 RecordingCoordinator: Started background monitoring for: \(keywords)")
        } catch {
            await handleError("Failed to start background monitoring: \(error.localizedDescription)")
        }
    }
    
    /// Stop background keyword monitoring
    func stopBackgroundMonitoring() async {
        guard case .listening = currentState else { return }
        
        await keywordDetectionService.stopListening()
        isBackgroundMonitoringEnabled = false
        backgroundKeywords = []
        currentState = .idle
        
        print("🛑 RecordingCoordinator: Stopped background monitoring")
    }
    
    /// Handle Siri shortcut request
    func handleSiriRequest(keyword: String?, content: String?) async {
        // If content is provided, create text note directly
        if let content = content, !content.isEmpty {
            await createTextNote(content: content, keyword: keyword)
            return
        }
        
        // If only keyword is provided, start recording with that keyword
        if let keyword = keyword {
            await startSiriRecording(with: keyword)
            return
        }
        
        // Fallback to manual recording
        await startManualRecording()
    }
    
    /// Create a text note (for Siri shortcuts)
    func createTextNote(content: String, keyword: String?) async {
        currentState = .processing
        
        do {
            let keywords = keyword != nil ? [keyword!] : []
            let title = generateSiriNoteTitle(keyword: keyword)
            
            let note = try await noteCreationService.createTextNote(
                title: title,
                content: content,
                keywords: keywords
            )
            
            lastCreatedNote = note
            currentState = .idle
            
            print("📝 RecordingCoordinator: Created Siri text note '\(note.title)'")
        } catch {
            await handleError("Failed to create Siri note: \(error.localizedDescription)")
        }
    }
}

// MARK: - Private Methods
private extension RecordingCoordinator {
    
    func setupBindings() {
        // Listen for keyword detections
        keywordDetectionService.detectionStream
            .sink { [weak self] detection in
                Task { @MainActor in
                    await self?.handleKeywordDetection(detection)
                }
            }
            .store(in: &cancellables)
    }
    
    func handleKeywordDetection(_ detection: KeywordDetection) async {
        guard case .listening = currentState else { return }
        
        print("🎯 RecordingCoordinator: Detected keyword '\(detection.keyword)'")
        
        // Stop listening and start recording
        await keywordDetectionService.stopListening()
        
        do {
            let session = try await voiceRecordingService.startRecording(mode: .background)
            activeSession = session
            currentState = .recording(session: session)
            
            // Auto-stop recording after a reasonable duration
            scheduleAutoStop(for: session, detectedKeyword: detection.keyword)
            
        } catch {
            await handleError("Failed to start background recording: \(error.localizedDescription)")
            // Resume listening on error
            if isBackgroundMonitoringEnabled {
                await startBackgroundMonitoring()
            }
        }
    }
    
    func scheduleAutoStop(for session: RecordingSession, detectedKeyword: String) {
        // Get user preferences for auto-stop duration
        let autoStopDuration: TimeInterval = 30.0 // Default 30 seconds, should come from preferences
        
        Task {
            try? await Task.sleep(nanoseconds: UInt64(autoStopDuration * 1_000_000_000))
            
            // Check if this session is still active
            if activeSession?.id == session.id {
                await stopRecording()
            }
        }
    }
    
    func determineKeywords(for result: RecordingResult) async -> [String] {
        switch result.session.mode {
        case .manual:
            // For manual recording, try to detect keywords in the transcription
            return await detectKeywordsInText(result.transcription)
            
        case .background:
            // For background recording, use the keyword that triggered it
            return backgroundKeywords.isEmpty ? [] : [backgroundKeywords.first!]
            
        case .siri:
            // For Siri recording, keywords should be provided separately
            return []
        }
    }
    
    func detectKeywordsInText(_ text: String) async -> [String] {
        let allKeywords = await getEnabledKeywords()
        let lowercaseText = text.lowercased()
        
        return allKeywords.filter { keyword in
            lowercaseText.contains(keyword.lowercased())
        }
    }
    
    func getEnabledKeywords() async -> [String] {
        // This should fetch from DataManager
        // For now, return empty array
        return []
    }
    
    func startSiriRecording(with keyword: String) async {
        // Start background monitoring with specific keyword focus
        backgroundKeywords = [keyword]
        await startBackgroundMonitoring()
    }
    
    func generateSiriNoteTitle(keyword: String?) -> String {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .short)
        
        if let keyword = keyword {
            return "\(keyword.capitalized) - \(timestamp)"
        } else {
            return "Siri Note - \(timestamp)"
        }
    }
    
    func handleError(_ message: String) async {
        print("❌ RecordingCoordinator: \(message)")
        updateRecordingState(.error(message))

        // Reset to idle after a delay
        Task {
            try? await Task.sleep(nanoseconds: 3_000_000_000) // 3 seconds
            if case .error = recordingState.value {
                updateRecordingState(.idle)
            }
        }
    }

    // MARK: - RecordingCoordinating Protocol Implementation

    func startManualRecording() async throws {
        guard case .idle = recordingState.value else {
            throw RecordingCoordinatorError.recordingInProgress
        }

        do {
            let session = try await voiceRecordingService.startRecording(mode: .manual)
            activeSession = session
            updateRecordingState(.recording(session: session))
            print("🎙️ RecordingCoordinator: Started manual recording")
        } catch {
            await handleError("Failed to start manual recording: \(error.localizedDescription)")
            throw error
        }
    }

    func stopManualRecording() async throws -> Note? {
        guard let session = activeSession,
              case .recording = recordingState.value else {
            throw RecordingCoordinatorError.noActiveRecording
        }

        updateRecordingState(.processing)

        do {
            let result = try await voiceRecordingService.stopRecording()
            let note = try await noteCreationService.createNote(from: result, transcription: result.transcription)

            activeSession = nil
            lastCreatedNote = note
            updateRecordingState(.idle)

            print("✅ RecordingCoordinator: Manual recording completed, note created")
            return note
        } catch {
            await handleError("Failed to stop manual recording: \(error.localizedDescription)")
            throw error
        }
    }

    func handleKeywordDetection(keyword: String) async throws {
        guard case .listening = recordingState.value else {
            print("⚠️ RecordingCoordinator: Keyword detected but not in listening state")
            return
        }

        do {
            let session = try await voiceRecordingService.startRecording(mode: .background)
            activeSession = session
            updateRecordingState(.recording(session: session))

            print("🎯 RecordingCoordinator: Started recording for keyword '\(keyword)'")

            // Auto-stop after a reasonable duration for background recording
            Task {
                try? await Task.sleep(nanoseconds: 30_000_000_000) // 30 seconds
                if let currentSession = activeSession, currentSession.id == session.id {
                    _ = try? await stopBackgroundRecording(for: keyword)
                }
            }
        } catch {
            await handleError("Failed to handle keyword detection: \(error.localizedDescription)")
            throw error
        }
    }

    func handleSiriShortcut(shortcutType: SiriShortcutType) async throws {
        switch shortcutType {
        case .startRecording:
            try await startManualRecording()

        case .stopRecording:
            _ = try await stopManualRecording()

        case .createQuickNote(let content):
            if let content = content, !content.isEmpty {
                let note = try await noteCreationService.createTextNote(
                    title: generateSiriNoteTitle(keyword: nil),
                    content: content,
                    keywords: []
                )
                lastCreatedNote = note
                print("📝 RecordingCoordinator: Created quick note via Siri")
            } else {
                try await startManualRecording()
            }
        }
    }

    func cancelCurrentRecording() async {
        if activeSession != nil {
            await voiceRecordingService.cancelRecording()
            activeSession = nil
        }

        if isBackgroundMonitoringEnabled {
            await keywordDetectionService.stopMonitoring()
            isBackgroundMonitoringEnabled = false
        }

        updateRecordingState(.idle)
        print("🚫 RecordingCoordinator: Cancelled current recording")
    }

    func enableBackgroundMonitoring(keywords: [String]) async throws {
        guard case .idle = recordingState.value else {
            throw RecordingCoordinatorError.recordingInProgress
        }

        do {
            backgroundKeywords = keywords
            try await keywordDetectionService.startMonitoring(for: keywords)
            isBackgroundMonitoringEnabled = true
            updateRecordingState(.listening(keywords: keywords))
            print("👂 RecordingCoordinator: Enabled background monitoring for keywords: \(keywords)")
        } catch {
            await handleError("Failed to enable background monitoring: \(error.localizedDescription)")
            throw error
        }
    }

    func disableBackgroundMonitoring() async {
        await keywordDetectionService.stopMonitoring()
        isBackgroundMonitoringEnabled = false
        backgroundKeywords = []

        if case .listening = recordingState.value {
            updateRecordingState(.idle)
        }

        print("🔇 RecordingCoordinator: Disabled background monitoring")
    }

    // MARK: - Private Helper Methods

    private func updateRecordingState(_ newState: RecordingState) {
        recordingState.send(newState)
    }

    private func setupStateBindings() {
        // Bind recording state changes to published properties
        recordingState
            .sink { [weak self] state in
                // Additional state-based logic can be added here
                switch state {
                case .idle:
                    self?.activeSession = nil
                case .recording(let session):
                    self?.activeSession = session
                default:
                    break
                }
            }
            .store(in: &cancellables)
    }

    private func stopBackgroundRecording(for keyword: String) async throws -> Note? {
        guard let session = activeSession,
              case .recording = recordingState.value else {
            return nil
        }

        updateRecordingState(.processing)

        do {
            let result = try await voiceRecordingService.stopRecording()
            let note = try await noteCreationService.createNote(from: result, transcription: result.transcription)

            activeSession = nil
            lastCreatedNote = note

            // Return to listening state if background monitoring is still enabled
            if isBackgroundMonitoringEnabled {
                updateRecordingState(.listening(keywords: backgroundKeywords))
            } else {
                updateRecordingState(.idle)
            }

            print("✅ RecordingCoordinator: Background recording completed for keyword '\(keyword)'")
            return note
        } catch {
            await handleError("Failed to stop background recording: \(error.localizedDescription)")
            throw error
        }
    }
}

// MARK: - Recording Coordinator Errors

enum RecordingCoordinatorError: LocalizedError {
    case recordingInProgress
    case noActiveRecording
    case invalidState
    case serviceUnavailable
    case coordinationFailed(underlying: Error)

    var errorDescription: String? {
        switch self {
        case .recordingInProgress:
            return "A recording is already in progress"
        case .noActiveRecording:
            return "No active recording to stop"
        case .invalidState:
            return "Invalid recording state for this operation"
        case .serviceUnavailable:
            return "Recording service is unavailable"
        case .coordinationFailed(let error):
            return "Recording coordination failed: \(error.localizedDescription)"
        }
    }
}

// MARK: - Convenience Extensions

extension RecordingState {
    var displayText: String {
        switch self {
        case .idle:
            return "Ready"
        case .listening(let keywords):
            return "Listening for: \(keywords.joined(separator: ", "))"
        case .recording(let session):
            return "Recording (\(session.mode))"
        case .processing:
            return "Processing..."
        case .error(let message):
            return "Error: \(message)"
        }
    }
}

extension RecordingSession.RecordingMode {
    var displayName: String {
        switch self {
        case .manual:
            return "Manual"
        case .background:
            return "Background"
        case .siri:
            return "Siri"
        }
    }
}
