//
//  ErrorView.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import SwiftUI

// MARK: - Error Severity

enum ErrorSeverity {
    case info
    case warning
    case error
    case critical
    
    var color: Color {
        switch self {
        case .info:
            return .blue
        case .warning:
            return .orange
        case .error:
            return .red
        case .critical:
            return .purple
        }
    }
    
    var icon: String {
        switch self {
        case .info:
            return "info.circle.fill"
        case .warning:
            return "exclamationmark.triangle.fill"
        case .error:
            return "xmark.circle.fill"
        case .critical:
            return "exclamationmark.octagon.fill"
        }
    }
}

// MARK: - Error Presentation Style

enum ErrorPresentationStyle {
    case banner
    case modal
    case inline
    case toast
}

// MARK: - Error Information

struct ErrorInfo {
    let error: Error
    let severity: ErrorSeverity
    let title: String?
    let message: String?
    let retryAction: (() -> Void)?
    let dismissAction: (() -> Void)?
    
    init(
        error: Error,
        severity: ErrorSeverity = .error,
        title: String? = nil,
        message: String? = nil,
        retryAction: (() -> Void)? = nil,
        dismissAction: (() -> Void)? = nil
    ) {
        self.error = error
        self.severity = severity
        self.title = title ?? "Error"
        self.message = message ?? error.localizedDescription
        self.retryAction = retryAction
        self.dismissAction = dismissAction
    }
}

// MARK: - Error View

struct ErrorView: View {
    let errorInfo: ErrorInfo
    let style: ErrorPresentationStyle
    @StateObject private var themeManager = ThemeManager.shared
    
    init(
        errorInfo: ErrorInfo,
        style: ErrorPresentationStyle = .modal
    ) {
        self.errorInfo = errorInfo
        self.style = style
    }
    
    var body: some View {
        switch style {
        case .banner:
            bannerView
        case .modal:
            modalView
        case .inline:
            inlineView
        case .toast:
            toastView
        }
    }
    
    // MARK: - Banner Style
    
    private var bannerView: some View {
        HStack(spacing: 12) {
            Image(systemName: errorInfo.severity.icon)
                .font(.title2)
                .foregroundColor(errorInfo.severity.color)
            
            VStack(alignment: .leading, spacing: 4) {
                if let title = errorInfo.title {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(.primary)
                }
                
                if let message = errorInfo.message {
                    Text(message)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
            }
            
            Spacer()
            
            if let dismissAction = errorInfo.dismissAction {
                Button(action: dismissAction) {
                    Image(systemName: "xmark")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(themeManager.currentTheme.backgroundColor.opacity(0.95))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(errorInfo.severity.color.opacity(0.3), lineWidth: 1)
                )
        )
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Modal Style
    
    private var modalView: some View {
        VStack(spacing: 20) {
            Image(systemName: errorInfo.severity.icon)
                .font(.system(size: 48))
                .foregroundColor(errorInfo.severity.color)
            
            VStack(spacing: 8) {
                if let title = errorInfo.title {
                    Text(title)
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                }
                
                if let message = errorInfo.message {
                    Text(message)
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
            }
            
            VStack(spacing: 12) {
                if let retryAction = errorInfo.retryAction {
                    Button("Retry") {
                        retryAction()
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.large)
                }
                
                if let dismissAction = errorInfo.dismissAction {
                    Button("Dismiss") {
                        dismissAction()
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.large)
                }
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(themeManager.currentTheme.backgroundColor)
                .shadow(color: .black.opacity(0.2), radius: 12, x: 0, y: 4)
        )
        .padding(.horizontal, 32)
    }
    
    // MARK: - Inline Style
    
    private var inlineView: some View {
        HStack(spacing: 12) {
            Image(systemName: errorInfo.severity.icon)
                .font(.title3)
                .foregroundColor(errorInfo.severity.color)
            
            VStack(alignment: .leading, spacing: 4) {
                if let title = errorInfo.title {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                }
                
                if let message = errorInfo.message {
                    Text(message)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            if let retryAction = errorInfo.retryAction {
                Button("Retry", action: retryAction)
                    .font(.caption)
                    .buttonStyle(.borderless)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(errorInfo.severity.color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 6)
                        .stroke(errorInfo.severity.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Toast Style
    
    private var toastView: some View {
        HStack(spacing: 8) {
            Image(systemName: errorInfo.severity.icon)
                .font(.caption)
                .foregroundColor(errorInfo.severity.color)
            
            if let message = errorInfo.message {
                Text(message)
                    .font(.caption)
                    .foregroundColor(.primary)
                    .lineLimit(1)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            Capsule()
                .fill(themeManager.currentTheme.backgroundColor.opacity(0.95))
                .overlay(
                    Capsule()
                        .stroke(errorInfo.severity.color.opacity(0.3), lineWidth: 1)
                )
        )
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// MARK: - Error Extensions

extension Error {
    var severity: ErrorSeverity {
        if let recordingError = self as? RecordingError {
            switch recordingError {
            case .permissionDenied:
                return .critical
            case .alreadyRecording, .notRecording:
                return .warning
            case .speechRecognitionUnavailable:
                return .error
            default:
                return .error
            }
        }
        
        if let audioSessionError = self as? AudioSessionError {
            switch audioSessionError {
            case .permissionDenied, .hardwareUnavailable:
                return .critical
            case .noSuitableRoute:
                return .error
            default:
                return .warning
            }
        }
        
        return .error
    }
    
    var userFriendlyMessage: String {
        if let recordingError = self as? RecordingError {
            switch recordingError {
            case .permissionDenied:
                return "Please allow microphone access in Settings to record notes."
            case .alreadyRecording:
                return "A recording is already in progress."
            case .notRecording:
                return "No recording is currently active."
            case .speechRecognitionUnavailable:
                return "Speech recognition is not available. Please try again later."
            default:
                return "Recording failed. Please try again."
            }
        }
        
        if let audioSessionError = self as? AudioSessionError {
            switch audioSessionError {
            case .permissionDenied:
                return "Microphone access is required to record notes."
            case .hardwareUnavailable:
                return "Audio hardware is not available."
            case .noSuitableRoute:
                return "No audio input device found."
            default:
                return "Audio setup failed. Please try again."
            }
        }
        
        return localizedDescription
    }
}

// MARK: - Preview

#if DEBUG
struct ErrorView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            ErrorView(
                errorInfo: ErrorInfo(
                    error: RecordingError.permissionDenied,
                    severity: .critical,
                    title: "Permission Required",
                    retryAction: {},
                    dismissAction: {}
                ),
                style: .modal
            )
            
            ErrorView(
                errorInfo: ErrorInfo(
                    error: AudioSessionError.configurationFailed(underlying: NSError(domain: "Test", code: 1)),
                    severity: .warning,
                    retryAction: {},
                    dismissAction: {}
                ),
                style: .banner
            )
            
            ErrorView(
                errorInfo: ErrorInfo(
                    error: NSError(domain: "Test", code: 1, userInfo: [NSLocalizedDescriptionKey: "Something went wrong"]),
                    severity: .info,
                    dismissAction: {}
                ),
                style: .toast
            )
        }
        .padding()
        .background(Color(.systemGroupedBackground))
    }
}
#endif
