//
//  ErrorHandler.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import SwiftUI
import Combine

// MARK: - Error Handling Service

@MainActor
class ErrorHandler: ObservableObject {
    
    // MARK: - Singleton
    static let shared = ErrorHandler()
    
    // MARK: - Published Properties
    @Published var currentError: ErrorInfo?
    @Published var errorQueue: [ErrorInfo] = []
    @Published var isShowingError = false
    
    // MARK: - Private Properties
    private var errorTimer: Timer?
    private let maxQueueSize = 5
    private let autoHideDelay: TimeInterval = 5.0
    
    // MARK: - Initialization
    private init() {}
    
    // MARK: - Public Methods
    
    func handle(
        _ error: Error,
        severity: ErrorSeverity? = nil,
        title: String? = nil,
        message: String? = nil,
        style: ErrorPresentationStyle = .modal,
        retryAction: (() -> Void)? = nil,
        autoHide: Bool = false
    ) {
        let errorInfo = ErrorInfo(
            error: error,
            severity: severity ?? .medium,
            title: title,
            message: message ?? error.userFriendlyMessage,
            retryAction: retryAction,
            dismissAction: { [weak self] in
                self?.dismiss()
            }
        )
        
        switch style {
        case .modal:
            showModalError(errorInfo)
        case .banner:
            showBannerError(errorInfo, autoHide: autoHide)
        case .inline:
            showInlineError(errorInfo)
        case .toast:
            showToastError(errorInfo)
        }
        
        // Log error for debugging
        logError(error, severity: errorInfo.severity)
    }
    
    func handleRecordingError(
        _ error: Error,
        retryAction: (() -> Void)? = nil
    ) {
        handle(
            error,
            severity: .high,
            title: "Recording Error",
            style: .modal,
            retryAction: retryAction
        )
    }
    
    func handleNetworkError(
        _ error: Error,
        retryAction: (() -> Void)? = nil
    ) {
        handle(
            error,
            severity: .warning,
            title: "Network Error",
            message: "Please check your internet connection and try again.",
            style: .banner,
            retryAction: retryAction,
            autoHide: true
        )
    }
    
    func handlePermissionError(
        _ error: Error,
        settingsAction: (() -> Void)? = nil
    ) {
        handle(
            error,
            severity: .critical,
            title: "Permission Required",
            style: .modal,
            retryAction: settingsAction
        )
    }
    
    func showInfo(
        _ message: String,
        title: String = "Info",
        style: ErrorPresentationStyle = .toast
    ) {
        let infoError = NSError(
            domain: "EchoNote.Info",
            code: 0,
            userInfo: [NSLocalizedDescriptionKey: message]
        )
        
        handle(
            infoError,
            severity: .low,
            title: title,
            style: style,
            autoHide: true
        )
    }
    
    func showWarning(
        _ message: String,
        title: String = "Warning",
        style: ErrorPresentationStyle = .banner
    ) {
        let warningError = NSError(
            domain: "EchoNote.Warning",
            code: 0,
            userInfo: [NSLocalizedDescriptionKey: message]
        )
        
        handle(
            warningError,
            severity: .warning,
            title: title,
            style: style,
            autoHide: true
        )
    }
    
    func dismiss() {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentError = nil
            isShowingError = false
        }
        
        errorTimer?.invalidate()
        errorTimer = nil
        
        // Show next error in queue if any
        processErrorQueue()
    }
    
    func clearAll() {
        currentError = nil
        errorQueue.removeAll()
        isShowingError = false
        errorTimer?.invalidate()
        errorTimer = nil
    }
    
    // MARK: - Private Methods
    
    private func showModalError(_ errorInfo: ErrorInfo) {
        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
            currentError = errorInfo
            isShowingError = true
        }
    }
    
    private func showBannerError(_ errorInfo: ErrorInfo, autoHide: Bool) {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentError = errorInfo
            isShowingError = true
        }
        
        if autoHide {
            startAutoHideTimer()
        }
    }
    
    private func showInlineError(_ errorInfo: ErrorInfo) {
        currentError = errorInfo
        isShowingError = true
    }
    
    private func showToastError(_ errorInfo: ErrorInfo) {
        // Add to queue if there's already an error showing
        if currentError != nil {
            addToQueue(errorInfo)
            return
        }
        
        withAnimation(.easeInOut(duration: 0.3)) {
            currentError = errorInfo
            isShowingError = true
        }
        
        startAutoHideTimer()
    }
    
    private func addToQueue(_ errorInfo: ErrorInfo) {
        if errorQueue.count >= maxQueueSize {
            errorQueue.removeFirst()
        }
        errorQueue.append(errorInfo)
    }
    
    private func processErrorQueue() {
        guard !errorQueue.isEmpty else { return }
        
        let nextError = errorQueue.removeFirst()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.showToastError(nextError)
        }
    }
    
    private func startAutoHideTimer() {
        errorTimer?.invalidate()
        errorTimer = Timer.scheduledTimer(withTimeInterval: autoHideDelay, repeats: false) { [weak self] _ in
            self?.dismiss()
        }
    }
    
    private func logError(_ error: Error, severity: ErrorSeverity) {
        let logLevel = severity == .critical ? "CRITICAL" : 
                      severity == .error ? "ERROR" : 
                      severity == .warning ? "WARNING" : "INFO"
        
        print("[\(logLevel)] ErrorHandler: \(error.localizedDescription)")
        
        // In a production app, you might want to send this to a logging service
        #if DEBUG
        print("Error details: \(error)")
        #endif
    }
}

// MARK: - Error Overlay Modifier

struct ErrorOverlayModifier: ViewModifier {
    @StateObject private var errorHandler = ErrorHandler.shared
    
    func body(content: Content) -> some View {
        ZStack {
            content
            
            // Error overlay
            if errorHandler.isShowingError, let errorInfo = errorHandler.currentError {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
                    .onTapGesture {
                        errorHandler.dismiss()
                    }
                
                ErrorView(errorInfo: errorInfo, style: .modal)
                    .transition(.scale.combined(with: .opacity))
                    .zIndex(1000)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: errorHandler.isShowingError)
    }
}

extension View {
    func withErrorHandling() -> some View {
        modifier(ErrorOverlayModifier())
    }
}

// MARK: - Banner Error Modifier

struct BannerErrorModifier: ViewModifier {
    @StateObject private var errorHandler = ErrorHandler.shared
    
    func body(content: Content) -> some View {
        VStack(spacing: 0) {
            // Banner error at top
            if errorHandler.isShowingError, 
               let errorInfo = errorHandler.currentError,
               errorInfo.severity == .warning {
                ErrorView(errorInfo: errorInfo, style: .banner)
                    .padding(.horizontal)
                    .padding(.top, 8)
                    .transition(.move(edge: .top).combined(with: .opacity))
            }
            
            content
        }
        .animation(.easeInOut(duration: 0.3), value: errorHandler.isShowingError)
    }
}

extension View {
    func withBannerErrors() -> some View {
        modifier(BannerErrorModifier())
    }
}

// MARK: - Toast Error Modifier

struct ToastErrorModifier: ViewModifier {
    @StateObject private var errorHandler = ErrorHandler.shared
    
    func body(content: Content) -> some View {
        ZStack(alignment: .top) {
            content
            
            // Toast error at top
            if errorHandler.isShowingError,
               let errorInfo = errorHandler.currentError,
               errorInfo.severity == .low {
                VStack {
                    ErrorView(errorInfo: errorInfo, style: .toast)
                        .padding(.top, 8)
                        .transition(.move(edge: .top).combined(with: .opacity))
                    
                    Spacer()
                }
                .zIndex(999)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: errorHandler.isShowingError)
    }
}

extension View {
    func withToastErrors() -> some View {
        modifier(ToastErrorModifier())
    }
}

// MARK: - Complete Error Handling

extension View {
    func withCompleteErrorHandling() -> some View {
        self
            .withErrorHandling()
            .withBannerErrors()
            .withToastErrors()
    }
}
