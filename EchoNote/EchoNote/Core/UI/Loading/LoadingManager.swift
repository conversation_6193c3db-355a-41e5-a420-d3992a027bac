//
//  LoadingManager.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import SwiftUI
import Combine

// MARK: - Loading Operation

struct LoadingOperation {
    let id: UUID
    let message: String
    let progress: Double?
    let style: LoadingStyle
    let isCancellable: Bool
    let cancelAction: (() -> Void)?
    
    init(
        id: UUID = UUID(),
        message: String,
        progress: Double? = nil,
        style: LoadingStyle = .circular,
        isCancellable: Bool = false,
        cancelAction: (() -> Void)? = nil
    ) {
        self.id = id
        self.message = message
        self.progress = progress
        self.style = style
        self.isCancellable = isCancellable
        self.cancelAction = cancelAction
    }
}

// MARK: - Loading Manager

@MainActor
class LoadingManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = LoadingManager()
    
    // MARK: - Published Properties
    @Published private(set) var isLoading = false
    @Published private(set) var currentOperation: LoadingOperation?
    @Published private(set) var operationQueue: [LoadingOperation] = []
    
    // MARK: - Private Properties
    private var activeOperations: [UUID: LoadingOperation] = [:]
    private let maxConcurrentOperations = 3
    
    // MARK: - Initialization
    private init() {}
    
    // MARK: - Public Methods
    
    @discardableResult
    func show(
        message: String = "Loading...",
        progress: Double? = nil,
        style: LoadingStyle = .circular,
        isCancellable: Bool = false,
        cancelAction: (() -> Void)? = nil
    ) -> UUID {
        let operation = LoadingOperation(
            message: message,
            progress: progress,
            style: style,
            isCancellable: isCancellable,
            cancelAction: cancelAction
        )
        
        activeOperations[operation.id] = operation
        updateCurrentOperation()
        
        return operation.id
    }
    
    func update(
        operationId: UUID,
        message: String? = nil,
        progress: Double? = nil
    ) {
        guard var operation = activeOperations[operationId] else { return }
        
        let updatedOperation = LoadingOperation(
            id: operation.id,
            message: message ?? operation.message,
            progress: progress ?? operation.progress,
            style: operation.style,
            isCancellable: operation.isCancellable,
            cancelAction: operation.cancelAction
        )
        
        activeOperations[operationId] = updatedOperation
        updateCurrentOperation()
    }
    
    func hide(operationId: UUID) {
        activeOperations.removeValue(forKey: operationId)
        updateCurrentOperation()
    }
    
    func hideAll() {
        activeOperations.removeAll()
        updateCurrentOperation()
    }
    
    func cancel(operationId: UUID) {
        guard let operation = activeOperations[operationId] else { return }
        
        if operation.isCancellable {
            operation.cancelAction?()
            hide(operationId: operationId)
        }
    }
    
    func cancelAll() {
        for operation in activeOperations.values {
            if operation.isCancellable {
                operation.cancelAction?()
            }
        }
        hideAll()
    }
    
    // MARK: - Convenience Methods
    
    @discardableResult
    func showRecording(message: String = "Recording...") -> UUID {
        return show(
            message: message,
            style: .recording,
            isCancellable: true
        ) { [weak self] in
            // Handle recording cancellation
            print("Recording cancelled by user")
        }
    }
    
    @discardableResult
    func showProcessing(message: String = "Processing...") -> UUID {
        return show(
            message: message,
            style: .pulse
        )
    }
    
    @discardableResult
    func showSaving(message: String = "Saving...") -> UUID {
        return show(
            message: message,
            style: .dots
        )
    }
    
    @discardableResult
    func showProgress(
        message: String = "Loading...",
        initialProgress: Double = 0.0,
        isCancellable: Bool = false,
        cancelAction: (() -> Void)? = nil
    ) -> UUID {
        return show(
            message: message,
            progress: initialProgress,
            style: .linear,
            isCancellable: isCancellable,
            cancelAction: cancelAction
        )
    }
    
    func updateProgress(operationId: UUID, progress: Double) {
        update(operationId: operationId, progress: progress)
    }
    
    // MARK: - Async Operation Helpers
    
    func withLoading<T>(
        message: String = "Loading...",
        style: LoadingStyle = .circular,
        operation: @escaping () async throws -> T
    ) async throws -> T {
        let operationId = show(message: message, style: style)
        
        defer {
            hide(operationId: operationId)
        }
        
        return try await operation()
    }
    
    func withProgress<T>(
        message: String = "Loading...",
        operation: @escaping (_ updateProgress: @escaping (Double) -> Void) async throws -> T
    ) async throws -> T {
        let operationId = showProgress(message: message)
        
        defer {
            hide(operationId: operationId)
        }
        
        let updateProgress: (Double) -> Void = { [weak self] progress in
            Task { @MainActor in
                self?.updateProgress(operationId: operationId, progress: progress)
            }
        }
        
        return try await operation(updateProgress)
    }
    
    // MARK: - Private Methods
    
    private func updateCurrentOperation() {
        if activeOperations.isEmpty {
            currentOperation = nil
            isLoading = false
        } else {
            // Show the most recent operation
            let sortedOperations = activeOperations.values.sorted { $0.id.uuidString < $1.id.uuidString }
            currentOperation = sortedOperations.last
            isLoading = true
        }
    }
}

// MARK: - Loading State Modifier

struct LoadingStateModifier: ViewModifier {
    @StateObject private var loadingManager = LoadingManager.shared
    let allowsInteraction: Bool
    
    func body(content: Content) -> some View {
        ZStack {
            content
                .disabled(loadingManager.isLoading && !allowsInteraction)
            
            if loadingManager.isLoading, let operation = loadingManager.currentOperation {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .transition(.opacity)
                
                VStack(spacing: 16) {
                    LoadingView(
                        message: operation.message,
                        progress: operation.progress,
                        style: operation.style,
                        isVisible: loadingManager.isLoading
                    )
                    
                    if operation.isCancellable {
                        Button("Cancel") {
                            loadingManager.cancel(operationId: operation.id)
                        }
                        .buttonStyle(.bordered)
                        .foregroundColor(.secondary)
                    }
                }
                .transition(.scale.combined(with: .opacity))
                .zIndex(100)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: loadingManager.isLoading)
    }
}

extension View {
    func withLoadingState(allowsInteraction: Bool = false) -> some View {
        modifier(LoadingStateModifier(allowsInteraction: allowsInteraction))
    }
}

// MARK: - Loading Context

struct LoadingContext {
    let manager: LoadingManager
    
    init(manager: LoadingManager = LoadingManager.shared) {
        self.manager = manager
    }
    
    func show(
        _ message: String,
        style: LoadingStyle = .circular,
        progress: Double? = nil
    ) async -> UUID {
        return await manager.show(message: message, progress: progress, style: style)
    }
    
    func hide(_ operationId: UUID) async {
        await manager.hide(operationId: operationId)
    }
    
    func update(_ operationId: UUID, message: String? = nil, progress: Double? = nil) async {
        await manager.update(operationId: operationId, message: message, progress: progress)
    }
}

// MARK: - Environment Key

struct LoadingContextKey: EnvironmentKey {
    static let defaultValue = LoadingContext()
}

extension EnvironmentValues {
    var loading: LoadingContext {
        get { self[LoadingContextKey.self] }
        set { self[LoadingContextKey.self] = newValue }
    }
}

// MARK: - Usage Examples

extension LoadingManager {
    
    // Example: Recording operation
    func startRecordingOperation() async {
        let operationId = showRecording(message: "Starting recording...")
        
        // Simulate recording setup
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        update(operationId: operationId, message: "Recording in progress...")
        
        // Simulate recording duration
        try? await Task.sleep(nanoseconds: 3_000_000_000) // 3 seconds
        
        hide(operationId: operationId)
    }
    
    // Example: File processing with progress
    func processFileOperation() async {
        let operationId = showProgress(message: "Processing file...")
        
        for i in 0...100 {
            updateProgress(operationId: operationId, progress: Double(i) / 100.0)
            try? await Task.sleep(nanoseconds: 50_000_000) // 50ms
        }
        
        hide(operationId: operationId)
    }
    
    // Example: Network operation with cancellation
    func networkOperation() async throws {
        let operationId = show(
            message: "Downloading...",
            style: .linear,
            isCancellable: true
        ) {
            // Cancel network request
            print("Network operation cancelled")
        }
        
        defer {
            hide(operationId: operationId)
        }
        
        // Simulate network operation
        for i in 0...10 {
            update(operationId: operationId, progress: Double(i) / 10.0)
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        }
    }
}
