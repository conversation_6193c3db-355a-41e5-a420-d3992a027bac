//
//  LoadingView.swift
//  EchoNote
//
//  Created by <PERSON> on 29/6/2025.
//

import SwiftUI

// MARK: - Loading Style

enum LoadingStyle {
    case circular
    case linear
    case dots
    case pulse
    case recording
    
    var animation: Animation {
        switch self {
        case .circular, .linear:
            return .linear(duration: 1.0).repeatForever(autoreverses: false)
        case .dots:
            return .easeInOut(duration: 0.6).repeatForever(autoreverses: true)
        case .pulse:
            return .easeInOut(duration: 1.0).repeatForever(autoreverses: true)
        case .recording:
            return .linear(duration: 2.0).repeatForever(autoreverses: false)
        }
    }
}

// MARK: - Loading Size

enum LoadingSize {
    case small
    case medium
    case large
    
    var scale: CGFloat {
        switch self {
        case .small:
            return 0.8
        case .medium:
            return 1.0
        case .large:
            return 1.5
        }
    }
    
    var padding: CGFloat {
        switch self {
        case .small:
            return 12
        case .medium:
            return 16
        case .large:
            return 24
        }
    }
}

// MARK: - Loading View

struct LoadingView: View {
    let message: String
    let progress: Double?
    let style: LoadingStyle
    let size: LoadingSize
    let isVisible: Bool
    
    @StateObject private var themeManager = ThemeManager.shared
    @State private var animationValue: CGFloat = 0
    
    init(
        message: String = "Loading...",
        progress: Double? = nil,
        style: LoadingStyle = .circular,
        size: LoadingSize = .medium,
        isVisible: Bool = true
    ) {
        self.message = message
        self.progress = progress
        self.style = style
        self.size = size
        self.isVisible = isVisible
    }
    
    var body: some View {
        if isVisible {
            VStack(spacing: size.padding) {
                loadingIndicator
                
                Text(message)
                    .font(messageFont)
                    .multilineTextAlignment(.center)
                    .foregroundColor(.primary)
                    .lineLimit(3)
            }
            .padding(size.padding)
            .background(backgroundView)
            .scaleEffect(size.scale)
            .opacity(isVisible ? 1 : 0)
            .animation(.easeInOut(duration: 0.3), value: isVisible)
            .onAppear {
                startAnimation()
            }
        }
    }
    
    // MARK: - Loading Indicators
    
    @ViewBuilder
    private var loadingIndicator: some View {
        switch style {
        case .circular:
            circularIndicator
        case .linear:
            linearIndicator
        case .dots:
            dotsIndicator
        case .pulse:
            pulseIndicator
        case .recording:
            recordingIndicator
        }
    }
    
    private var circularIndicator: some View {
        Group {
            if let progress = progress {
                ProgressView(value: progress, total: 1.0)
                    .progressViewStyle(CircularProgressViewStyle(tint: themeManager.accentColor))
                    .scaleEffect(1.2)
            } else {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: themeManager.accentColor))
                    .scaleEffect(1.2)
            }
        }
    }
    
    private var linearIndicator: some View {
        VStack(spacing: 8) {
            if let progress = progress {
                ProgressView(value: progress, total: 1.0)
                    .progressViewStyle(LinearProgressViewStyle(tint: themeManager.accentColor))
                    .frame(width: 120)
                
                Text("\(Int(progress * 100))%")
                    .font(.caption)
                    .foregroundColor(.secondary)
            } else {
                ProgressView()
                    .progressViewStyle(LinearProgressViewStyle(tint: themeManager.accentColor))
                    .frame(width: 120)
            }
        }
    }
    
    private var dotsIndicator: some View {
        HStack(spacing: 8) {
            ForEach(0..<3, id: \.self) { index in
                Circle()
                    .fill(themeManager.accentColor)
                    .frame(width: 8, height: 8)
                    .scaleEffect(animationValue == CGFloat(index) ? 1.5 : 1.0)
                    .animation(
                        .easeInOut(duration: 0.6)
                        .repeatForever(autoreverses: true)
                        .delay(Double(index) * 0.2),
                        value: animationValue
                    )
            }
        }
    }
    
    private var pulseIndicator: some View {
        ZStack {
            Circle()
                .fill(themeManager.accentColor.opacity(0.3))
                .frame(width: 40, height: 40)
                .scaleEffect(1 + animationValue * 0.5)
            
            Circle()
                .fill(themeManager.accentColor)
                .frame(width: 20, height: 20)
                .scaleEffect(1 + animationValue * 0.2)
        }
        .animation(style.animation, value: animationValue)
    }
    
    private var recordingIndicator: some View {
        HStack(spacing: 4) {
            ForEach(0..<5, id: \.self) { index in
                RoundedRectangle(cornerRadius: 2)
                    .fill(themeManager.accentColor)
                    .frame(width: 4, height: CGFloat.random(in: 10...30))
                    .animation(
                        .easeInOut(duration: Double.random(in: 0.3...0.8))
                        .repeatForever(autoreverses: true)
                        .delay(Double(index) * 0.1),
                        value: animationValue
                    )
            }
        }
    }
    
    // MARK: - Supporting Views
    
    private var backgroundView: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(themeManager.surfaceColor.opacity(0.95))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(themeManager.accentColor.opacity(0.2), lineWidth: 1)
            )
            .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
    }
    
    private var messageFont: Font {
        switch size {
        case .small:
            return .caption
        case .medium:
            return .subheadline
        case .large:
            return .headline
        }
    }
    
    // MARK: - Animation
    
    private func startAnimation() {
        withAnimation(style.animation) {
            animationValue = 1.0
        }
    }
}

// MARK: - Loading Overlay Modifier

struct LoadingOverlayModifier: ViewModifier {
    let isLoading: Bool
    let message: String
    let progress: Double?
    let style: LoadingStyle
    let allowsInteraction: Bool
    
    func body(content: Content) -> some View {
        ZStack {
            content
                .disabled(isLoading && !allowsInteraction)
            
            if isLoading {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .transition(.opacity)
                
                LoadingView(
                    message: message,
                    progress: progress,
                    style: style,
                    isVisible: isLoading
                )
                .transition(.scale.combined(with: .opacity))
                .zIndex(100)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: isLoading)
    }
}

extension View {
    func loadingOverlay(
        isLoading: Bool,
        message: String = "Loading...",
        progress: Double? = nil,
        style: LoadingStyle = .circular,
        allowsInteraction: Bool = false
    ) -> some View {
        modifier(LoadingOverlayModifier(
            isLoading: isLoading,
            message: message,
            progress: progress,
            style: style,
            allowsInteraction: allowsInteraction
        ))
    }
}

// MARK: - Inline Loading Modifier

struct InlineLoadingModifier: ViewModifier {
    let isLoading: Bool
    let message: String
    let style: LoadingStyle
    let size: LoadingSize
    
    func body(content: Content) -> some View {
        VStack {
            if isLoading {
                LoadingView(
                    message: message,
                    style: style,
                    size: size,
                    isVisible: isLoading
                )
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
            
            content
                .opacity(isLoading ? 0.6 : 1.0)
                .animation(.easeInOut(duration: 0.3), value: isLoading)
        }
    }
}

extension View {
    func inlineLoading(
        isLoading: Bool,
        message: String = "Loading...",
        style: LoadingStyle = .dots,
        size: LoadingSize = .small
    ) -> some View {
        modifier(InlineLoadingModifier(
            isLoading: isLoading,
            message: message,
            style: style,
            size: size
        ))
    }
}

// MARK: - Preview

#if DEBUG
struct LoadingView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 30) {
            LoadingView(message: "Circular Loading", style: .circular)
            LoadingView(message: "Linear Progress", progress: 0.6, style: .linear)
            LoadingView(message: "Dots Animation", style: .dots, size: .small)
            LoadingView(message: "Pulse Effect", style: .pulse)
            LoadingView(message: "Recording...", style: .recording)
        }
        .padding()
        .background(Color(.systemGroupedBackground))
    }
}
#endif
