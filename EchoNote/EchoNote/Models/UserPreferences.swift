//
//  UserPreferences.swift
//  EchoNote
//
//  Temporary model for migration compatibility
//

import Foundation
import SwiftData

@Model
class UserPreferences {
    var recordingQuality: String
    var backgroundMonitoringEnabled: Bool
    var transcriptionEnabled: Bool
    var autoStopDuration: Int
    var maxRecordingDuration: Int
    var saveAudioEnabled: Bool
    var darkModeEnabled: Bool?
    var selectedTheme: String
    var animationSpeed: Double
    var notificationsEnabled: Bool
    var hapticFeedbackEnabled: Bool
    var createdAt: Date
    var updatedAt: Date
    
    init(
        recordingQuality: String = "medium",
        backgroundMonitoringEnabled: Bool = false,
        transcriptionEnabled: Bool = true,
        autoStopDuration: Int = 30,
        maxRecordingDuration: Int = 600,
        saveAudioEnabled: Bool = true,
        darkModeEnabled: Bool? = nil,
        selectedTheme: String = "default",
        animationSpeed: Double = 1.0,
        notificationsEnabled: Bool = true,
        hapticFeedbackEnabled: Bool = true
    ) {
        self.recordingQuality = recordingQuality
        self.backgroundMonitoringEnabled = backgroundMonitoringEnabled
        self.transcriptionEnabled = transcriptionEnabled
        self.autoStopDuration = autoStopDuration
        self.maxRecordingDuration = maxRecordingDuration
        self.saveAudioEnabled = saveAudioEnabled
        self.darkModeEnabled = darkModeEnabled
        self.selectedTheme = selectedTheme
        self.animationSpeed = animationSpeed
        self.notificationsEnabled = notificationsEnabled
        self.hapticFeedbackEnabled = hapticFeedbackEnabled
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}
