//
//  SiriShortcutsService.swift
//  EchoNote
//
//  Created by <PERSON> on 27/6/2025.
//

import Foundation
import Intents
import IntentsUI

// MARK: - Siri Shortcuts Service

@MainActor
class SiriShortcutsService: ObservableObject {
    
    // MARK: - Properties

    static let shared = SiriShortcutsService()
    private let siriIntegrationManager = SiriIntegrationManager.shared

    // MARK: - Initialization

    private init() {
        // Initialize Siri integration
        Task {
            await siriIntegrationManager.registerShortcuts()
        }
    }
    
    // MARK: - Intent Donation
    
    /// Donate a "Start Recording" activity to Siri
    func donateStartRecordingIntent(with keyword: String? = nil) {
        let activity = NSUserActivity(activityType: "com.clevorie.EchoNote.startRecording")
        activity.title = "Start Recording"
        activity.suggestedInvocationPhrase = keyword != nil ? "Record \(keyword!)" : "Start recording"
        activity.isEligibleForSearch = true
        activity.isEligibleForPrediction = true
        activity.persistentIdentifier = "startRecording"
        
        if let keyword = keyword {
            activity.userInfo = ["keyword": keyword]
        }
        
        activity.becomeCurrent()
        
        LoggingService.shared.info("Donated start recording intent", category: .system, metadata: [
            "keyword": keyword ?? "none"
        ])
    }
    
    /// Donate a "Quick Note" activity to Siri
    func donateQuickNoteIntent(with title: String) {
        let activity = NSUserActivity(activityType: "com.clevorie.EchoNote.quickNote")
        activity.title = "Quick Note: \(title)"
        activity.suggestedInvocationPhrase = "Quick note \(title)"
        activity.isEligibleForSearch = true
        activity.isEligibleForPrediction = true
        activity.persistentIdentifier = "quickNote_\(title)"
        activity.userInfo = ["title": title]
        
        activity.becomeCurrent()
        
        LoggingService.shared.info("Donated quick note intent", category: .system, metadata: [
            "title": title
        ])
    }
    
    /// Donate a "Play Last Recording" activity to Siri
    func donatePlayLastRecordingIntent() {
        let activity = NSUserActivity(activityType: "com.clevorie.EchoNote.playLastRecording")
        activity.title = "Play Last Recording"
        activity.suggestedInvocationPhrase = "Play my last recording"
        activity.isEligibleForSearch = true
        activity.isEligibleForPrediction = true
        activity.persistentIdentifier = "playLastRecording"
        
        activity.becomeCurrent()
        
        LoggingService.shared.info("Donated play last recording intent", category: .system)
    }
    
    /// Donate a "Search Notes" activity to Siri
    func donateSearchNotesIntent(with query: String) {
        let activity = NSUserActivity(activityType: "com.clevorie.EchoNote.searchNotes")
        activity.title = "Search Notes"
        activity.suggestedInvocationPhrase = "Search my notes for \(query)"
        activity.isEligibleForSearch = true
        activity.isEligibleForPrediction = true
        activity.persistentIdentifier = "searchNotes_\(query)"
        activity.userInfo = ["query": query]
        
        activity.becomeCurrent()
        
        LoggingService.shared.info("Donated search notes intent", category: .system, metadata: [
            "query": query
        ])
    }
    
    // MARK: - Intent Handling
    
    /// Handle incoming Siri shortcuts
    func handleUserActivity(_ userActivity: NSUserActivity) -> Bool {
        let activityType = userActivity.activityType

        LoggingService.shared.info("Handling Siri shortcut", category: .system, metadata: [
            "activityType": activityType
        ])

        // Use SiriIntegrationManager to handle user activities
        Task {
            let handled = await siriIntegrationManager.handleUserActivity(userActivity)
            if !handled {
                // Fallback to legacy handling if needed
                await handleLegacyUserActivity(userActivity)
            }
        }

        return true // Return true to indicate we're handling it asynchronously
    }

    /// Legacy handling for backward compatibility
    private func handleLegacyUserActivity(_ userActivity: NSUserActivity) async {
        let activityType = userActivity.activityType

        switch activityType {
        case "com.clevorie.EchoNote.startRecording":
            _ = handleStartRecordingIntent(userActivity)

        case "com.clevorie.EchoNote.quickNote":
            _ = handleQuickNoteIntent(userActivity)

        case "com.clevorie.EchoNote.playLastRecording":
            _ = handlePlayLastRecordingIntent(userActivity)

        case "com.clevorie.EchoNote.searchNotes":
            _ = handleSearchNotesIntent(userActivity)

        default:
            LoggingService.shared.warning("Unknown Siri shortcut activity type: \(activityType)", category: .system)
        }
    }
    
    private func handleStartRecordingIntent(_ userActivity: NSUserActivity) -> Bool {
        let keyword = userActivity.userInfo?["keyword"] as? String

        // Use SiriIntegrationManager to handle the shortcut
        Task {
            do {
                try await siriIntegrationManager.handleShortcut(SiriShortcutType.startRecording)
                LoggingService.shared.info("Handled start recording intent via SiriIntegrationManager", category: .system, metadata: [
                    "keyword": keyword ?? "none"
                ])
            } catch {
                LoggingService.shared.error("Failed to handle start recording intent", category: .system, metadata: [
                    "error": error.localizedDescription,
                    "keyword": keyword ?? "none"
                ])
            }
        }

        return true
    }
    
    private func handleQuickNoteIntent(_ userActivity: NSUserActivity) -> Bool {
        let title = userActivity.userInfo?["title"] as? String ?? "Quick Note"

        // Use SiriIntegrationManager to handle the shortcut
        Task {
            do {
                try await siriIntegrationManager.handleShortcut(SiriShortcutType.createQuickNote(content: title))
                LoggingService.shared.info("Handled quick note intent via SiriIntegrationManager", category: .system, metadata: [
                    "title": title
                ])
            } catch {
                LoggingService.shared.error("Failed to handle quick note intent", category: .system, metadata: [
                    "error": error.localizedDescription,
                    "title": title
                ])
            }
        }

        return true
    }
    
    private func handlePlayLastRecordingIntent(_ userActivity: NSUserActivity) -> Bool {
        // This functionality would need to be implemented in the new architecture
        // For now, we'll log that it's not yet supported
        LoggingService.shared.warning("Play last recording intent not yet implemented in new architecture", category: .system)

        return false
    }
    
    private func handleSearchNotesIntent(_ userActivity: NSUserActivity) -> Bool {
        guard let query = userActivity.userInfo?["query"] as? String else { return false }

        // This functionality would need to be implemented in the new architecture
        // For now, we'll log that it's not yet supported
        LoggingService.shared.warning("Search notes intent not yet implemented in new architecture", category: .system, metadata: [
            "query": query
        ])

        return false
    }
    
    // MARK: - Shortcut Management
    
    /// Get available shortcuts
    func getAvailableShortcuts() -> [SiriShortcut] {
        return [
            SiriShortcut(
                id: "smartNote",
                title: "Smart Note",
                subtitle: "Say 'keyword: content' to create categorized notes",
                phrase: "Record task: finish the report",
                activityType: "SmartNoteIntent",
                iconName: "brain.head.profile"
            ),
            SiriShortcut(
                id: "quickRecord",
                title: "Quick Voice Recording",
                subtitle: "Start recording with optional keyword",
                phrase: "Start recording for shopping",
                activityType: "QuickRecordIntent",
                iconName: "mic.circle.fill"
            ),
            SiriShortcut(
                id: "addNote",
                title: "Add Categorized Note",
                subtitle: "Create a note with specific keyword and content",
                phrase: "Add work note: meeting at 3pm",
                activityType: "AddNoteIntent",
                iconName: "plus.circle.fill"
            ),
            SiriShortcut(
                id: "startRecording",
                title: "Start Recording (Legacy)",
                subtitle: "Begin recording a new voice note",
                phrase: "Start recording",
                activityType: "com.clevorie.EchoNote.startRecording",
                iconName: "headphones"
            ),
            SiriShortcut(
                id: "searchNotes",
                title: "Search Notes",
                subtitle: "Search through your notes",
                phrase: "Search my notes",
                activityType: "com.clevorie.EchoNote.searchNotes",
                iconName: "magnifyingglass"
            )
        ]
    }
    
    /// Create a voice shortcut for a specific activity
    func createVoiceShortcut(for shortcut: SiriShortcut, completion: @escaping (Bool) -> Void) {
        let activity = NSUserActivity(activityType: shortcut.activityType)
        activity.title = shortcut.title
        activity.suggestedInvocationPhrase = shortcut.phrase
        activity.isEligibleForSearch = true
        activity.isEligibleForPrediction = true
        activity.persistentIdentifier = shortcut.id
        
        if #available(iOS 12.0, *) {
            let voiceShortcut = INShortcut(userActivity: activity)
            _ = INUIAddVoiceShortcutViewController(shortcut: voiceShortcut)

            // Note: In a real implementation, you would present this view controller
            // For now, we'll just mark it as successful
            completion(true)
            
            LoggingService.shared.info("Created voice shortcut", category: .system, metadata: [
                "shortcutId": shortcut.id,
                "title": shortcut.title
            ])
        } else {
            completion(false)
        }
    }
    
    // MARK: - Contextual Suggestions
    
    /// Suggest shortcuts based on user behavior
    func suggestContextualShortcuts(for context: ShortcutContext) {
        switch context {
        case .afterRecording:
            donateStartRecordingIntent()
            
        case .afterPlayback:
            donatePlayLastRecordingIntent()
            
        case .afterSearch(let query):
            donateSearchNotesIntent(with: query)
            
        case .keywordDetected(let keyword):
            donateStartRecordingIntent(with: keyword)
        }
    }
}

// MARK: - Siri Shortcut Model

struct SiriShortcut: Identifiable {
    let id: String
    let title: String
    let subtitle: String
    let phrase: String
    let activityType: String
    let iconName: String
}

// MARK: - Shortcut Context

enum ShortcutContext {
    case afterRecording
    case afterPlayback
    case afterSearch(String)
    case keywordDetected(String)
}

// MARK: - Notification Names

extension Notification.Name {
    static let siriStartRecording = Notification.Name("siriStartRecording")
    static let siriQuickNote = Notification.Name("siriQuickNote")
    static let siriPlayLastRecording = Notification.Name("siriPlayLastRecording")
    static let siriSearchNotes = Notification.Name("siriSearchNotes")

    // New parameterized intents
    static let siriCreateNoteWithKeyword = Notification.Name("siriCreateNoteWithKeyword")
    static let siriStartRecordingWithKeyword = Notification.Name("siriStartRecordingWithKeyword")
}
