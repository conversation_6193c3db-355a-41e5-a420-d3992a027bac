# EchoNote Component Guide

## Table of Contents

1. [Service Components](#service-components)
2. [Coordination Components](#coordination-components)
3. [UI Components](#ui-components)
4. [Infrastructure Components](#infrastructure-components)
5. [Usage Examples](#usage-examples)
6. [Best Practices](#best-practices)

## Service Components

### VoiceRecordingService

#### Overview
The VoiceRecordingService is responsible for all audio recording functionality, including real-time transcription and audio processing.

#### Key Features
- Audio recording with configurable quality
- Real-time speech-to-text transcription
- Audio level monitoring
- Recording session management
- Audio file processing

#### Usage Example
```swift
// Initialize service
let voiceService = VoiceRecordingService(
    audioSessionManager: audioSessionManager,
    speechRecognizer: speechRecognizer
)

// Start recording
let session = try await voiceService.startRecording(mode: .manual)

// Monitor recording progress
voiceService.recordingProgress
    .sink { progress in
        print("Duration: \(progress.duration)")
        print("Transcription: \(progress.transcription)")
    }
    .store(in: &cancellables)

// Stop recording
let result = try await voiceService.stopRecording()
```

#### Configuration Options
```swift
struct RecordingConfiguration {
    let quality: RecordingQuality
    let enableTranscription: Bool
    let autoStopDuration: TimeInterval?
    let maxDuration: TimeInterval?
}
```

### KeywordDetectionService

#### Overview
Monitors audio input for predefined keywords and triggers recording when detected.

#### Key Features
- Background audio monitoring
- Configurable keyword sensitivity
- Real-time keyword detection
- Multiple keyword support
- Detection confidence scoring

#### Usage Example
```swift
// Initialize service
let keywordService = KeywordDetectionService()

// Set up keyword detection
let keywords = ["note", "reminder", "todo"]
try await keywordService.startMonitoring(for: keywords)

// Listen for detections
keywordService.detectionStream
    .sink { detection in
        print("Detected: \(detection.keyword) with confidence: \(detection.confidence)")
    }
    .store(in: &cancellables)

// Update sensitivity
await keywordService.setDetectionSensitivity(0.8)
```

#### Detection Configuration
```swift
struct DetectionConfiguration {
    let sensitivity: Float // 0.0 to 1.0
    let minimumConfidence: Float
    let cooldownPeriod: TimeInterval
    let maxConcurrentDetections: Int
}
```

### NoteCreationService

#### Overview
Creates and manages notes from recording results and text input.

#### Key Features
- Note creation from audio recordings
- Text note creation
- Automatic metadata extraction
- Keyword assignment
- Note validation

#### Usage Example
```swift
// Create note from recording
let note = try await noteCreationService.createNote(
    from: recordingResult,
    title: "Meeting Notes",
    keywords: [meetingKeyword]
)

// Create text note
let textNote = try await noteCreationService.createTextNote(
    title: "Quick Reminder",
    content: "Buy groceries",
    keywords: [reminderKeyword]
)
```

### AudioSessionManager

#### Overview
Manages iOS audio session configuration and lifecycle.

#### Key Features
- Audio session category management
- Route change handling
- Interruption management
- Permission handling

#### Usage Example
```swift
// Configure for recording
try await audioSessionManager.configureSession(for: .recording)

// Activate session
try await audioSessionManager.activateSession()

// Monitor session state
audioSessionManager.$currentSessionState
    .sink { state in
        switch state {
        case .active:
            print("Audio session is active")
        case .inactive:
            print("Audio session is inactive")
        case .error(let error):
            print("Audio session error: \(error)")
        }
    }
    .store(in: &cancellables)
```

### SiriIntegrationManager

#### Overview
Handles Siri shortcuts and voice command integration.

#### Key Features
- Shortcut registration and donation
- Intent handling
- User activity processing
- Voice command routing

#### Usage Example
```swift
// Register shortcuts
await siriIntegrationManager.registerShortcuts()

// Handle shortcut
try await siriIntegrationManager.handleShortcut(.startRecording)

// Donate intent
await siriIntegrationManager.donateRecordingIntent()
```

## Coordination Components

### RecordingCoordinator

#### Overview
Central coordinator that orchestrates all recording-related activities across different modes and services.

#### Key Responsibilities
- State management across recording modes
- Service coordination
- Error handling and recovery
- Event publishing

#### Usage Example
```swift
// Manual recording
try await recordingCoordinator.startManualRecording()
let note = try await recordingCoordinator.stopManualRecording()

// Background monitoring
try await recordingCoordinator.enableBackgroundMonitoring(keywords: ["note", "reminder"])

// Siri shortcuts
try await recordingCoordinator.handleSiriShortcut(shortcutType: .startRecording)

// Monitor state changes
recordingCoordinator.recordingState
    .sink { state in
        switch state {
        case .idle:
            print("Ready to record")
        case .recording(let session):
            print("Recording session: \(session.id)")
        case .processing:
            print("Processing recording")
        case .error(let error):
            print("Recording error: \(error)")
        }
    }
    .store(in: &cancellables)
```

### ServiceManager

#### Overview
Dependency injection container that manages service creation and lifecycle.

#### Key Features
- Service instantiation
- Dependency resolution
- Lifecycle management
- Mock service provision

#### Usage Example
```swift
// Get services
let recordingCoordinator = await serviceManager.getRecordingCoordinator()
let keywordService = await serviceManager.getKeywordDetectionService()
let dataManager = await serviceManager.getDataManager()

// Configure with custom implementations
serviceManager.configure(
    voiceRecordingService: CustomVoiceRecordingService(),
    dataManager: CustomDataManager()
)
```

## UI Components

### HomeView

#### Overview
Main recording interface with quick access to recording functionality.

#### Key Features
- Recording button with visual feedback
- Recent notes display
- Background monitoring status
- Quick actions

#### Usage Example
```swift
struct HomeView: View {
    @Environment(\.serviceManager) private var serviceManager
    @State private var recordingCoordinator: RecordingCoordinator?
    
    var body: some View {
        VStack {
            RecordingButton(coordinator: recordingCoordinator)
            RecentNotesView()
            BackgroundMonitoringStatusView()
        }
        .onAppear {
            Task {
                recordingCoordinator = await serviceManager.getRecordingCoordinator()
            }
        }
    }
}
```

### RecordingView

#### Overview
Full-screen recording interface with real-time feedback.

#### Key Features
- Real-time transcription display
- Audio level visualization
- Recording controls
- Timer display

#### Usage Example
```swift
struct RecordingView: View {
    @Binding var isPresented: Bool
    @Environment(\.serviceManager) private var serviceManager
    @State private var recordingCoordinator: RecordingCoordinator?
    
    var body: some View {
        VStack {
            TranscriptionView()
            AudioVisualizerView()
            RecordingControlsView(
                onStop: stopRecording,
                onCancel: cancelRecording
            )
        }
        .onAppear {
            Task {
                recordingCoordinator = await serviceManager.getRecordingCoordinator()
                try? await recordingCoordinator?.startManualRecording()
            }
        }
    }
    
    private func stopRecording() {
        Task {
            _ = try? await recordingCoordinator?.stopManualRecording()
            isPresented = false
        }
    }
    
    private func cancelRecording() {
        Task {
            await recordingCoordinator?.cancelCurrentRecording()
            isPresented = false
        }
    }
}
```

### NotesView

#### Overview
Notes management interface with list/grid views and search functionality.

#### Key Features
- Multiple view modes (list/grid)
- Search and filtering
- Note actions (edit, delete, favorite)
- Keyword-based organization

#### Usage Example
```swift
struct NotesView: View {
    @Environment(\.dataManager) private var dataManager
    @State private var notes: [Note] = []
    @State private var searchText = ""
    @State private var viewMode: ViewMode = .list
    
    var body: some View {
        NavigationView {
            VStack {
                SearchBar(text: $searchText)
                ViewModeToggle(mode: $viewMode)
                
                if viewMode == .list {
                    NotesListView(notes: filteredNotes)
                } else {
                    NotesGridView(notes: filteredNotes)
                }
            }
        }
        .onAppear {
            loadNotes()
        }
    }
    
    private var filteredNotes: [Note] {
        if searchText.isEmpty {
            return notes
        } else {
            return notes.filter { note in
                note.title.localizedCaseInsensitiveContains(searchText) ||
                note.content.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
    
    private func loadNotes() {
        Task {
            do {
                notes = try await dataManager.fetchNotes()
            } catch {
                print("Failed to load notes: \(error)")
            }
        }
    }
}
```

## Infrastructure Components

### EventBus

#### Overview
Centralized event system using Combine for type-safe event handling.

#### Key Features
- Type-safe event publishing
- Multiple event subscribers
- Event filtering and transformation
- Automatic memory management

#### Usage Example
```swift
// Subscribe to events
EventBus.shared.recordingStarted
    .sink { event in
        print("Recording started: \(event.session.id)")
    }
    .store(in: &cancellables)

// Publish events
EventBus.shared.publishRecordingStarted(session: recordingSession)

// Subscribe to multiple event types
EventBus.shared.recordingEvents
    .sink { event in
        switch event {
        case let startedEvent as RecordingStartedEvent:
            handleRecordingStarted(startedEvent)
        case let stoppedEvent as RecordingStoppedEvent:
            handleRecordingStopped(stoppedEvent)
        default:
            break
        }
    }
    .store(in: &cancellables)
```

### ErrorRecoveryManager

#### Overview
Automatic error recovery system with configurable strategies.

#### Key Features
- Automatic error recovery
- Configurable recovery strategies
- Recovery attempt tracking
- User intervention handling

#### Usage Example
```swift
// Register custom recovery strategy
ErrorRecoveryManager.shared.registerRecoveryStrategy(
    for: CustomError.self,
    strategy: .retry(maxAttempts: 3, delay: 1.0)
)

// Attempt recovery
let context = RecoveryContext(
    originalError: error,
    component: "Recording",
    operation: "startRecording"
)
let result = await ErrorRecoveryManager.shared.attemptRecovery(for: context)

switch result {
case .success:
    print("Recovery successful")
case .failed(let error):
    print("Recovery failed: \(error)")
case .requiresUserAction(let message):
    showUserDialog(message: message)
}
```

### MemoryOptimizationManager

#### Overview
Automatic memory optimization based on system pressure and usage patterns.

#### Key Features
- Real-time memory monitoring
- Automatic optimization triggers
- Configurable optimization strategies
- Performance metrics

#### Usage Example
```swift
// Start memory monitoring
MemoryOptimizationManager.shared.startMemoryMonitoring()

// Monitor memory usage
MemoryOptimizationManager.shared.$currentMemoryUsage
    .compactMap { $0 }
    .sink { usage in
        print("Memory pressure: \(usage.memoryPressure)")
        print("App usage: \(usage.formattedAppMemoryUsage)")
    }
    .store(in: &cancellables)

// Manual optimization
await MemoryOptimizationManager.shared.optimizeMemoryUsage()

// Apply specific strategy
await MemoryOptimizationManager.shared.applyOptimizationStrategy(.clearCaches)
```

## Usage Examples

### Complete Recording Workflow

```swift
class RecordingWorkflowExample {
    private let serviceManager = ServiceManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    func performCompleteRecordingWorkflow() async throws {
        // Get coordinator
        let coordinator = await serviceManager.getRecordingCoordinator()
        
        // Monitor state changes
        coordinator.recordingState
            .sink { state in
                print("Recording state: \(state)")
            }
            .store(in: &cancellables)
        
        // Start recording
        try await coordinator.startManualRecording()
        
        // Simulate recording duration
        try await Task.sleep(nanoseconds: 5_000_000_000) // 5 seconds
        
        // Stop recording and get note
        let note = try await coordinator.stopManualRecording()
        
        print("Created note: \(note?.title ?? "Unknown")")
    }
}
```

### Background Monitoring Setup

```swift
class BackgroundMonitoringExample {
    private let serviceManager = ServiceManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    func setupBackgroundMonitoring() async throws {
        let coordinator = await serviceManager.getRecordingCoordinator()
        
        // Define keywords
        let keywords = ["note", "reminder", "todo", "meeting"]
        
        // Enable background monitoring
        try await coordinator.enableBackgroundMonitoring(keywords: keywords)
        
        // Listen for created notes
        EventBus.shared.noteCreated
            .sink { event in
                print("Background note created: \(event.note.title)")
            }
            .store(in: &cancellables)
        
        print("Background monitoring enabled for keywords: \(keywords)")
    }
}
```

### Custom Service Implementation

```swift
class CustomVoiceRecordingService: VoiceRecordingServicing {
    @Published var isRecording = false
    let recordingState = CurrentValueSubject<RecordingState, Never>(.idle)
    @Published var currentSession: RecordingSession?
    @Published var recordingDuration: TimeInterval = 0
    @Published var transcribedText = ""
    @Published var audioLevels: [Float] = []
    
    var recordingProgress: AnyPublisher<RecordingProgress, Never> {
        // Custom implementation
        Timer.publish(every: 0.1, on: .main, in: .common)
            .autoconnect()
            .map { _ in
                RecordingProgress(
                    duration: self.recordingDuration,
                    transcription: self.transcribedText,
                    audioLevels: self.audioLevels,
                    isActive: self.isRecording
                )
            }
            .eraseToAnyPublisher()
    }
    
    func startRecording(mode: RecordingSession.RecordingMode) async throws -> RecordingSession {
        // Custom recording implementation
        let session = RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: URL(fileURLWithPath: "/custom/path.m4a"),
            mode: mode
        )
        
        currentSession = session
        isRecording = true
        recordingState.send(.recording(session: session))
        
        return session
    }
    
    // Implement other required methods...
}
```

## Best Practices

### Service Implementation

1. **Always implement protocols**: Use protocol definitions for all services
2. **Handle errors gracefully**: Provide meaningful error messages and recovery options
3. **Use async/await**: Prefer modern concurrency over completion handlers
4. **Publish state changes**: Use Combine publishers for reactive state management
5. **Validate inputs**: Always validate parameters before processing

### UI Development

1. **Use environment objects**: Inject services through SwiftUI environment
2. **Handle loading states**: Show appropriate loading indicators
3. **Provide feedback**: Give users clear feedback on actions
4. **Handle errors in UI**: Display user-friendly error messages
5. **Optimize for accessibility**: Support VoiceOver and other accessibility features

### Testing

1. **Mock all dependencies**: Use protocol-based mocking for isolated testing
2. **Test error scenarios**: Verify error handling and recovery
3. **Use async testing**: Properly test async/await code
4. **Test state changes**: Verify reactive state updates
5. **Performance testing**: Monitor memory and CPU usage

### Memory Management

1. **Use weak references**: Avoid retain cycles in closures
2. **Cancel subscriptions**: Store Combine cancellables and clean up
3. **Monitor memory usage**: Use MemoryOptimizationManager for monitoring
4. **Optimize audio handling**: Efficiently manage audio buffers and files
5. **Clean up resources**: Properly dispose of resources when done

### Error Handling

1. **Use typed errors**: Define specific error types for different scenarios
2. **Provide recovery options**: Implement automatic recovery where possible
3. **Log errors appropriately**: Use structured logging for debugging
4. **Handle user intervention**: Guide users through error resolution
5. **Test error scenarios**: Verify error handling in all components

---

*This component guide should be referenced when implementing new features or modifying existing components.*
