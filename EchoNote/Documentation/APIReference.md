# EchoNote API Reference

## Table of Contents

1. [Service Protocols](#service-protocols)
2. [Data Models](#data-models)
3. [Event Types](#event-types)
4. [Error Types](#error-types)
5. [Configuration Types](#configuration-types)
6. [Utility Types](#utility-types)

## Service Protocols

### VoiceRecordingServicing

```swift
protocol VoiceRecordingServicing: ObservableObject {
    // MARK: - Published Properties
    var isRecording: Bool { get }
    var recordingState: CurrentValueSubject<RecordingState, Never> { get }
    var currentSession: RecordingSession? { get }
    var recordingDuration: TimeInterval { get }
    var transcribedText: String { get }
    var audioLevels: [Float] { get }
    var recordingProgress: AnyPublisher<RecordingProgress, Never> { get }
    
    // MARK: - Recording Methods
    func startRecording(mode: RecordingSession.RecordingMode) async throws -> RecordingSession
    func stopRecording() async throws -> RecordingResult
    func pauseRecording() async throws
    func resumeRecording() async throws
    func cancelRecording() async
    
    // MARK: - Transcription Methods
    func transcribeAudio(from url: URL) async throws -> String
}
```

### KeywordDetectionServicing

```swift
protocol KeywordDetectionServicing: ObservableObject {
    // MARK: - Published Properties
    var isMonitoring: Bool { get }
    var activeKeywords: [String] { get }
    var detectionState: CurrentValueSubject<DetectionState, Never> { get }
    var detectionStream: AnyPublisher<KeywordDetection, Never> { get }
    
    // MARK: - Monitoring Methods
    func startMonitoring(for keywords: [String]) async throws
    func stopMonitoring() async
    func updateKeywords(_ keywords: [String]) async
    func setDetectionSensitivity(_ sensitivity: Float) async
}
```

### NoteCreationServicing

```swift
protocol NoteCreationServicing {
    // MARK: - Note Creation Methods
    func createNote(
        from result: RecordingResult,
        title: String?,
        keywords: [Keyword]
    ) async throws -> Note
    
    func createTextNote(
        title: String,
        content: String,
        keywords: [Keyword]
    ) async throws -> Note
}
```

### AudioSessionManaging

```swift
protocol AudioSessionManaging: ObservableObject {
    // MARK: - Published Properties
    var currentSessionState: AudioSessionState { get }
    
    // MARK: - Session Management Methods
    func configureSession(for mode: RecordingMode) async throws
    func activateSession() async throws
    func deactivateSession() async throws
}
```

### RecordingCoordinating

```swift
protocol RecordingCoordinating: ObservableObject {
    // MARK: - Published Properties
    var recordingState: CurrentValueSubject<RecordingState, Never> { get }
    var activeSession: RecordingSession? { get }
    var lastCreatedNote: Note? { get }
    
    // MARK: - Manual Recording Methods
    func startManualRecording() async throws
    func stopManualRecording() async throws -> Note?
    func cancelCurrentRecording() async
    
    // MARK: - Background Monitoring Methods
    func enableBackgroundMonitoring(keywords: [String]) async throws
    func disableBackgroundMonitoring() async
    func handleKeywordDetection(keyword: String) async throws
    
    // MARK: - Siri Integration Methods
    func handleSiriShortcut(shortcutType: SiriShortcutType) async throws
}
```

### DataManaging

```swift
protocol DataManaging {
    // MARK: - Note Management
    func saveNote(_ note: Note) async throws
    func fetchNotes() async throws -> [Note]
    func fetchNote(withId id: UUID) async throws -> Note?
    func updateNote(_ note: Note) async throws
    func deleteNote(_ note: Note) async throws
    
    // MARK: - Keyword Management
    func fetchKeywords() async throws -> [Keyword]
    func saveKeywords(_ keywords: [Keyword]) async throws
    
    // MARK: - Generic Data Operations
    func save() async throws
    func delete<T: PersistentModel>(_ model: T) async throws
    func fetch<T: PersistentModel>(_ type: T.Type) async throws -> [T]
    func insert<T: PersistentModel>(_ model: T) async
}
```

## Data Models

### Note

```swift
@Model
class Note: ObservableObject, Identifiable {
    // MARK: - Properties
    let id: UUID
    var title: String
    var content: String
    var audioURL: URL?
    var duration: TimeInterval
    var createdAt: Date
    var updatedAt: Date
    var keywords: [Keyword]
    var isFavorite: Bool
    
    // MARK: - Initialization
    init(
        title: String,
        content: String,
        audioURL: URL? = nil,
        duration: TimeInterval = 0,
        keywords: [Keyword] = [],
        isFavorite: Bool = false
    )
    
    // MARK: - Computed Properties
    var hasAudio: Bool { audioURL != nil }
    var formattedDuration: String { /* Implementation */ }
    var keywordTexts: [String] { keywords.map { $0.text } }
}
```

### Keyword

```swift
struct Keyword: Identifiable, Codable, Hashable {
    // MARK: - Properties
    let id: UUID
    var text: String
    var color: String
    var createdAt: Date
    var lastUsed: Date?
    var usageCount: Int
    
    // MARK: - Initialization
    init(
        text: String,
        color: String = "blue",
        usageCount: Int = 0
    )
    
    // MARK: - Computed Properties
    var isRecentlyUsed: Bool { /* Implementation */ }
    var isFrequentlyUsed: Bool { /* Implementation */ }
    var usageFrequency: KeywordUsageFrequency { /* Implementation */ }
}
```

### RecordingSession

```swift
struct RecordingSession: Identifiable, Codable {
    // MARK: - Properties
    let id: UUID
    let startTime: Date
    let audioURL: URL
    let mode: RecordingMode
    
    // MARK: - Nested Types
    enum RecordingMode: String, Codable, CaseIterable {
        case manual = "manual"
        case background = "background"
        case siri = "siri"
    }
    
    // MARK: - Computed Properties
    var duration: TimeInterval { /* Implementation */ }
    var isActive: Bool { /* Implementation */ }
}
```

### RecordingResult

```swift
struct RecordingResult {
    // MARK: - Properties
    let session: RecordingSession
    let duration: TimeInterval
    let transcription: String
    let audioURL: URL
    let metadata: RecordingMetadata?
    
    // MARK: - Computed Properties
    var hasTranscription: Bool { !transcription.isEmpty }
    var fileSize: Int64 { /* Implementation */ }
}
```

### RecordingProgress

```swift
struct RecordingProgress {
    // MARK: - Properties
    let duration: TimeInterval
    let transcription: String
    let audioLevels: [Float]
    let isActive: Bool
    
    // MARK: - Computed Properties
    var formattedDuration: String { /* Implementation */ }
    var averageAudioLevel: Float { /* Implementation */ }
    var peakAudioLevel: Float { /* Implementation */ }
}
```

## Event Types

### AppEvent Protocol

```swift
protocol AppEvent {
    var timestamp: Date { get }
    var eventId: UUID { get }
}
```

### Recording Events

```swift
struct RecordingStartedEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let session: RecordingSession
}

struct RecordingStoppedEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let result: RecordingResult
}

struct RecordingFailedEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let error: Error
    let sessionId: UUID?
}

struct RecordingCancelledEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let sessionId: UUID
}
```

### Note Events

```swift
struct NoteCreatedEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let note: Note
}

struct NoteUpdatedEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let note: Note
    let previousVersion: Note?
}

struct NoteDeletedEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let noteId: UUID
    let note: Note?
}
```

### Keyword Events

```swift
struct KeywordDetectedEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let keyword: String
    let confidence: Float
    let context: String?
}

struct KeywordsUpdatedEvent: AppEvent {
    let timestamp: Date
    let eventId: UUID
    let keywords: [Keyword]
}
```

## Error Types

### RecordingError

```swift
enum RecordingError: LocalizedError {
    case permissionDenied
    case audioSessionConfigurationFailed
    case alreadyRecording
    case notRecording
    case recordingFailed(underlying: Error)
    case transcriptionFailed(underlying: Error)
    case fileNotFound(url: URL)
    case invalidAudioFormat
    
    var errorDescription: String? { /* Implementation */ }
    var recoverySuggestion: String? { /* Implementation */ }
}
```

### AudioSessionError

```swift
enum AudioSessionError: LocalizedError {
    case permissionDenied
    case configurationFailed(underlying: Error)
    case activationFailed(underlying: Error)
    case deactivationFailed(underlying: Error)
    case routeChangeFailed
    case interruptionHandlingFailed
    
    var errorDescription: String? { /* Implementation */ }
}
```

### KeywordError

```swift
enum KeywordError: LocalizedError {
    case validationFailed(String)
    case keywordNotFound
    case dataStorageError(Error)
    case detectionServiceError(Error)
    
    var errorDescription: String? { /* Implementation */ }
}
```

### SiriIntegrationError

```swift
enum SiriIntegrationError: LocalizedError {
    case coordinatorUnavailable
    case authorizationDenied
    case shortcutRegistrationFailed(underlying: Error)
    case shortcutHandlingFailed(underlying: Error)
    case intentDonationFailed(underlying: Error)
    
    var errorDescription: String? { /* Implementation */ }
}
```

## Configuration Types

### AppUserPreferences

```swift
struct AppUserPreferences: Codable, Equatable {
    // MARK: - Recording Preferences
    var recordingQuality: RecordingQuality
    var backgroundMonitoringEnabled: Bool
    var transcriptionEnabled: Bool
    var autoStopDuration: AutoStopDuration
    var maxRecordingDuration: MaxRecordingDuration
    var saveAudioEnabled: Bool
    
    // MARK: - UI Preferences
    var darkModeEnabled: Bool?
    var selectedTheme: String
    var animationSpeed: Double
    
    // MARK: - System Preferences
    var notificationsEnabled: Bool
    var hapticFeedbackEnabled: Bool
    
    // MARK: - Default Configuration
    static let `default`: AppUserPreferences
}
```

### RecordingQuality

```swift
enum RecordingQuality: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    
    var displayName: String { /* Implementation */ }
    var sampleRate: Double { /* Implementation */ }
    var bitRate: Int { /* Implementation */ }
    var fileSize: String { /* Implementation */ }
}
```

### AutoStopDuration

```swift
enum AutoStopDuration: Int, CaseIterable, Codable {
    case seconds5 = 5
    case seconds10 = 10
    case seconds15 = 15
    case seconds30 = 30
    case minute1 = 60
    case minute2 = 120
    case never = -1
    
    var displayName: String { /* Implementation */ }
    var timeInterval: TimeInterval? { /* Implementation */ }
}
```

### MaxRecordingDuration

```swift
enum MaxRecordingDuration: Int, CaseIterable, Codable {
    case minute1 = 60
    case minute2 = 120
    case minute5 = 300
    case minute10 = 600
    case minute15 = 900
    case minute30 = 1800
    case unlimited = -1
    
    var displayName: String { /* Implementation */ }
    var timeInterval: TimeInterval? { /* Implementation */ }
}
```

## Utility Types

### RecordingState

```swift
enum RecordingState: Equatable {
    case idle
    case listening
    case recording(session: RecordingSession)
    case processing
    case error(Error)
    
    var isRecording: Bool { /* Implementation */ }
    var isProcessing: Bool { /* Implementation */ }
    var hasError: Bool { /* Implementation */ }
}
```

### DetectionState

```swift
enum DetectionState: Equatable {
    case inactive
    case monitoring
    case keywordDetected(String)
    case failed(Error)
    
    var isMonitoring: Bool { /* Implementation */ }
    var isActive: Bool { /* Implementation */ }
}
```

### AudioSessionState

```swift
enum AudioSessionState: Equatable {
    case inactive
    case configured(RecordingMode)
    case active
    case error(Error)
    
    var isActive: Bool { /* Implementation */ }
    var isConfigured: Bool { /* Implementation */ }
}
```

### SiriShortcutType

```swift
enum SiriShortcutType {
    case startRecording
    case stopRecording
    case createQuickNote(content: String)
    
    var identifier: String { /* Implementation */ }
    var title: String { /* Implementation */ }
    var description: String { /* Implementation */ }
}
```

### KeywordDetection

```swift
struct KeywordDetection {
    let keyword: String
    let confidence: Float
    let timestamp: Date
    let context: String?
    
    var isHighConfidence: Bool { confidence > 0.8 }
    var formattedTimestamp: String { /* Implementation */ }
}
```

### MemoryUsageMetrics

```swift
struct MemoryUsageMetrics {
    let totalMemory: UInt64
    let usedMemory: UInt64
    let availableMemory: UInt64
    let appMemoryUsage: UInt64
    let timestamp: Date
    
    var memoryPressure: MemoryPressureLevel { /* Implementation */ }
    var formattedAppMemoryUsage: String { /* Implementation */ }
}
```

### RecoveryContext

```swift
struct RecoveryContext {
    let originalError: Error
    let component: String
    let operation: String
    let timestamp: Date
    let attemptCount: Int
    let userContext: [String: Any]
}
```

### RecoveryResult

```swift
enum RecoveryResult {
    case success
    case partialSuccess(message: String)
    case failed(error: Error)
    case requiresUserAction(message: String)
    
    var isSuccessful: Bool { /* Implementation */ }
}
```

---

## Usage Notes

### Async/Await Patterns

All service methods that perform I/O operations or long-running tasks use async/await:

```swift
// Correct usage
try await voiceRecordingService.startRecording(mode: .manual)

// Error handling
do {
    let result = try await voiceRecordingService.stopRecording()
    // Handle success
} catch {
    // Handle error
}
```

### Combine Publishers

Services expose state through Combine publishers:

```swift
// Subscribe to state changes
voiceRecordingService.$isRecording
    .sink { isRecording in
        // Update UI
    }
    .store(in: &cancellables)

// Subscribe to events
EventBus.shared.recordingStarted
    .sink { event in
        // Handle event
    }
    .store(in: &cancellables)
```

### Error Handling

Always handle errors appropriately:

```swift
do {
    try await operation()
} catch let recordingError as RecordingError {
    // Handle specific recording error
} catch {
    // Handle general error
}
```

### Memory Management

Use weak references to avoid retain cycles:

```swift
service.publisher
    .sink { [weak self] value in
        self?.handleValue(value)
    }
    .store(in: &cancellables)
```

---

*This API reference is automatically generated from the source code and should be kept in sync with implementation changes.*
