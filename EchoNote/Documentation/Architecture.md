# EchoNote Architecture Documentation

## Table of Contents

1. [Overview](#overview)
2. [Architecture Principles](#architecture-principles)
3. [Core Components](#core-components)
4. [Service Layer](#service-layer)
5. [Coordination Layer](#coordination-layer)
6. [UI Layer](#ui-layer)
7. [Data Layer](#data-layer)
8. [Event System](#event-system)
9. [Error Handling](#error-handling)
10. [Memory Management](#memory-management)
11. [Recording Flows](#recording-flows)
12. [State Management](#state-management)
13. [Testing Strategy](#testing-strategy)
14. [Performance Considerations](#performance-considerations)
15. [Design Decisions](#design-decisions)

## Overview

EchoNote is a voice recording and note-taking application built using modern iOS development practices. The architecture follows SOLID principles and uses a protocol-oriented design to provide clean separation of concerns, improved testability, and maintainable code.

### Key Features

- **Manual Recording**: User-initiated voice recordings with real-time transcription
- **Background Keyword Monitoring**: Automatic recording triggered by keyword detection
- **Siri Integration**: Voice shortcuts for hands-free operation
- **Note Management**: Organized storage and retrieval of voice notes
- **Multi-modal Input**: Support for both voice and text notes

## Architecture Principles

### 1. Protocol-Oriented Design
- All services are defined by protocols
- Enables dependency injection and easy testing
- Allows for future implementation changes without breaking existing code

### 2. Separation of Concerns
- Clear boundaries between UI, business logic, and data layers
- Each component has a single responsibility
- Minimal coupling between components

### 3. Reactive Programming
- Uses Combine framework for state management
- Event-driven architecture with EventBus
- Reactive UI updates based on state changes

### 4. Modern Swift Concurrency
- async/await for asynchronous operations
- Actors for thread safety where needed
- Structured concurrency for task management

### 5. Dependency Injection
- ServiceManager provides centralized service creation
- Mock services for testing
- Configurable service implementations

## Core Components

```mermaid
graph TB
    subgraph "UI Layer"
        A[HomeView]
        B[RecordingView]
        C[NotesView]
        D[ProfileView]
        E[MainTabView]
    end
    
    subgraph "Coordination Layer"
        F[RecordingCoordinator]
        G[ServiceManager]
    end
    
    subgraph "Service Layer"
        H[VoiceRecordingService]
        I[KeywordDetectionService]
        J[NoteCreationService]
        K[AudioSessionManager]
        L[SiriIntegrationManager]
        M[PreferencesManager]
        N[KeywordManager]
    end
    
    subgraph "Infrastructure"
        O[EventBus]
        P[ErrorRecoveryManager]
        Q[MemoryOptimizationManager]
        R[DataManager]
    end
    
    A --> F
    B --> F
    C --> F
    D --> M
    E --> F
    
    F --> H
    F --> I
    F --> J
    F --> K
    F --> L
    
    G --> F
    G --> H
    G --> I
    G --> J
    G --> K
    G --> L
    G --> M
    G --> N
    
    H --> K
    I --> O
    J --> R
    L --> F
    
    O --> P
    O --> Q
```

## Service Layer

### VoiceRecordingService

**Purpose**: Handles audio recording, processing, and speech recognition.

**Key Responsibilities**:
- Audio session configuration
- Recording start/stop/pause/resume
- Real-time audio level monitoring
- Speech-to-text transcription
- Audio file management

**Protocol Definition**:
```swift
protocol VoiceRecordingServicing: ObservableObject {
    var isRecording: Bool { get }
    var recordingState: CurrentValueSubject<RecordingState, Never> { get }
    var currentSession: RecordingSession? { get }
    var recordingProgress: AnyPublisher<RecordingProgress, Never> { get }
    
    func startRecording(mode: RecordingSession.RecordingMode) async throws -> RecordingSession
    func stopRecording() async throws -> RecordingResult
    func pauseRecording() async throws
    func resumeRecording() async throws
    func cancelRecording() async
    func transcribeAudio(from url: URL) async throws -> String
}
```

### KeywordDetectionService

**Purpose**: Monitors audio input for predefined keywords in the background.

**Key Responsibilities**:
- Background audio monitoring
- Keyword recognition using speech recognition
- Configurable sensitivity settings
- Real-time detection events

**Protocol Definition**:
```swift
protocol KeywordDetectionServicing: ObservableObject {
    var isMonitoring: Bool { get }
    var activeKeywords: [String] { get }
    var detectionState: CurrentValueSubject<DetectionState, Never> { get }
    var detectionStream: AnyPublisher<KeywordDetection, Never> { get }
    
    func startMonitoring(for keywords: [String]) async throws
    func stopMonitoring() async
    func updateKeywords(_ keywords: [String]) async
    func setDetectionSensitivity(_ sensitivity: Float) async
}
```

### NoteCreationService

**Purpose**: Creates and manages notes from recording results.

**Key Responsibilities**:
- Note creation from audio recordings
- Text note creation
- Metadata extraction and assignment
- Integration with data persistence

**Protocol Definition**:
```swift
protocol NoteCreationServicing {
    func createNote(from result: RecordingResult, title: String?, keywords: [Keyword]) async throws -> Note
    func createTextNote(title: String, content: String, keywords: [Keyword]) async throws -> Note
}
```

### AudioSessionManager

**Purpose**: Manages iOS audio session configuration and lifecycle.

**Key Responsibilities**:
- Audio session category configuration
- Audio route management
- Interruption handling
- Permission management

### SiriIntegrationManager

**Purpose**: Handles Siri shortcuts and voice commands.

**Key Responsibilities**:
- Shortcut registration and donation
- Intent handling
- User activity processing
- Voice command routing

## Coordination Layer

### RecordingCoordinator

**Purpose**: Central coordinator for all recording-related activities.

**Key Responsibilities**:
- Orchestrates recording workflows
- Manages state transitions
- Coordinates between services
- Handles different recording modes (manual, background, Siri)

**State Management**:
```swift
enum RecordingState {
    case idle
    case listening
    case recording(session: RecordingSession)
    case processing
    case error(Error)
}
```

### ServiceManager

**Purpose**: Dependency injection container and service factory.

**Key Responsibilities**:
- Service instantiation and configuration
- Dependency resolution
- Service lifecycle management
- Mock service provision for testing

## UI Layer

### Component Hierarchy

```mermaid
graph TD
    A[App] --> B[ContentView]
    B --> C[MainTabView]
    C --> D[HomeView]
    C --> E[NotesView]
    C --> F[ProfileView]
    
    D --> G[RecordingButton]
    D --> H[FloatingRecordingButton]
    
    E --> I[NotesListView]
    E --> J[NoteCardView]
    E --> K[NoteDetailView]
    
    F --> L[SettingsView]
    F --> M[PreferencesView]
    
    N[RecordingView] --> O[RecordingControlsView]
    N --> P[TranscriptionView]
    N --> Q[AudioVisualizerView]
```

### Key UI Components

#### HomeView
- Main recording interface
- Quick access to recording functionality
- Recent notes display
- Background monitoring status

#### RecordingView
- Full-screen recording interface
- Real-time transcription display
- Audio level visualization
- Recording controls (stop, pause, cancel)

#### NotesView
- Notes list and grid views
- Search and filtering
- Note management actions
- Keyword-based organization

#### ProfileView
- User preferences and settings
- Recording quality settings
- Background monitoring configuration
- Theme and appearance options

## Data Layer

### Core Data Models

```swift
// Note entity
class Note: ObservableObject, Identifiable {
    let id: UUID
    var title: String
    var content: String
    var audioURL: URL?
    var duration: TimeInterval
    var createdAt: Date
    var updatedAt: Date
    var keywords: [Keyword]
    var isFavorite: Bool
}

// Keyword entity
struct Keyword: Identifiable, Codable {
    let id: UUID
    var text: String
    var color: String
    var createdAt: Date
    var lastUsed: Date?
    var usageCount: Int
}

// User preferences
struct AppUserPreferences: Codable {
    var recordingQuality: RecordingQuality
    var backgroundMonitoringEnabled: Bool
    var transcriptionEnabled: Bool
    var autoStopDuration: AutoStopDuration
    var maxRecordingDuration: MaxRecordingDuration
    var saveAudioEnabled: Bool
    var darkModeEnabled: Bool?
    var selectedTheme: String
    var animationSpeed: Double
    var notificationsEnabled: Bool
    var hapticFeedbackEnabled: Bool
}
```

### Data Persistence

- **SwiftData**: Primary data persistence framework
- **UserDefaults**: User preferences and settings
- **File System**: Audio file storage
- **iCloud**: Optional cloud synchronization

## Event System

### EventBus Architecture

The EventBus provides a centralized, type-safe event system using Combine:

```swift
// Event protocol
protocol AppEvent {
    var timestamp: Date { get }
    var eventId: UUID { get }
}

// Event types
struct RecordingStartedEvent: AppEvent
struct RecordingStoppedEvent: AppEvent
struct NoteCreatedEvent: AppEvent
struct KeywordDetectedEvent: AppEvent
struct ErrorOccurredEvent: AppEvent
```

### Event Flow

```mermaid
sequenceDiagram
    participant UI as UI Component
    participant Coordinator as RecordingCoordinator
    participant Service as VoiceRecordingService
    participant EventBus as EventBus
    participant Subscriber as Event Subscriber
    
    UI->>Coordinator: Start Recording
    Coordinator->>Service: startRecording()
    Service->>EventBus: publishRecordingStarted()
    EventBus->>Subscriber: RecordingStartedEvent
    Subscriber->>UI: Update State
```

## Error Handling

### Error Recovery System

The ErrorRecoveryManager provides automatic error recovery:

```swift
enum RecoveryStrategy {
    case retry(maxAttempts: Int, delay: TimeInterval)
    case fallback(action: () async throws -> Void)
    case reset(component: String)
    case userIntervention(message: String)
    case ignore
}
```

### Error Types

- **RecordingError**: Audio recording and processing errors
- **AudioSessionError**: Audio session configuration errors
- **KeywordError**: Keyword detection and management errors
- **SiriIntegrationError**: Siri shortcuts and integration errors
- **ErrorRecoveryError**: Error recovery system errors

### Error Handling Flow

```mermaid
graph TD
    A[Error Occurs] --> B[ErrorRecoveryManager]
    B --> C{Recovery Strategy?}
    C -->|Retry| D[Retry Operation]
    C -->|Fallback| E[Execute Fallback]
    C -->|Reset| F[Reset Component]
    C -->|User Action| G[Show User Dialog]
    C -->|Ignore| H[Continue]
    
    D --> I{Success?}
    I -->|Yes| J[Continue]
    I -->|No| K[Escalate Error]
    
    E --> L{Success?}
    L -->|Yes| M[Partial Success]
    L -->|No| K
    
    F --> N{Success?}
    N -->|Yes| J
    N -->|No| K
```

## Memory Management

### MemoryOptimizationManager

Provides automatic memory optimization based on system pressure:

```swift
enum MemoryOptimizationStrategy {
    case clearCaches
    case releaseUnusedResources
    case compressAudioData
    case limitConcurrentOperations
    case reduceImageQuality
    case purgeOldRecordings
}
```

### Memory Monitoring

- Real-time memory usage tracking
- Automatic optimization triggers
- Configurable optimization strategies
- Performance metrics collection

## Recording Flows

### Manual Recording Flow

```mermaid
sequenceDiagram
    participant User
    participant UI as HomeView
    participant Coordinator as RecordingCoordinator
    participant VRS as VoiceRecordingService
    participant ASM as AudioSessionManager
    participant NCS as NoteCreationService
    
    User->>UI: Tap Record Button
    UI->>Coordinator: startManualRecording()
    Coordinator->>ASM: configureSession(for: .recording)
    ASM-->>Coordinator: Session Configured
    Coordinator->>VRS: startRecording(mode: .manual)
    VRS-->>Coordinator: RecordingSession
    Coordinator-->>UI: Recording Started
    
    User->>UI: Tap Stop Button
    UI->>Coordinator: stopManualRecording()
    Coordinator->>VRS: stopRecording()
    VRS-->>Coordinator: RecordingResult
    Coordinator->>NCS: createNote(from: result)
    NCS-->>Coordinator: Note
    Coordinator-->>UI: Note Created
```

### Background Keyword Detection Flow

```mermaid
sequenceDiagram
    participant KDS as KeywordDetectionService
    participant Coordinator as RecordingCoordinator
    participant VRS as VoiceRecordingService
    participant NCS as NoteCreationService
    participant EventBus
    
    KDS->>KDS: Monitor Audio
    KDS->>EventBus: KeywordDetectedEvent
    EventBus->>Coordinator: Keyword Detected
    Coordinator->>VRS: startRecording(mode: .background)
    VRS-->>Coordinator: RecordingSession
    
    Note over VRS: Auto-stop after duration
    
    VRS->>Coordinator: Recording Stopped
    Coordinator->>NCS: createNote(from: result)
    NCS-->>Coordinator: Note
    Coordinator->>EventBus: NoteCreatedEvent
```

### Siri Shortcuts Flow

```mermaid
sequenceDiagram
    participant Siri
    participant SIM as SiriIntegrationManager
    participant Coordinator as RecordingCoordinator
    participant VRS as VoiceRecordingService
    
    Siri->>SIM: "Start Recording"
    SIM->>Coordinator: handleShortcut(.startRecording)
    Coordinator->>VRS: startRecording(mode: .manual)
    VRS-->>Coordinator: RecordingSession
    Coordinator-->>SIM: Recording Started
    SIM-->>Siri: Success Response
```

## State Management

### Reactive State Flow

```mermaid
graph LR
    A[Service State] --> B[Coordinator State]
    B --> C[UI State]
    C --> D[User Action]
    D --> E[Service Action]
    E --> A
    
    F[EventBus] --> B
    F --> C
    B --> F
```

### State Synchronization

- Services publish state changes via Combine
- RecordingCoordinator aggregates and coordinates state
- UI components subscribe to relevant state publishers
- EventBus provides cross-cutting state notifications

## Testing Strategy

### Test Pyramid

```mermaid
graph TD
    A[UI Tests] --> B[Integration Tests]
    B --> C[Unit Tests]
    
    style A fill:#ff9999
    style B fill:#ffcc99
    style C fill:#99ff99
```

### Test Types

#### Unit Tests
- Service protocol implementations
- Business logic validation
- Error handling scenarios
- State management verification

#### Integration Tests
- Complete recording workflows
- Service interaction testing
- Event system validation
- Error recovery testing

#### UI Tests
- User interaction flows
- Navigation testing
- Accessibility validation
- Performance benchmarking

#### Performance Tests
- Memory usage monitoring
- CPU utilization tracking
- Battery impact assessment
- Network efficiency testing

### Mock Strategy

- Protocol-based mocking for all services
- Configurable mock behaviors
- Deterministic test scenarios
- Performance test mocks with minimal overhead

## Performance Considerations

### Audio Processing
- Efficient audio buffer management
- Background processing for transcription
- Optimized speech recognition usage
- Memory-conscious audio storage

### UI Responsiveness
- Async/await for non-blocking operations
- Combine for reactive UI updates
- Efficient list rendering with lazy loading
- Optimized animation performance

### Memory Management
- Automatic memory optimization
- Efficient audio data handling
- Cache management strategies
- Resource cleanup on app backgrounding

### Battery Optimization
- Efficient background monitoring
- Optimized audio session usage
- Minimal CPU usage during idle
- Smart keyword detection algorithms

## Design Decisions

### Why Protocol-Oriented Design?

**Decision**: Use protocols for all service definitions

**Rationale**:
- Enables dependency injection and testing
- Allows for multiple implementations
- Provides clear contracts between components
- Facilitates future architectural changes

**Trade-offs**:
- Additional abstraction layer
- More initial setup complexity
- Potential for over-abstraction

### Why Combine Over Delegates?

**Decision**: Use Combine for reactive programming

**Rationale**:
- Modern, declarative approach
- Better composability
- Automatic memory management
- Consistent with SwiftUI

**Trade-offs**:
- Learning curve for team members
- iOS 13+ requirement
- Potential for complex publisher chains

### Why Centralized Coordination?

**Decision**: Use RecordingCoordinator as central orchestrator

**Rationale**:
- Single source of truth for recording state
- Simplified service interactions
- Easier testing and debugging
- Clear separation of concerns

**Trade-offs**:
- Potential for coordinator to become too large
- Additional indirection layer
- Risk of tight coupling if not managed properly

### Why EventBus Over NotificationCenter?

**Decision**: Replace NotificationCenter with custom EventBus

**Rationale**:
- Type safety with strongly-typed events
- Better integration with Combine
- More explicit event contracts
- Easier testing and mocking

**Trade-offs**:
- Custom implementation maintenance
- Migration effort from existing code
- Additional learning curve

## Migration from Legacy Architecture

### Migration Strategy

1. **Parallel Implementation**: New architecture implemented alongside legacy
2. **Feature Flags**: Gradual rollout with feature toggles
3. **Compatibility Layer**: Temporary bridge for legacy code
4. **Incremental Migration**: Component-by-component replacement
5. **Validation Testing**: Comprehensive testing at each migration step

### Legacy Component Mapping

| Legacy Component | New Component | Status |
|------------------|---------------|---------|
| AudioManager | VoiceRecordingService + AudioSessionManager | ✅ Completed |
| BackgroundMonitorService | KeywordDetectionService | ✅ Completed |
| NotificationCenter Events | EventBus (Combine) | ✅ Completed |
| Legacy UserPreferences | PreferencesManager | ✅ Completed |
| Legacy KeywordManager | KeywordManager | ✅ Completed |

### Migration Validation

- All legacy functionality preserved
- Performance parity or improvement
- No regression in user experience
- Comprehensive test coverage
- Documentation updated

## Future Considerations

### Scalability
- Modular architecture supports feature additions
- Service-oriented design enables team scaling
- Clear boundaries facilitate parallel development

### Maintainability
- Protocol-oriented design simplifies testing
- Comprehensive documentation aids onboarding
- Consistent patterns reduce cognitive load

### Extensibility
- Plugin architecture for new recording modes
- Configurable service implementations
- Event-driven architecture supports new features

### Technology Evolution
- Modern Swift concurrency adoption
- SwiftUI-first approach
- Combine integration throughout

---

*This documentation is maintained alongside the codebase and should be updated with any architectural changes.*
