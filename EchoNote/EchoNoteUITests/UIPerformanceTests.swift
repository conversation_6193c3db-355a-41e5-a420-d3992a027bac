//
//  UIPerformanceTests.swift
//  EchoNoteUITests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest

// MARK: - UI Performance Tests

final class UIPerformanceTests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        app = XCUIApplication()
        
        // Set launch arguments for performance testing
        app.launchArguments = [
            "-performanceTestMode",
            "-disableAnimations", // Disable animations for consistent timing
            "-mockServices" // Use mock services for predictable performance
        ]
    }
    
    override func tearDownWithError() throws {
        app = nil
    }
    
    // MARK: - App Launch Performance Tests
    
    func testAppLaunchPerformance() throws {
        measure(metrics: [XCTApplicationLaunchMetric()]) {
            app.launch()
        }
    }
    
    func testAppLaunchMemoryUsage() throws {
        measure(metrics: [XCTMemoryMetric()]) {
            app.launch()
            
            // Wait for app to fully load
            let homeTab = app.tabBars.buttons["Home"]
            XCTAssertTrue(homeTab.waitForExistence(timeout: 5))
        }
    }
    
    func testColdStartPerformance() throws {
        // Terminate app if running
        app.terminate()
        
        measure(metrics: [XCTApplicationLaunchMetric()]) {
            app.launch()
            
            // Wait for main interface to appear
            let homeTab = app.tabBars.buttons["Home"]
            XCTAssertTrue(homeTab.waitForExistence(timeout: 10))
        }
    }
    
    // MARK: - Navigation Performance Tests
    
    func testTabSwitchingPerformance() throws {
        app.launch()
        
        let homeTab = app.tabBars.buttons["Home"]
        let notesTab = app.tabBars.buttons["Notes"]
        let profileTab = app.tabBars.buttons["Profile"]
        
        // Wait for initial load
        XCTAssertTrue(homeTab.waitForExistence(timeout: 5))
        
        measure {
            // Switch between tabs multiple times
            for _ in 0..<10 {
                notesTab.tap()
                profileTab.tap()
                homeTab.tap()
            }
        }
    }
    
    func testNotesListScrollingPerformance() throws {
        app.launch()
        
        // Navigate to notes tab
        let notesTab = app.tabBars.buttons["Notes"]
        notesTab.tap()
        
        // Wait for notes list to load
        let notesList = app.collectionViews.firstMatch
        XCTAssertTrue(notesList.waitForExistence(timeout: 5))
        
        measure(metrics: [XCTCPUMetric()]) {
            // Perform scrolling
            for _ in 0..<20 {
                notesList.swipeUp()
                notesList.swipeDown()
            }
        }
    }
    
    // MARK: - Recording Performance Tests
    
    func testRecordingStartLatency() throws {
        app.launch()
        
        // Navigate to home tab
        let homeTab = app.tabBars.buttons["Home"]
        homeTab.tap()
        
        // Find recording button
        let recordButton = app.buttons["recordButton"]
        XCTAssertTrue(recordButton.waitForExistence(timeout: 5))
        
        measure {
            // Start recording
            recordButton.tap()
            
            // Wait for recording to start (indicated by recording UI)
            let recordingIndicator = app.staticTexts["Recording"]
            XCTAssertTrue(recordingIndicator.waitForExistence(timeout: 2))
            
            // Stop recording
            let stopButton = app.buttons["stopButton"]
            if stopButton.exists {
                stopButton.tap()
            }
            
            // Wait for recording to stop
            let _ = recordingIndicator.waitForNonExistence(timeout: 2)
        }
    }
    
    func testRecordingUIUpdatePerformance() throws {
        app.launch()
        
        // Navigate to home tab and start recording
        let homeTab = app.tabBars.buttons["Home"]
        homeTab.tap()
        
        let recordButton = app.buttons["recordButton"]
        XCTAssertTrue(recordButton.waitForExistence(timeout: 5))
        recordButton.tap()
        
        // Wait for recording to start
        let recordingIndicator = app.staticTexts["Recording"]
        XCTAssertTrue(recordingIndicator.waitForExistence(timeout: 2))
        
        measure(metrics: [XCTCPUMetric()]) {
            // Let recording run for a few seconds to measure UI update performance
            Thread.sleep(forTimeInterval: 3.0)
        }
        
        // Stop recording
        let stopButton = app.buttons["stopButton"]
        if stopButton.exists {
            stopButton.tap()
        }
    }
    
    // MARK: - Note Creation Performance Tests
    
    func testNoteCreationFlowPerformance() throws {
        app.launch()
        
        // Navigate to home tab
        let homeTab = app.tabBars.buttons["Home"]
        homeTab.tap()
        
        measure {
            // Start recording
            let recordButton = app.buttons["recordButton"]
            recordButton.tap()
            
            // Wait for recording to start
            let recordingIndicator = app.staticTexts["Recording"]
            XCTAssertTrue(recordingIndicator.waitForExistence(timeout: 2))
            
            // Stop recording
            let stopButton = app.buttons["stopButton"]
            if stopButton.exists {
                stopButton.tap()
            }
            
            // Wait for note creation flow to complete
            let saveButton = app.buttons["Save"]
            if saveButton.waitForExistence(timeout: 5) {
                saveButton.tap()
            }
            
            // Wait for return to home screen
            XCTAssertTrue(recordButton.waitForExistence(timeout: 5))
        }
    }
    
    // MARK: - Search Performance Tests
    
    func testNotesSearchPerformance() throws {
        app.launch()
        
        // Navigate to notes tab
        let notesTab = app.tabBars.buttons["Notes"]
        notesTab.tap()
        
        // Find search field
        let searchField = app.searchFields.firstMatch
        XCTAssertTrue(searchField.waitForExistence(timeout: 5))
        
        measure {
            // Perform search
            searchField.tap()
            searchField.typeText("test")
            
            // Wait for search results
            Thread.sleep(forTimeInterval: 1.0)
            
            // Clear search
            searchField.clearText()
        }
    }
    
    // MARK: - Settings Performance Tests
    
    func testSettingsNavigationPerformance() throws {
        app.launch()
        
        // Navigate to profile tab
        let profileTab = app.tabBars.buttons["Profile"]
        profileTab.tap()
        
        measure {
            // Navigate through various settings
            let settingsButtons = app.buttons.allElementsBoundByIndex
            
            for button in settingsButtons.prefix(5) {
                if button.isHittable {
                    button.tap()
                    
                    // Navigate back if we're in a detail view
                    let backButton = app.navigationBars.buttons.firstMatch
                    if backButton.exists && backButton.label.contains("Back") {
                        backButton.tap()
                    }
                }
            }
        }
    }
    
    // MARK: - Memory Pressure Tests
    
    func testMemoryUsageDuringExtendedUse() throws {
        app.launch()
        
        measure(metrics: [XCTMemoryMetric()]) {
            // Simulate extended app usage
            for _ in 0..<50 {
                // Switch tabs
                app.tabBars.buttons["Notes"].tap()
                app.tabBars.buttons["Profile"].tap()
                app.tabBars.buttons["Home"].tap()
                
                // Perform recording if possible
                let recordButton = app.buttons["recordButton"]
                if recordButton.exists {
                    recordButton.tap()
                    Thread.sleep(forTimeInterval: 0.5)
                    
                    let stopButton = app.buttons["stopButton"]
                    if stopButton.exists {
                        stopButton.tap()
                    }
                }
                
                Thread.sleep(forTimeInterval: 0.1)
            }
        }
    }
    
    // MARK: - Animation Performance Tests
    
    func testAnimationPerformance() throws {
        // Re-enable animations for this test
        app.launchArguments = ["-performanceTestMode", "-mockServices"]
        app.launch()
        
        measure(metrics: [XCTCPUMetric()]) {
            // Trigger various animations
            for _ in 0..<20 {
                // Tab switching animations
                app.tabBars.buttons["Notes"].tap()
                Thread.sleep(forTimeInterval: 0.3) // Wait for animation
                
                app.tabBars.buttons["Home"].tap()
                Thread.sleep(forTimeInterval: 0.3) // Wait for animation
                
                // Recording button animation
                let recordButton = app.buttons["recordButton"]
                if recordButton.exists {
                    recordButton.tap()
                    Thread.sleep(forTimeInterval: 0.2)
                    
                    let stopButton = app.buttons["stopButton"]
                    if stopButton.exists {
                        stopButton.tap()
                        Thread.sleep(forTimeInterval: 0.2)
                    }
                }
            }
        }
    }
    
    // MARK: - Stress Tests
    
    func testRapidUserInteractionStress() throws {
        app.launch()
        
        measure(metrics: [XCTCPUMetric(), XCTMemoryMetric()]) {
            // Rapid user interactions
            for _ in 0..<100 {
                // Rapid tab switching
                app.tabBars.buttons["Notes"].tap()
                app.tabBars.buttons["Profile"].tap()
                app.tabBars.buttons["Home"].tap()
                
                // Rapid button tapping
                let recordButton = app.buttons["recordButton"]
                if recordButton.exists {
                    recordButton.tap()
                    
                    // Immediately try to stop
                    let stopButton = app.buttons["stopButton"]
                    if stopButton.exists {
                        stopButton.tap()
                    }
                }
            }
        }
    }
    
    // MARK: - Background/Foreground Performance Tests
    
    func testBackgroundForegroundTransition() throws {
        app.launch()
        
        measure {
            for _ in 0..<10 {
                // Send app to background
                XCUIDevice.shared.press(.home)
                Thread.sleep(forTimeInterval: 1.0)
                
                // Bring app back to foreground
                app.activate()
                
                // Wait for app to become active
                let homeTab = app.tabBars.buttons["Home"]
                XCTAssertTrue(homeTab.waitForExistence(timeout: 3))
            }
        }
    }
}

// MARK: - XCUIElement Extensions for Performance Testing

extension XCUIElement {
    func clearText() {
        guard let stringValue = self.value as? String else {
            return
        }
        
        let deleteString = String(repeating: XCUIKeyboardKey.delete.rawValue, count: stringValue.count)
        typeText(deleteString)
    }
}
