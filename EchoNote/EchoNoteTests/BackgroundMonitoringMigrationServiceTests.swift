//
//  BackgroundMonitoringMigrationServiceTests.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest
import Combine
@testable import EchoNote

// MARK: - Mock Dependencies for Migration Service

class MockRecordingCoordinatorForMigration: RecordingCoordinating {
    let recordingState = CurrentValueSubject<RecordingState, Never>(.idle)
    @Published var activeSession: RecordingSession?
    @Published var lastCreatedNote: Note?
    
    var isBackgroundMonitoringEnabled = false
    var monitoringKeywords: [String] = []
    var shouldThrowError = false
    
    func startManualRecording() async throws {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 1)
        }
        recordingState.send(.recording(session: RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: URL(fileURLWithPath: "/tmp/test.m4a"),
            mode: .manual
        )))
    }
    
    func stopManualRecording() async throws -> Note? {
        recordingState.send(.idle)
        return nil
    }
    
    func handleKeywordDetection(keyword: String) async throws {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 2)
        }
        recordingState.send(.recording(session: RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: URL(fileURLWithPath: "/tmp/test.m4a"),
            mode: .background
        )))
    }
    
    func handleSiriShortcut(shortcutType: SiriShortcutType) async throws {}
    
    func cancelCurrentRecording() async {
        recordingState.send(.idle)
    }
    
    func enableBackgroundMonitoring(keywords: [String]) async throws {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 3)
        }
        isBackgroundMonitoringEnabled = true
        monitoringKeywords = keywords
        recordingState.send(.listening(keywords: keywords))
    }
    
    func disableBackgroundMonitoring() async {
        isBackgroundMonitoringEnabled = false
        monitoringKeywords = []
        recordingState.send(.idle)
    }
}

class MockKeywordDetectionServiceForMigration: KeywordDetectionServicing {
    @Published var isMonitoring = false
    @Published var activeKeywords: [String] = []
    
    let detectionState = CurrentValueSubject<DetectionState, Never>(.inactive)
    var detectionStream: AnyPublisher<KeywordDetection, Never> {
        Just(KeywordDetection(keyword: "test", confidence: 0.9, timestamp: Date(), context: "test")).eraseToAnyPublisher()
    }
    
    var shouldThrowError = false
    
    func startMonitoring(for keywords: [String]) async throws {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 4)
        }
        activeKeywords = keywords
        isMonitoring = true
        detectionState.send(.monitoring)
    }
    
    func stopMonitoring() async {
        isMonitoring = false
        activeKeywords = []
        detectionState.send(.inactive)
    }
    
    func updateKeywords(_ keywords: [String]) async {
        activeKeywords = keywords
    }
    
    func setDetectionSensitivity(_ sensitivity: Float) async {}
}

class MockDataManagerForMigration: DataManaging {
    var keywords: [Keyword] = []
    
    func saveNote(_ note: Note) async throws {}
    func fetchNotes() async throws -> [Note] { return [] }
    func fetchNote(withId id: UUID) async throws -> Note? { return nil }
    func updateNote(_ note: Note) async throws {}
    func deleteNote(_ note: Note) async throws {}
    
    func fetchKeywords() async throws -> [Keyword] {
        return keywords
    }
    
    func saveKeywords(_ keywords: [Keyword]) async throws {
        self.keywords = keywords
    }
    
    func save() async throws {}
    func delete<T: PersistentModel>(_ model: T) async throws {}
    func fetch<T: PersistentModel>(_ type: T.Type) async throws -> [T] { return [] }
    func insert<T: PersistentModel>(_ model: T) async {}
}

// MARK: - BackgroundMonitoringMigrationService Tests

@MainActor
final class BackgroundMonitoringMigrationServiceTests: XCTestCase {
    
    var migrationService: BackgroundMonitoringMigrationService!
    var mockRecordingCoordinator: MockRecordingCoordinatorForMigration!
    var mockKeywordService: MockKeywordDetectionServiceForMigration!
    var mockDataManager: MockDataManagerForMigration!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() async throws {
        migrationService = BackgroundMonitoringMigrationService.shared
        mockRecordingCoordinator = MockRecordingCoordinatorForMigration()
        mockKeywordService = MockKeywordDetectionServiceForMigration()
        mockDataManager = MockDataManagerForMigration()
        cancellables = Set<AnyCancellable>()
        
        // Set up mock keywords
        mockDataManager.keywords = [
            Keyword(text: "idea"),
            Keyword(text: "task"),
            Keyword(text: "note")
        ]
        
        // Initialize migration service with mock data manager
        await migrationService.initialize(with: mockDataManager)
        
        // Manually inject mock services (in real implementation, these would come from ServiceManager)
        let mirror = Mirror(reflecting: migrationService)
        if let recordingCoordinatorChild = mirror.children.first(where: { $0.label == "recordingCoordinator" }) {
            // In a real test, we'd use dependency injection
            // For now, we'll test the public interface
        }
    }
    
    override func tearDown() async throws {
        await migrationService.stopMonitoring()
        cancellables.removeAll()
        migrationService = nil
        mockRecordingCoordinator = nil
        mockKeywordService = nil
        mockDataManager = nil
    }
    
    // MARK: - Initial State Tests
    
    func testInitialState() {
        XCTAssertFalse(migrationService.isMonitoring)
        XCTAssertFalse(migrationService.isStarting)
        XCTAssertNil(migrationService.lastError)
        XCTAssertTrue(migrationService.detectedKeywords.isEmpty)
        XCTAssertFalse(migrationService.isRecordingAfterKeyword)
        XCTAssertTrue(migrationService.keywordDetectionEnabled)
    }
    
    // MARK: - Monitoring Tests
    
    func testStartMonitoringWithKeywords() async throws {
        // Given
        let keywords = ["test1", "test2"]
        
        // When
        await migrationService.startMonitoring(keywords: keywords)
        
        // Then
        XCTAssertTrue(migrationService.isMonitoring)
        XCTAssertFalse(migrationService.isStarting)
        XCTAssertEqual(migrationService.detectedKeywords, keywords)
        XCTAssertNil(migrationService.lastError)
    }
    
    func testStartMonitoringWithoutKeywords() async throws {
        // When
        await migrationService.startMonitoring()
        
        // Then
        XCTAssertTrue(migrationService.isMonitoring)
        XCTAssertFalse(migrationService.isStarting)
        // Should use stored keywords from mock data manager
        XCTAssertEqual(migrationService.detectedKeywords, ["idea", "task", "note"])
    }
    
    func testStopMonitoring() async throws {
        // Given
        await migrationService.startMonitoring(keywords: ["test"])
        XCTAssertTrue(migrationService.isMonitoring)
        
        // When
        await migrationService.stopMonitoring()
        
        // Then
        XCTAssertFalse(migrationService.isMonitoring)
        XCTAssertFalse(migrationService.isStarting)
        XCTAssertTrue(migrationService.detectedKeywords.isEmpty)
    }
    
    func testStartMonitoringWhenAlreadyMonitoring() async throws {
        // Given
        await migrationService.startMonitoring(keywords: ["test1"])
        XCTAssertTrue(migrationService.isMonitoring)
        
        // When
        await migrationService.startMonitoring(keywords: ["test2"])
        
        // Then
        // Should not change state when already monitoring
        XCTAssertTrue(migrationService.isMonitoring)
        XCTAssertEqual(migrationService.detectedKeywords, ["test1"])
    }
    
    // MARK: - Keyword Update Tests
    
    func testUpdateKeywords() async throws {
        // Given
        await migrationService.startMonitoring(keywords: ["original"])
        XCTAssertEqual(migrationService.detectedKeywords, ["original"])
        
        // When
        await migrationService.updateKeywords(["updated1", "updated2"])
        
        // Then
        XCTAssertEqual(migrationService.detectedKeywords, ["updated1", "updated2"])
        XCTAssertTrue(migrationService.isMonitoring) // Should restart monitoring
    }
    
    func testUpdateKeywordsWhenNotMonitoring() async throws {
        // Given
        XCTAssertFalse(migrationService.isMonitoring)
        
        // When
        await migrationService.updateKeywords(["test"])
        
        // Then
        XCTAssertEqual(migrationService.detectedKeywords, ["test"])
        XCTAssertFalse(migrationService.isMonitoring) // Should not start monitoring
    }
    
    // MARK: - Legacy Compatibility Tests
    
    func testLegacyStartMonitoring() {
        // Given
        let expectation = XCTestExpectation(description: "Legacy start monitoring completes")
        
        // When
        migrationService.startMonitoring()
        
        // Then
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            XCTAssertTrue(self.migrationService.isMonitoring)
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    // MARK: - Error Handling Tests
    
    func testStartMonitoringWithServiceError() async throws {
        // Given
        mockRecordingCoordinator.shouldThrowError = true
        
        // When
        await migrationService.startMonitoring(keywords: ["test"])
        
        // Then
        XCTAssertFalse(migrationService.isMonitoring)
        XCTAssertFalse(migrationService.isStarting)
        XCTAssertNotNil(migrationService.lastError)
    }
    
    // MARK: - State Binding Tests
    
    func testStateBindingWithRecordingCoordinator() async throws {
        // This test would verify that the migration service properly binds to
        // the RecordingCoordinator's state changes. In a real implementation,
        // we would inject the mock coordinator and test the bindings.
        
        // For now, we test that the service can handle state changes gracefully
        await migrationService.startMonitoring(keywords: ["test"])
        XCTAssertTrue(migrationService.isMonitoring)
        
        await migrationService.stopMonitoring()
        XCTAssertFalse(migrationService.isMonitoring)
    }
    
    // MARK: - Performance Tests
    
    func testMonitoringPerformance() {
        measure {
            Task {
                await migrationService.startMonitoring(keywords: ["performance", "test"])
                await migrationService.stopMonitoring()
            }
        }
    }
}
