//
//  KeywordManagerTests.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest
import Combine
@testable import EchoNote

// MARK: - Mock Dependencies for Keyword Manager

class MockDataManagerForKeywords: DataManaging {
    var keywords: [Keyword] = []
    var shouldThrowError = false
    var fetchKeywordsCallCount = 0
    var saveKeywordsCallCount = 0
    
    func fetchKeywords() async throws -> [Keyword] {
        fetchKeywordsCallCount += 1
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 1)
        }
        return keywords
    }
    
    func saveKeywords(_ keywords: [Keyword]) async throws {
        saveKeywordsCallCount += 1
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 2)
        }
        self.keywords = keywords
    }
    
    // Other DataManaging methods (not used in these tests)
    func saveNote(_ note: Note) async throws {}
    func fetchNotes() async throws -> [Note] { return [] }
    func fetchNote(withId id: UUID) async throws -> Note? { return nil }
    func updateNote(_ note: Note) async throws {}
    func deleteNote(_ note: Note) async throws {}
    func save() async throws {}
    func delete<T: PersistentModel>(_ model: T) async throws {}
    func fetch<T: PersistentModel>(_ type: T.Type) async throws -> [T] { return [] }
    func insert<T: PersistentModel>(_ model: T) async {}
}

class MockKeywordDetectionServiceForKeywords: KeywordDetectionServicing {
    @Published var isMonitoring = false
    @Published var activeKeywords: [String] = []
    
    let detectionState = CurrentValueSubject<DetectionState, Never>(.inactive)
    var detectionStream: AnyPublisher<KeywordDetection, Never> {
        Just(KeywordDetection(keyword: "test", confidence: 0.9, timestamp: Date(), context: "test")).eraseToAnyPublisher()
    }
    
    var updateKeywordsCallCount = 0
    var lastUpdatedKeywords: [String] = []
    
    func startMonitoring(for keywords: [String]) async throws {
        activeKeywords = keywords
        isMonitoring = true
    }
    
    func stopMonitoring() async {
        isMonitoring = false
        activeKeywords = []
    }
    
    func updateKeywords(_ keywords: [String]) async {
        updateKeywordsCallCount += 1
        lastUpdatedKeywords = keywords
        activeKeywords = keywords
    }
    
    func setDetectionSensitivity(_ sensitivity: Float) async {}
}

// MARK: - Keyword Manager Tests

@MainActor
final class KeywordManagerTests: XCTestCase {
    
    var keywordManager: KeywordManager!
    var mockDataManager: MockDataManagerForKeywords!
    var mockKeywordDetectionService: MockKeywordDetectionServiceForKeywords!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() async throws {
        mockDataManager = MockDataManagerForKeywords()
        mockKeywordDetectionService = MockKeywordDetectionServiceForKeywords()
        cancellables = Set<AnyCancellable>()
        
        keywordManager = KeywordManager(
            dataManager: mockDataManager,
            keywordDetectionService: mockKeywordDetectionService
        )
        
        // Wait for initial load to complete
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
    }
    
    override func tearDown() async throws {
        cancellables.removeAll()
        keywordManager = nil
        mockDataManager = nil
        mockKeywordDetectionService = nil
    }
    
    // MARK: - Initialization Tests
    
    func testInitialization_LoadsKeywordsFromDataManager() async throws {
        // Given
        let initialKeywords = [
            Keyword(text: "test1"),
            Keyword(text: "test2"),
            Keyword(text: "test3")
        ]
        mockDataManager.keywords = initialKeywords
        
        // When
        let newKeywordManager = KeywordManager(
            dataManager: mockDataManager,
            keywordDetectionService: mockKeywordDetectionService
        )
        
        // Wait for initialization
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        // Then
        XCTAssertEqual(mockDataManager.fetchKeywordsCallCount, 2) // Once for original, once for new
        XCTAssertEqual(newKeywordManager.keywords.count, 3)
        XCTAssertEqual(newKeywordManager.keywordTexts, ["test1", "test2", "test3"])
    }
    
    // MARK: - Add Keyword Tests
    
    func testAddKeyword_ValidKeyword_SuccessfullyAddsKeyword() async throws {
        // Given
        let keywordText = "newkeyword"
        
        // When
        let addedKeyword = try await keywordManager.addKeyword(keywordText)
        
        // Then
        XCTAssertEqual(addedKeyword.text, keywordText)
        XCTAssertEqual(keywordManager.keywords.count, 1)
        XCTAssertEqual(keywordManager.keywordTexts, [keywordText])
        XCTAssertEqual(mockDataManager.saveKeywordsCallCount, 1)
        XCTAssertEqual(mockKeywordDetectionService.updateKeywordsCallCount, 2) // Initial + add
        XCTAssertEqual(mockKeywordDetectionService.lastUpdatedKeywords, [keywordText])
    }
    
    func testAddKeyword_WithWhitespace_TrimsWhitespace() async throws {
        // Given
        let keywordText = "  trimmed  "
        let expectedText = "trimmed"
        
        // When
        let addedKeyword = try await keywordManager.addKeyword(keywordText)
        
        // Then
        XCTAssertEqual(addedKeyword.text, expectedText)
        XCTAssertEqual(keywordManager.keywords.first?.text, expectedText)
    }
    
    func testAddKeyword_DuplicateKeyword_ThrowsError() async throws {
        // Given
        let keywordText = "duplicate"
        _ = try await keywordManager.addKeyword(keywordText)
        
        // When/Then
        do {
            _ = try await keywordManager.addKeyword(keywordText)
            XCTFail("Expected error not thrown")
        } catch {
            XCTAssertTrue(error is KeywordError)
            if case .validationFailed(let message) = error as? KeywordError {
                XCTAssertTrue(message.contains("already exists"))
            } else {
                XCTFail("Unexpected error type")
            }
        }
    }
    
    // MARK: - Remove Keyword Tests
    
    func testRemoveKeyword_ExistingKeyword_SuccessfullyRemovesKeyword() async throws {
        // Given
        let keyword = try await keywordManager.addKeyword("toremove")
        XCTAssertEqual(keywordManager.keywords.count, 1)
        
        // When
        try await keywordManager.removeKeyword(keyword)
        
        // Then
        XCTAssertEqual(keywordManager.keywords.count, 0)
        XCTAssertTrue(keywordManager.keywordTexts.isEmpty)
        XCTAssertEqual(mockDataManager.saveKeywordsCallCount, 2) // Add + remove
        XCTAssertEqual(mockKeywordDetectionService.lastUpdatedKeywords, [])
    }
    
    func testRemoveKeyword_ByText_SuccessfullyRemovesKeyword() async throws {
        // Given
        let keywordText = "toremove"
        _ = try await keywordManager.addKeyword(keywordText)
        XCTAssertEqual(keywordManager.keywords.count, 1)
        
        // When
        try await keywordManager.removeKeyword(withText: keywordText)
        
        // Then
        XCTAssertEqual(keywordManager.keywords.count, 0)
        XCTAssertTrue(keywordManager.keywordTexts.isEmpty)
    }
    
    func testRemoveKeyword_NonExistentKeyword_ThrowsError() async throws {
        // Given
        let nonExistentKeyword = Keyword(text: "nonexistent")
        
        // When/Then
        do {
            try await keywordManager.removeKeyword(nonExistentKeyword)
            XCTFail("Expected error not thrown")
        } catch {
            XCTAssertTrue(error is KeywordError)
            if case .keywordNotFound = error as? KeywordError {
                // Expected
            } else {
                XCTFail("Unexpected error type")
            }
        }
    }
    
    // MARK: - Update Keyword Tests
    
    func testUpdateKeyword_ValidUpdate_SuccessfullyUpdatesKeyword() async throws {
        // Given
        let originalKeyword = try await keywordManager.addKeyword("original")
        var updatedKeyword = originalKeyword
        updatedKeyword.text = "updated"
        
        // When
        try await keywordManager.updateKeyword(updatedKeyword)
        
        // Then
        XCTAssertEqual(keywordManager.keywords.count, 1)
        XCTAssertEqual(keywordManager.keywords.first?.text, "updated")
        XCTAssertEqual(keywordManager.keywords.first?.id, originalKeyword.id)
    }
    
    func testUpdateKeywords_MultipleKeywords_SuccessfullyUpdatesAll() async throws {
        // Given
        let newKeywords = [
            Keyword(text: "keyword1"),
            Keyword(text: "keyword2"),
            Keyword(text: "keyword3")
        ]
        
        // When
        try await keywordManager.updateKeywords(newKeywords)
        
        // Then
        XCTAssertEqual(keywordManager.keywords.count, 3)
        XCTAssertEqual(keywordManager.keywordTexts, ["keyword1", "keyword2", "keyword3"])
        XCTAssertEqual(mockKeywordDetectionService.lastUpdatedKeywords, ["keyword1", "keyword2", "keyword3"])
    }
    
    // MARK: - Validation Tests
    
    func testValidateKeyword_ValidKeyword_ReturnsValid() {
        // Given
        let validKeyword = "validkeyword"
        
        // When
        let result = keywordManager.validateKeyword(validKeyword)
        
        // Then
        XCTAssertEqual(result, .valid)
        XCTAssertTrue(result.isValid)
        XCTAssertTrue(result.errorMessage.isEmpty)
    }
    
    func testValidateKeyword_EmptyKeyword_ReturnsEmpty() {
        // Given
        let emptyKeyword = ""
        
        // When
        let result = keywordManager.validateKeyword(emptyKeyword)
        
        // Then
        XCTAssertEqual(result, .empty)
        XCTAssertFalse(result.isValid)
        XCTAssertFalse(result.errorMessage.isEmpty)
    }
    
    func testValidateKeyword_TooShort_ReturnsTooShort() {
        // Given
        let shortKeyword = "a"
        
        // When
        let result = keywordManager.validateKeyword(shortKeyword)
        
        // Then
        XCTAssertEqual(result, .tooShort)
        XCTAssertFalse(result.isValid)
    }
    
    func testValidateKeyword_TooLong_ReturnsTooLong() {
        // Given
        let longKeyword = String(repeating: "a", count: 51)
        
        // When
        let result = keywordManager.validateKeyword(longKeyword)
        
        // Then
        XCTAssertEqual(result, .tooLong)
        XCTAssertFalse(result.isValid)
    }
    
    func testValidateKeyword_InvalidCharacters_ReturnsInvalidCharacters() {
        // Given
        let invalidKeyword = "keyword@#$"
        
        // When
        let result = keywordManager.validateKeyword(invalidKeyword)
        
        // Then
        XCTAssertEqual(result, .containsInvalidCharacters)
        XCTAssertFalse(result.isValid)
    }
    
    func testValidateKeyword_AlreadyExists_ReturnsAlreadyExists() async throws {
        // Given
        let existingKeyword = "existing"
        _ = try await keywordManager.addKeyword(existingKeyword)
        
        // When
        let result = keywordManager.validateKeyword(existingKeyword)
        
        // Then
        XCTAssertEqual(result, .alreadyExists)
        XCTAssertFalse(result.isValid)
    }
    
    // MARK: - Search Tests
    
    func testSearchKeywords_MatchingQuery_ReturnsMatchingKeywords() async throws {
        // Given
        _ = try await keywordManager.addKeyword("apple")
        _ = try await keywordManager.addKeyword("application")
        _ = try await keywordManager.addKeyword("banana")
        _ = try await keywordManager.addKeyword("grape")
        
        // When
        let results = keywordManager.searchKeywords("app")
        
        // Then
        XCTAssertEqual(results.count, 2)
        XCTAssertTrue(results.contains { $0.text == "apple" })
        XCTAssertTrue(results.contains { $0.text == "application" })
    }
    
    func testSearchKeywords_NoMatches_ReturnsEmptyArray() async throws {
        // Given
        _ = try await keywordManager.addKeyword("apple")
        _ = try await keywordManager.addKeyword("banana")
        
        // When
        let results = keywordManager.searchKeywords("orange")
        
        // Then
        XCTAssertTrue(results.isEmpty)
    }
    
    // MARK: - Publishers Tests
    
    func testKeywordsPublisher_EmitsUpdates() async throws {
        // Given
        var publishedKeywords: [[Keyword]] = []
        
        keywordManager.keywordsPublisher
            .sink { keywords in
                publishedKeywords.append(keywords)
            }
            .store(in: &cancellables)
        
        // When
        _ = try await keywordManager.addKeyword("test1")
        _ = try await keywordManager.addKeyword("test2")
        
        // Wait for publishers to emit
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        // Then
        XCTAssertGreaterThanOrEqual(publishedKeywords.count, 3) // Initial + 2 additions
        XCTAssertEqual(publishedKeywords.last?.count, 2)
    }
    
    func testKeywordTextsPublisher_EmitsTextUpdates() async throws {
        // Given
        var publishedTexts: [[String]] = []
        
        keywordManager.keywordTextsPublisher
            .sink { texts in
                publishedTexts.append(texts)
            }
            .store(in: &cancellables)
        
        // When
        _ = try await keywordManager.addKeyword("text1")
        _ = try await keywordManager.addKeyword("text2")
        
        // Wait for publishers to emit
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        // Then
        XCTAssertGreaterThanOrEqual(publishedTexts.count, 3) // Initial + 2 additions
        XCTAssertEqual(publishedTexts.last, ["text1", "text2"])
    }
    
    // MARK: - Error Handling Tests
    
    func testAddKeyword_DataManagerError_ThrowsError() async throws {
        // Given
        mockDataManager.shouldThrowError = true
        
        // When/Then
        do {
            _ = try await keywordManager.addKeyword("test")
            XCTFail("Expected error not thrown")
        } catch {
            // Expected error
        }
    }
    
    // MARK: - Performance Tests
    
    func testPerformance_AddMultipleKeywords() {
        measure {
            Task {
                for i in 0..<100 {
                    try? await keywordManager.addKeyword("keyword\(i)")
                }
            }
        }
    }
    
    func testPerformance_SearchKeywords() async throws {
        // Given
        for i in 0..<1000 {
            _ = try await keywordManager.addKeyword("keyword\(i)")
        }
        
        // When
        measure {
            _ = keywordManager.searchKeywords("keyword")
        }
    }
}
