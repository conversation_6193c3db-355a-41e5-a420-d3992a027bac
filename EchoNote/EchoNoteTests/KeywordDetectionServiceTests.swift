//
//  KeywordDetectionServiceTests.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest
import Speech
import AVFoundation
import Combine
@testable import EchoNote

// MARK: - Mock Dependencies

class MockAudioSessionManagerForKeyword: AudioSessionManaging {
    @Published var currentSessionState: AudioSessionState = .inactive
    var sessionStatePublisher: AnyPublisher<AudioSessionState, Never> {
        $currentSessionState.eraseToAnyPublisher()
    }
    
    var shouldThrowError = false
    var configuredMode: RecordingMode?
    
    func configureSession(for mode: RecordingMode) async throws {
        if shouldThrowError {
            throw AudioSessionError.configurationFailed(underlying: NSError(domain: "Test", code: 1))
        }
        configuredMode = mode
        currentSessionState = .active(mode)
    }
    
    func activateSession() async throws {
        if shouldThrowError {
            throw AudioSessionError.activationFailed(underlying: NSError(domain: "Test", code: 2))
        }
    }
    
    func deactivateSession() async throws {
        if shouldThrowError {
            throw AudioSessionError.deactivationFailed(underlying: NSError(domain: "Test", code: 3))
        }
        currentSessionState = .inactive
    }
    
    func handleInterruption(_ type: AVAudioSession.InterruptionType) async {
        currentSessionState = .interrupted
    }
    
    func handleRouteChange(_ reason: AVAudioSession.RouteChangeReason) async {
        // Mock implementation
    }
}

class MockPermissionsServiceForKeyword: PermissionsServiceProtocol {
    var hasMicrophonePermission: Bool = true
    var hasSpeechRecognitionPermission: Bool = true
    var shouldGrantPermissions = true
    
    func requestMicrophonePermission() async -> Bool {
        return shouldGrantPermissions
    }
    
    func requestSpeechRecognitionPermission() async -> Bool {
        return shouldGrantPermissions
    }
    
    func requestAllPermissions() async -> Bool {
        return shouldGrantPermissions && hasMicrophonePermission && hasSpeechRecognitionPermission
    }
}

// MARK: - KeywordDetectionService Tests

@MainActor
final class KeywordDetectionServiceTests: XCTestCase {
    
    var keywordDetectionService: KeywordDetectionService!
    var mockAudioSessionManager: MockAudioSessionManagerForKeyword!
    var mockPermissionsService: MockPermissionsServiceForKeyword!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() async throws {
        mockAudioSessionManager = MockAudioSessionManagerForKeyword()
        mockPermissionsService = MockPermissionsServiceForKeyword()
        cancellables = Set<AnyCancellable>()
        
        keywordDetectionService = KeywordDetectionService(
            audioSessionManager: mockAudioSessionManager,
            permissionsService: mockPermissionsService
        )
    }
    
    override func tearDown() async throws {
        await keywordDetectionService.stopMonitoring()
        cancellables.removeAll()
        keywordDetectionService = nil
        mockAudioSessionManager = nil
        mockPermissionsService = nil
    }
    
    // MARK: - Initial State Tests
    
    func testInitialState() {
        // Then
        XCTAssertFalse(keywordDetectionService.isMonitoring)
        XCTAssertTrue(keywordDetectionService.activeKeywords.isEmpty)
        XCTAssertEqual(keywordDetectionService.detectionState.value, .inactive)
    }
    
    // MARK: - Detection State Tests
    
    func testDetectionStatePublisher() async throws {
        // Given
        var receivedStates: [DetectionState] = []
        let expectation = XCTestExpectation(description: "State changes received")
        expectation.expectedFulfillmentCount = 3
        
        keywordDetectionService.detectionState
            .sink { state in
                receivedStates.append(state)
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // When
        try await keywordDetectionService.startMonitoring(for: ["test"])
        await keywordDetectionService.stopMonitoring()
        
        // Then
        await fulfillment(of: [expectation], timeout: 2.0)
        
        XCTAssertEqual(receivedStates.count, 3)
        XCTAssertEqual(receivedStates[0], .inactive) // Initial state
        XCTAssertEqual(receivedStates[1], .monitoring)
        XCTAssertEqual(receivedStates[2], .inactive)
    }
    
    // MARK: - Start Monitoring Tests
    
    func testStartMonitoringSuccess() async throws {
        // Given
        let keywords = ["idea", "task", "note"]
        
        // When
        try await keywordDetectionService.startMonitoring(for: keywords)
        
        // Then
        XCTAssertTrue(keywordDetectionService.isMonitoring)
        XCTAssertEqual(keywordDetectionService.activeKeywords, keywords)
        XCTAssertEqual(keywordDetectionService.detectionState.value, .monitoring)
        XCTAssertEqual(mockAudioSessionManager.configuredMode, .backgroundMonitoring)
    }
    
    func testStartMonitoringWhenAlreadyMonitoring() async throws {
        // Given
        try await keywordDetectionService.startMonitoring(for: ["test"])
        
        // When/Then
        do {
            try await keywordDetectionService.startMonitoring(for: ["another"])
            XCTFail("Expected error when starting monitoring while already monitoring")
        } catch {
            XCTAssertTrue(error is KeywordDetectionError)
            if case .failed(let stateError) = keywordDetectionService.detectionState.value {
                XCTAssertTrue(stateError is KeywordDetectionError)
            } else {
                XCTFail("Expected failed state")
            }
        }
    }
    
    func testStartMonitoringWithEmptyKeywords() async throws {
        // When/Then
        do {
            try await keywordDetectionService.startMonitoring(for: [])
            XCTFail("Expected error when starting monitoring with empty keywords")
        } catch {
            XCTAssertTrue(error is KeywordDetectionError)
            XCTAssertFalse(keywordDetectionService.isMonitoring)
            
            if case .failed(let stateError) = keywordDetectionService.detectionState.value {
                XCTAssertTrue(stateError is KeywordDetectionError)
            } else {
                XCTFail("Expected failed state")
            }
        }
    }
    
    func testStartMonitoringWithoutPermissions() async throws {
        // Given
        mockPermissionsService.shouldGrantPermissions = false
        
        // When/Then
        do {
            try await keywordDetectionService.startMonitoring(for: ["test"])
            XCTFail("Expected error when permissions denied")
        } catch {
            XCTAssertTrue(error is KeywordDetectionError)
            XCTAssertFalse(keywordDetectionService.isMonitoring)
            
            if case .failed(let stateError) = keywordDetectionService.detectionState.value {
                XCTAssertTrue(stateError is KeywordDetectionError)
            } else {
                XCTFail("Expected failed state")
            }
        }
    }
    
    // MARK: - Stop Monitoring Tests
    
    func testStopMonitoringSuccess() async throws {
        // Given
        try await keywordDetectionService.startMonitoring(for: ["test"])
        XCTAssertTrue(keywordDetectionService.isMonitoring)
        
        // When
        await keywordDetectionService.stopMonitoring()
        
        // Then
        XCTAssertFalse(keywordDetectionService.isMonitoring)
        XCTAssertTrue(keywordDetectionService.activeKeywords.isEmpty)
        XCTAssertEqual(keywordDetectionService.detectionState.value, .inactive)
        XCTAssertEqual(mockAudioSessionManager.currentSessionState, .inactive)
    }
    
    func testStopMonitoringWhenNotMonitoring() async throws {
        // When
        await keywordDetectionService.stopMonitoring()
        
        // Then
        XCTAssertFalse(keywordDetectionService.isMonitoring)
        XCTAssertEqual(keywordDetectionService.detectionState.value, .inactive)
    }
    
    // MARK: - Update Keywords Tests
    
    func testUpdateKeywordsWhenNotMonitoring() async throws {
        // Given
        let newKeywords = ["updated", "keywords"]
        
        // When
        await keywordDetectionService.updateKeywords(newKeywords)
        
        // Then
        XCTAssertEqual(keywordDetectionService.activeKeywords, newKeywords)
        XCTAssertFalse(keywordDetectionService.isMonitoring)
    }
    
    func testUpdateKeywordsWhenMonitoring() async throws {
        // Given
        try await keywordDetectionService.startMonitoring(for: ["original"])
        let newKeywords = ["updated", "keywords"]
        
        // When
        await keywordDetectionService.updateKeywords(newKeywords)
        
        // Then
        XCTAssertEqual(keywordDetectionService.activeKeywords, newKeywords)
        // Should restart monitoring with new keywords
        XCTAssertTrue(keywordDetectionService.isMonitoring)
    }
    
    // MARK: - Detection Sensitivity Tests
    
    func testSetDetectionSensitivity() async throws {
        // When
        await keywordDetectionService.setDetectionSensitivity(0.5)
        
        // Then
        // We can't directly test the private property, but we can verify the method doesn't crash
        // and that the service still functions
        XCTAssertFalse(keywordDetectionService.isMonitoring)
    }
    
    func testSetDetectionSensitivityBounds() async throws {
        // Test lower bound
        await keywordDetectionService.setDetectionSensitivity(-0.5)
        
        // Test upper bound
        await keywordDetectionService.setDetectionSensitivity(1.5)
        
        // Should clamp values between 0.0 and 1.0
        // We can't directly test the private property, but the method should handle bounds correctly
    }
    
    // MARK: - Performance Tests
    
    func testMonitoringPerformance() {
        measure {
            Task {
                do {
                    try await keywordDetectionService.startMonitoring(for: ["test"])
                    await keywordDetectionService.stopMonitoring()
                } catch {
                    // Performance test - errors are acceptable
                }
            }
        }
    }
    
    // MARK: - Thread Safety Tests
    
    func testConcurrentMonitoringOperations() async {
        let expectation = XCTestExpectation(description: "Concurrent operations complete")
        expectation.expectedFulfillmentCount = 5
        
        await withTaskGroup(of: Void.self) { group in
            for i in 0..<5 {
                group.addTask {
                    do {
                        try await self.keywordDetectionService.startMonitoring(for: ["test\(i)"])
                        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
                        await self.keywordDetectionService.stopMonitoring()
                    } catch {
                        // Expected - only one monitoring should succeed at a time
                    }
                    expectation.fulfill()
                }
            }
        }
        
        await fulfillment(of: [expectation], timeout: 5.0)
        
        // Should end up in inactive state
        XCTAssertEqual(keywordDetectionService.detectionState.value, .inactive)
        XCTAssertFalse(keywordDetectionService.isMonitoring)
    }
}
