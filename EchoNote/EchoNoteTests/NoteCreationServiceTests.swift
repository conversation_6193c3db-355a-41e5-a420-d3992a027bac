//
//  NoteCreationServiceTests.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest
import SwiftData
@testable import EchoNote

// MARK: - Mock Dependencies

class MockDataManager: DataManaging {
    var savedModels: [any PersistentModel] = []
    var deletedModels: [any PersistentModel] = []
    var insertedModels: [any PersistentModel] = []
    var shouldThrowError = false
    
    func save() async throws {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Save failed"])
        }
        savedModels.append(contentsOf: insertedModels)
    }
    
    func delete<T: PersistentModel>(_ model: T) async throws {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 2, userInfo: [NSLocalizedDescriptionKey: "Delete failed"])
        }
        deletedModels.append(model)
    }
    
    func fetch<T: PersistentModel>(_ type: T.Type) async throws -> [T] {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 3, userInfo: [NSLocalizedDescriptionKey: "Fetch failed"])
        }
        return savedModels.compactMap { $0 as? T }
    }
    
    func insert<T: PersistentModel>(_ model: T) async {
        insertedModels.append(model)
    }
}

class MockKeywordManager: KeywordManagerProtocol {
    var keywords: [Keyword] = []
    var shouldThrowError = false
    
    func findOrCreateKeyword(text: String) async throws -> Keyword {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 4, userInfo: [NSLocalizedDescriptionKey: "Keyword creation failed"])
        }
        
        if let existing = keywords.first(where: { $0.text == text }) {
            return existing
        }
        
        let keyword = Keyword(text: text)
        keywords.append(keyword)
        return keyword
    }
    
    func getAllKeywords() async -> [Keyword] {
        return keywords
    }
}

class MockUserPreferencesService: UserPreferencesService {
    var preferences: UserPreferences?
    
    func getCurrentPreferences() async -> UserPreferences? {
        return preferences
    }
}

class MockFileManager: FileManager {
    var removedURLs: [URL] = []
    var movedFiles: [(from: URL, to: URL)] = []
    var createdDirectories: [URL] = []
    var shouldThrowError = false
    
    override func removeItem(at URL: URL) throws {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 5, userInfo: [NSLocalizedDescriptionKey: "Remove failed"])
        }
        removedURLs.append(URL)
    }
    
    override func moveItem(at srcURL: URL, to dstURL: URL) throws {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 6, userInfo: [NSLocalizedDescriptionKey: "Move failed"])
        }
        movedFiles.append((from: srcURL, to: dstURL))
    }
    
    override func createDirectory(at url: URL, withIntermediateDirectories createIntermediates: Bool, attributes: [FileAttributeKey : Any]? = nil) throws {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 7, userInfo: [NSLocalizedDescriptionKey: "Directory creation failed"])
        }
        createdDirectories.append(url)
    }
    
    override func urls(for directory: FileManager.SearchPathDirectory, in domainMask: FileManager.SearchPathDomainMask) -> [URL] {
        return [URL(fileURLWithPath: "/tmp/Documents")]
    }
}

// MARK: - NoteCreationService Tests

@MainActor
final class NoteCreationServiceTests: XCTestCase {
    
    var noteCreationService: NoteCreationService!
    var mockDataManager: MockDataManager!
    var mockKeywordManager: MockKeywordManager!
    var mockUserPreferences: MockUserPreferencesService!
    var mockFileManager: MockFileManager!
    
    override func setUp() async throws {
        mockDataManager = MockDataManager()
        mockKeywordManager = MockKeywordManager()
        mockUserPreferences = MockUserPreferencesService()
        mockFileManager = MockFileManager()
        
        noteCreationService = NoteCreationService(
            dataManager: mockDataManager,
            keywordManager: mockKeywordManager,
            userPreferences: mockUserPreferences,
            fileManager: mockFileManager
        )
    }
    
    override func tearDown() async throws {
        noteCreationService = nil
        mockDataManager = nil
        mockKeywordManager = nil
        mockUserPreferences = nil
        mockFileManager = nil
    }
    
    // MARK: - Create Note from Recording Tests
    
    func testCreateNoteFromRecording() async throws {
        // Given
        let session = RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: URL(fileURLWithPath: "/tmp/test.m4a"),
            mode: .manual
        )
        let recording = RecordingResult(
            session: session,
            duration: 30.0,
            transcription: "This is a test note",
            audioURL: session.audioURL,
            audioLevels: []
        )
        let transcription = "This is a test note about ideas and tasks"
        
        // When
        let note = try await noteCreationService.createNote(from: recording, transcription: transcription)
        
        // Then
        XCTAssertEqual(note.content, transcription)
        XCTAssertEqual(note.title, "This is a test note...")
        XCTAssertEqual(note.duration, 30.0)
        XCTAssertNotNil(note.audioURL)
        XCTAssertEqual(mockDataManager.insertedModels.count, 1)
        XCTAssertEqual(mockDataManager.savedModels.count, 1)
        XCTAssertEqual(mockFileManager.movedFiles.count, 1)
    }
    
    func testCreateNoteFromRecordingWithoutAudio() async throws {
        // Given
        mockUserPreferences.preferences = UserPreferences(recordAudioWhenCreatingNote: false)
        
        let session = RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: URL(fileURLWithPath: "/tmp/test.m4a"),
            mode: .manual
        )
        let recording = RecordingResult(
            session: session,
            duration: 30.0,
            transcription: "This is a test note",
            audioURL: session.audioURL,
            audioLevels: []
        )
        let transcription = "This is a test note"
        
        // When
        let note = try await noteCreationService.createNote(from: recording, transcription: transcription)
        
        // Then
        XCTAssertEqual(note.content, transcription)
        XCTAssertNil(note.audioURL)
        XCTAssertEqual(note.duration, 0)
        XCTAssertEqual(mockFileManager.removedURLs.count, 1) // Temp file cleaned up
        XCTAssertEqual(mockFileManager.movedFiles.count, 0) // No file moved
    }
    
    // MARK: - Save Note Tests
    
    func testSaveNote() async throws {
        // Given
        let note = Note(
            title: "Test Note",
            content: "Test content",
            audioURL: nil,
            duration: 0,
            createdAt: Date(),
            modifiedAt: Date(),
            isFavorite: false,
            keywords: []
        )
        
        // When
        try await noteCreationService.saveNote(note)
        
        // Then
        XCTAssertEqual(mockDataManager.insertedModels.count, 1)
        XCTAssertEqual(mockDataManager.savedModels.count, 1)
    }
    
    func testSaveNoteFailure() async throws {
        // Given
        mockDataManager.shouldThrowError = true
        let note = Note(
            title: "Test Note",
            content: "Test content",
            audioURL: nil,
            duration: 0,
            createdAt: Date(),
            modifiedAt: Date(),
            isFavorite: false,
            keywords: []
        )
        
        // When/Then
        do {
            try await noteCreationService.saveNote(note)
            XCTFail("Expected save to throw error")
        } catch {
            XCTAssertTrue(error.localizedDescription.contains("Save failed"))
        }
    }
    
    // MARK: - Delete Note Tests
    
    func testDeleteNote() async throws {
        // Given
        let note = Note(
            title: "Test Note",
            content: "Test content",
            audioURL: "/tmp/audio.m4a",
            duration: 30,
            createdAt: Date(),
            modifiedAt: Date(),
            isFavorite: false,
            keywords: []
        )
        
        // When
        try await noteCreationService.deleteNote(note)
        
        // Then
        XCTAssertEqual(mockDataManager.deletedModels.count, 1)
        XCTAssertEqual(mockFileManager.removedURLs.count, 1)
        XCTAssertEqual(mockFileManager.removedURLs.first?.path, "/tmp/audio.m4a")
    }
    
    func testDeleteNoteWithoutAudio() async throws {
        // Given
        let note = Note(
            title: "Test Note",
            content: "Test content",
            audioURL: nil,
            duration: 0,
            createdAt: Date(),
            modifiedAt: Date(),
            isFavorite: false,
            keywords: []
        )
        
        // When
        try await noteCreationService.deleteNote(note)
        
        // Then
        XCTAssertEqual(mockDataManager.deletedModels.count, 1)
        XCTAssertEqual(mockFileManager.removedURLs.count, 0) // No audio file to remove
    }
    
    // MARK: - Update Note Tests
    
    func testUpdateNote() async throws {
        // Given
        let note = Note(
            title: "Test Note",
            content: "Test content",
            audioURL: nil,
            duration: 0,
            createdAt: Date(),
            modifiedAt: Date(),
            isFavorite: false,
            keywords: []
        )
        let originalModifiedAt = note.modifiedAt
        
        // Wait a bit to ensure timestamp difference
        try await Task.sleep(nanoseconds: 1_000_000) // 1ms
        
        // When
        try await noteCreationService.updateNote(note)
        
        // Then
        XCTAssertGreaterThan(note.modifiedAt, originalModifiedAt)
        XCTAssertEqual(mockDataManager.savedModels.count, 1)
    }
    
    // MARK: - Title Generation Tests
    
    func testTitleGeneration() async throws {
        // Given
        let session = RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: URL(fileURLWithPath: "/tmp/test.m4a"),
            mode: .manual
        )
        let recording = RecordingResult(
            session: session,
            duration: 30.0,
            transcription: "This is a test note",
            audioURL: session.audioURL,
            audioLevels: []
        )
        
        // Test short transcription
        let shortTranscription = "Short note"
        let shortNote = try await noteCreationService.createNote(from: recording, transcription: shortTranscription)
        XCTAssertEqual(shortNote.title, "Short note")
        
        // Test long transcription
        let longTranscription = "This is a very long transcription that should be truncated to a reasonable length for the title"
        let longNote = try await noteCreationService.createNote(from: recording, transcription: longTranscription)
        XCTAssertTrue(longNote.title.hasSuffix("..."))
        XCTAssertLessThanOrEqual(longNote.title.count, 50)
    }
    
    // MARK: - Performance Tests
    
    func testNoteCreationPerformance() {
        measure {
            Task {
                let session = RecordingSession(
                    id: UUID(),
                    startTime: Date(),
                    audioURL: URL(fileURLWithPath: "/tmp/test.m4a"),
                    mode: .manual
                )
                let recording = RecordingResult(
                    session: session,
                    duration: 30.0,
                    transcription: "Performance test note",
                    audioURL: session.audioURL,
                    audioLevels: []
                )
                
                try? await noteCreationService.createNote(from: recording, transcription: "Performance test")
            }
        }
    }
}
