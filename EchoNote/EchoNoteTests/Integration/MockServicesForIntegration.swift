//
//  MockServicesForIntegration.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import Combine
@testable import EchoNote

// MARK: - Mock Voice Recording Service for Integration

class MockVoiceRecordingServiceForIntegration: VoiceRecordingServicing {
    @Published var isRecording = false
    let recordingState = CurrentValueSubject<RecordingState, Never>(.idle)
    @Published var currentSession: RecordingSession?
    @Published var recordingDuration: TimeInterval = 0
    @Published var transcribedText = ""
    @Published var audioLevels: [Float] = []
    
    var recordingProgress: AnyPublisher<RecordingProgress, Never> {
        Just(RecordingProgress(
            duration: recordingDuration,
            transcription: transcribedText,
            audioLevels: audioLevels,
            isActive: isRecording
        )).eraseToAnyPublisher()
    }
    
    // Mock results
    var startRecordingResult: Result<RecordingSession, Error> = .success(RecordingSession(
        id: UUID(),
        startTime: Date(),
        audioURL: URL(fileURLWithPath: "/test.m4a"),
        mode: .manual
    ))
    var stopRecordingResult: Result<RecordingResult, Error> = .success(RecordingResult(
        session: RecordingSession(id: UUID(), startTime: Date(), audioURL: URL(fileURLWithPath: "/test.m4a"), mode: .manual),
        duration: 10.0,
        transcription: "Test",
        audioURL: URL(fileURLWithPath: "/test.m4a")
    ))
    var transcribeAudioResult: Result<String, Error> = .success("Test transcription")
    
    // Call tracking
    var startRecordingCallCount = 0
    var stopRecordingCallCount = 0
    var pauseRecordingCallCount = 0
    var resumeRecordingCallCount = 0
    var cancelRecordingCallCount = 0
    var transcribeAudioCallCount = 0
    
    func startRecording(mode: RecordingSession.RecordingMode) async throws -> RecordingSession {
        startRecordingCallCount += 1
        
        switch startRecordingResult {
        case .success(let session):
            currentSession = session
            isRecording = true
            recordingState.send(.recording(session: session))
            return session
        case .failure(let error):
            recordingState.send(.error(error))
            throw error
        }
    }
    
    func stopRecording() async throws -> RecordingResult {
        stopRecordingCallCount += 1
        
        switch stopRecordingResult {
        case .success(let result):
            currentSession = nil
            isRecording = false
            recordingState.send(.processing)
            
            // Simulate processing delay
            try await Task.sleep(nanoseconds: 50_000_000) // 0.05 seconds
            
            recordingState.send(.idle)
            return result
        case .failure(let error):
            recordingState.send(.error(error))
            throw error
        }
    }
    
    func pauseRecording() async throws {
        pauseRecordingCallCount += 1
        // Mock implementation
    }
    
    func resumeRecording() async throws {
        resumeRecordingCallCount += 1
        // Mock implementation
    }
    
    func cancelRecording() async {
        cancelRecordingCallCount += 1
        currentSession = nil
        isRecording = false
        recordingState.send(.idle)
    }
    
    func transcribeAudio(from url: URL) async throws -> String {
        transcribeAudioCallCount += 1
        
        switch transcribeAudioResult {
        case .success(let transcription):
            return transcription
        case .failure(let error):
            throw error
        }
    }
}

// MARK: - Mock Note Creation Service for Integration

class MockNoteCreationServiceForIntegration: NoteCreationServicing {
    
    // Mock results
    var createNoteResult: Result<Note, Error> = .success(Note(
        title: "Test Note",
        content: "Test content",
        audioURL: URL(fileURLWithPath: "/test.m4a"),
        duration: 10.0,
        keywords: []
    ))
    var createTextNoteResult: Result<Note, Error> = .success(Note(
        title: "Test Text Note",
        content: "Test text content",
        audioURL: nil,
        duration: 0,
        keywords: []
    ))
    
    // Call tracking
    var createNoteCallCount = 0
    var createTextNoteCallCount = 0
    var lastCreatedNote: Note?
    
    func createNote(
        from result: RecordingResult,
        title: String?,
        keywords: [Keyword]
    ) async throws -> Note {
        createNoteCallCount += 1
        
        switch createNoteResult {
        case .success(let note):
            lastCreatedNote = note
            return note
        case .failure(let error):
            throw error
        }
    }
    
    func createTextNote(
        title: String,
        content: String,
        keywords: [Keyword]
    ) async throws -> Note {
        createTextNoteCallCount += 1
        
        switch createTextNoteResult {
        case .success(let note):
            lastCreatedNote = note
            return note
        case .failure(let error):
            throw error
        }
    }
}

// MARK: - Mock Keyword Detection Service for Integration

class MockKeywordDetectionServiceForIntegration: KeywordDetectionServicing {
    @Published var isMonitoring = false
    @Published var activeKeywords: [String] = []
    
    let detectionState = CurrentValueSubject<DetectionState, Never>(.inactive)
    var detectionStream: AnyPublisher<KeywordDetection, Never> {
        detectionSubject.eraseToAnyPublisher()
    }
    
    private let detectionSubject = PassthroughSubject<KeywordDetection, Never>()
    
    // Mock results
    var startMonitoringResult: Result<Void, Error> = .success(())
    var shouldThrowError = false
    
    // Call tracking
    var startMonitoringCallCount = 0
    var stopMonitoringCallCount = 0
    var updateKeywordsCallCount = 0
    var setDetectionSensitivityCallCount = 0
    var lastMonitoredKeywords: [String] = []
    var lastSensitivity: Float = 0.5
    
    func startMonitoring(for keywords: [String]) async throws {
        startMonitoringCallCount += 1
        lastMonitoredKeywords = keywords
        
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 1)
        }
        
        switch startMonitoringResult {
        case .success:
            activeKeywords = keywords
            isMonitoring = true
            detectionState.send(.monitoring)
        case .failure(let error):
            detectionState.send(.failed(error))
            throw error
        }
    }
    
    func stopMonitoring() async {
        stopMonitoringCallCount += 1
        isMonitoring = false
        activeKeywords = []
        detectionState.send(.inactive)
    }
    
    func updateKeywords(_ keywords: [String]) async {
        updateKeywordsCallCount += 1
        activeKeywords = keywords
        lastMonitoredKeywords = keywords
    }
    
    func setDetectionSensitivity(_ sensitivity: Float) async {
        setDetectionSensitivityCallCount += 1
        lastSensitivity = sensitivity
    }
    
    // Helper method to simulate keyword detection
    func simulateKeywordDetection(_ keyword: String, confidence: Float = 0.9) {
        let detection = KeywordDetection(
            keyword: keyword,
            confidence: confidence,
            timestamp: Date(),
            context: "Simulated detection"
        )
        detectionSubject.send(detection)
        detectionState.send(.keywordDetected(keyword))
    }
}

// MARK: - Mock Audio Session Manager for Integration

class MockAudioSessionManagerForIntegration: AudioSessionManaging {
    @Published var currentSessionState: AudioSessionState = .inactive
    
    // Mock results
    var configureSessionResult: Result<Void, Error> = .success(())
    var activateSessionResult: Result<Void, Error> = .success(())
    var deactivateSessionResult: Result<Void, Error> = .success(())
    
    // Call tracking
    var configureSessionCallCount = 0
    var activateSessionCallCount = 0
    var deactivateSessionCallCount = 0
    var lastConfiguredMode: RecordingMode?
    
    func configureSession(for mode: RecordingMode) async throws {
        configureSessionCallCount += 1
        lastConfiguredMode = mode
        
        switch configureSessionResult {
        case .success:
            currentSessionState = .configured(mode)
        case .failure(let error):
            currentSessionState = .error(error)
            throw error
        }
    }
    
    func activateSession() async throws {
        activateSessionCallCount += 1
        
        switch activateSessionResult {
        case .success:
            currentSessionState = .active
        case .failure(let error):
            currentSessionState = .error(error)
            throw error
        }
    }
    
    func deactivateSession() async throws {
        deactivateSessionCallCount += 1
        
        switch deactivateSessionResult {
        case .success:
            currentSessionState = .inactive
        case .failure(let error):
            currentSessionState = .error(error)
            throw error
        }
    }
}

// MARK: - Mock Data Manager for Integration

class MockDataManagerForIntegration: DataManaging {
    
    // Storage
    var notes: [Note] = []
    var keywords: [Keyword] = []
    
    // Call tracking
    var saveNoteCallCount = 0
    var fetchNotesCallCount = 0
    var fetchKeywordsCallCount = 0
    var saveKeywordsCallCount = 0
    
    func saveNote(_ note: Note) async throws {
        saveNoteCallCount += 1
        notes.append(note)
    }
    
    func fetchNotes() async throws -> [Note] {
        fetchNotesCallCount += 1
        return notes
    }
    
    func fetchNote(withId id: UUID) async throws -> Note? {
        return notes.first { $0.id == id }
    }
    
    func updateNote(_ note: Note) async throws {
        if let index = notes.firstIndex(where: { $0.id == note.id }) {
            notes[index] = note
        }
    }
    
    func deleteNote(_ note: Note) async throws {
        notes.removeAll { $0.id == note.id }
    }
    
    func fetchKeywords() async throws -> [Keyword] {
        fetchKeywordsCallCount += 1
        return keywords
    }
    
    func saveKeywords(_ keywords: [Keyword]) async throws {
        saveKeywordsCallCount += 1
        self.keywords = keywords
    }
    
    func save() async throws {
        // Mock implementation
    }
    
    func delete<T: PersistentModel>(_ model: T) async throws {
        // Mock implementation
    }
    
    func fetch<T: PersistentModel>(_ type: T.Type) async throws -> [T] {
        return []
    }
    
    func insert<T: PersistentModel>(_ model: T) async {
        // Mock implementation
    }
}
