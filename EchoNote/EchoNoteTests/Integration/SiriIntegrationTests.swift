//
//  SiriIntegrationTests.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest
import Intents
@testable import EchoNote

// MARK: - Siri Integration Tests

@MainActor
final class SiriIntegrationTests: XCTestCase {
    
    var siriIntegrationManager: SiriIntegrationManager!
    var mockRecordingCoordinator: MockRecordingCoordinatorForSiri!
    var mockIntentManager: MockINInteractionManager!
    
    override func setUp() async throws {
        mockRecordingCoordinator = MockRecordingCoordinatorForSiri()
        mockIntentManager = MockINInteractionManager()
        
        siriIntegrationManager = SiriIntegrationManager(
            recordingCoordinator: mockRecordingCoordinator,
            intentManager: mockIntentManager
        )
    }
    
    override func tearDown() async throws {
        siriIntegrationManager = nil
        mockRecordingCoordinator = nil
        mockIntentManager = nil
    }
    
    // MARK: - Shortcut Registration Tests
    
    func testRegisterShortcuts_SuccessfullyRegistersAllShortcuts() async {
        // When
        await siriIntegrationManager.registerShortcuts()
        
        // Then
        // This test verifies that the registration process completes without errors
        // In a real implementation, we would verify that shortcuts are properly registered
        // with the system, but for testing purposes, we ensure no exceptions are thrown
        XCTAssertTrue(true, "Shortcut registration completed successfully")
    }
    
    // MARK: - Intent Donation Tests
    
    func testDonateRecordingIntent_SuccessfullyDonatesIntent() async {
        // When
        await siriIntegrationManager.donateRecordingIntent()
        
        // Then
        XCTAssertEqual(mockIntentManager.donatedInteractions.count, 1)
        
        let interaction = mockIntentManager.donatedInteractions.first!
        XCTAssertTrue(interaction.intent is StartRecordingIntent)
        XCTAssertNotNil(interaction.identifier)
        XCTAssertTrue(interaction.identifier!.hasPrefix("start-recording-"))
    }
    
    func testDonateQuickNoteIntent_WithContent_SuccessfullyDonatesIntent() async {
        // Given
        let content = "Test note content"
        
        // When
        await siriIntegrationManager.donateQuickNoteIntent(content: content)
        
        // Then
        XCTAssertEqual(mockIntentManager.donatedInteractions.count, 1)
        
        let interaction = mockIntentManager.donatedInteractions.first!
        XCTAssertTrue(interaction.intent is CreateQuickNoteIntent)
        
        let quickNoteIntent = interaction.intent as! CreateQuickNoteIntent
        XCTAssertEqual(quickNoteIntent.content, content)
        XCTAssertNotNil(interaction.identifier)
        XCTAssertTrue(interaction.identifier!.hasPrefix("quick-note-"))
    }
    
    func testDonateQuickNoteIntent_WithoutContent_SuccessfullyDonatesIntent() async {
        // When
        await siriIntegrationManager.donateQuickNoteIntent(content: nil)
        
        // Then
        XCTAssertEqual(mockIntentManager.donatedInteractions.count, 1)
        
        let interaction = mockIntentManager.donatedInteractions.first!
        XCTAssertTrue(interaction.intent is CreateQuickNoteIntent)
        
        let quickNoteIntent = interaction.intent as! CreateQuickNoteIntent
        XCTAssertNil(quickNoteIntent.content)
    }
    
    // MARK: - Shortcut Handling Tests
    
    func testHandleStartRecordingShortcut_SuccessfullyStartsRecording() async throws {
        // When
        try await siriIntegrationManager.handleShortcut(.startRecording)
        
        // Then
        XCTAssertEqual(mockRecordingCoordinator.handledShortcuts.count, 1)
        
        if case .startRecording = mockRecordingCoordinator.handledShortcuts.first {
            // Expected
        } else {
            XCTFail("Expected startRecording shortcut")
        }
    }
    
    func testHandleStopRecordingShortcut_SuccessfullyStopsRecording() async throws {
        // When
        try await siriIntegrationManager.handleShortcut(.stopRecording)
        
        // Then
        XCTAssertEqual(mockRecordingCoordinator.handledShortcuts.count, 1)
        
        if case .stopRecording = mockRecordingCoordinator.handledShortcuts.first {
            // Expected
        } else {
            XCTFail("Expected stopRecording shortcut")
        }
    }
    
    func testHandleCreateQuickNoteShortcut_WithContent_SuccessfullyCreatesNote() async throws {
        // Given
        let content = "Test quick note"
        
        // When
        try await siriIntegrationManager.handleShortcut(.createQuickNote(content: content))
        
        // Then
        XCTAssertEqual(mockRecordingCoordinator.handledShortcuts.count, 1)
        
        if case .createQuickNote(let receivedContent) = mockRecordingCoordinator.handledShortcuts.first {
            XCTAssertEqual(receivedContent, content)
        } else {
            XCTFail("Expected createQuickNote shortcut")
        }
    }
    
    func testHandleShortcut_WhenCoordinatorThrowsError_PropagatesError() async {
        // Given
        mockRecordingCoordinator.shouldThrowError = true
        
        // When/Then
        do {
            try await siriIntegrationManager.handleShortcut(.startRecording)
            XCTFail("Expected error not thrown")
        } catch {
            XCTAssertTrue(error is SiriIntegrationError)
            if case .shortcutHandlingFailed = error as? SiriIntegrationError {
                // Expected
            } else {
                XCTFail("Expected shortcutHandlingFailed error")
            }
        }
    }
    
    // MARK: - User Activity Handling Tests
    
    func testHandleUserActivity_StartRecording_SuccessfullyHandlesActivity() async {
        // Given
        let activity = NSUserActivity(activityType: "com.echonote.startRecording")
        activity.userInfo = ["action": "startRecording"]
        
        // When
        let handled = await siriIntegrationManager.handleUserActivity(activity)
        
        // Then
        XCTAssertTrue(handled)
        XCTAssertEqual(mockRecordingCoordinator.handledShortcuts.count, 1)
        
        if case .startRecording = mockRecordingCoordinator.handledShortcuts.first {
            // Expected
        } else {
            XCTFail("Expected startRecording shortcut")
        }
    }
    
    func testHandleUserActivity_CreateQuickNote_SuccessfullyHandlesActivity() async {
        // Given
        let activity = NSUserActivity(activityType: "com.echonote.createQuickNote")
        activity.userInfo = [
            "action": "createQuickNote",
            "content": "Activity note content"
        ]
        
        // When
        let handled = await siriIntegrationManager.handleUserActivity(activity)
        
        // Then
        XCTAssertTrue(handled)
        XCTAssertEqual(mockRecordingCoordinator.handledShortcuts.count, 1)
        
        if case .createQuickNote(let content) = mockRecordingCoordinator.handledShortcuts.first {
            XCTAssertEqual(content, "Activity note content")
        } else {
            XCTFail("Expected createQuickNote shortcut")
        }
    }
    
    func testHandleUserActivity_UnknownAction_ReturnsFalse() async {
        // Given
        let activity = NSUserActivity(activityType: "com.echonote.unknown")
        activity.userInfo = ["action": "unknown"]
        
        // When
        let handled = await siriIntegrationManager.handleUserActivity(activity)
        
        // Then
        XCTAssertFalse(handled)
        XCTAssertTrue(mockRecordingCoordinator.handledShortcuts.isEmpty)
    }
    
    func testHandleUserActivity_MissingAction_ReturnsFalse() async {
        // Given
        let activity = NSUserActivity(activityType: "com.echonote.test")
        activity.userInfo = [:]
        
        // When
        let handled = await siriIntegrationManager.handleUserActivity(activity)
        
        // Then
        XCTAssertFalse(handled)
        XCTAssertTrue(mockRecordingCoordinator.handledShortcuts.isEmpty)
    }
    
    // MARK: - Intent Response Handling Tests
    
    func testHandleIntentResponse_StartRecordingIntent_SuccessfullyHandlesIntent() async {
        // Given
        let intent = StartRecordingIntent()
        
        // When
        let handled = await siriIntegrationManager.handleIntentResponse(intent)
        
        // Then
        XCTAssertTrue(handled)
        XCTAssertEqual(mockRecordingCoordinator.handledShortcuts.count, 1)
        
        if case .startRecording = mockRecordingCoordinator.handledShortcuts.first {
            // Expected
        } else {
            XCTFail("Expected startRecording shortcut")
        }
    }
    
    func testHandleIntentResponse_CreateQuickNoteIntent_SuccessfullyHandlesIntent() async {
        // Given
        let intent = CreateQuickNoteIntent()
        intent.content = "Intent note content"
        
        // When
        let handled = await siriIntegrationManager.handleIntentResponse(intent)
        
        // Then
        XCTAssertTrue(handled)
        XCTAssertEqual(mockRecordingCoordinator.handledShortcuts.count, 1)
        
        if case .createQuickNote(let content) = mockRecordingCoordinator.handledShortcuts.first {
            XCTAssertEqual(content, "Intent note content")
        } else {
            XCTFail("Expected createQuickNote shortcut")
        }
    }
    
    func testHandleIntentResponse_UnknownIntent_ReturnsFalse() async {
        // Given
        let intent = INIntent() // Generic intent
        
        // When
        let handled = await siriIntegrationManager.handleIntentResponse(intent)
        
        // Then
        XCTAssertFalse(handled)
        XCTAssertTrue(mockRecordingCoordinator.handledShortcuts.isEmpty)
    }
    
    // MARK: - Error Handling Tests
    
    func testSiriIntegrationError_HasCorrectDescriptions() {
        let errors: [SiriIntegrationError] = [
            .coordinatorUnavailable,
            .authorizationDenied,
            .shortcutRegistrationFailed(underlying: NSError(domain: "Test", code: 1)),
            .shortcutHandlingFailed(underlying: NSError(domain: "Test", code: 2)),
            .intentDonationFailed(underlying: NSError(domain: "Test", code: 3))
        ]
        
        for error in errors {
            XCTAssertNotNil(error.errorDescription)
            XCTAssertFalse(error.errorDescription!.isEmpty)
        }
    }
    
    // MARK: - Integration with SiriShortcutsService Tests
    
    func testSiriShortcutsService_UsesIntegrationManager() async {
        // Given
        let siriService = SiriShortcutsService.shared
        let activity = NSUserActivity(activityType: "com.echonote.startRecording")
        activity.userInfo = ["action": "startRecording"]
        
        // When
        let handled = siriService.handleUserActivity(activity)
        
        // Then
        XCTAssertTrue(handled) // Should return true for async handling
    }
    
    // MARK: - Performance Tests
    
    func testPerformance_MultipleShortcutHandling() {
        measure {
            Task {
                for i in 0..<100 {
                    let content = "Performance test \(i)"
                    try? await siriIntegrationManager.handleShortcut(.createQuickNote(content: content))
                }
            }
        }
    }
    
    func testPerformance_IntentDonation() {
        measure {
            Task {
                for _ in 0..<50 {
                    await siriIntegrationManager.donateRecordingIntent()
                    await siriIntegrationManager.donateQuickNoteIntent(content: "Performance test")
                }
            }
        }
    }
}
