//
//  RecordingFlowIntegrationTests.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest
import Combine
@testable import EchoNote

// MARK: - Recording Flow Integration Tests

@MainActor
final class RecordingFlowIntegrationTests: XCTestCase {
    
    var serviceManager: ServiceManager!
    var recordingCoordinator: RecordingCoordinator!
    var mockVoiceRecordingService: MockVoiceRecordingServiceForIntegration!
    var mockNoteCreationService: MockNoteCreationServiceForIntegration!
    var mockKeywordDetectionService: MockKeywordDetectionServiceForIntegration!
    var mockAudioSessionManager: MockAudioSessionManagerForIntegration!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() async throws {
        // Create mock services
        mockVoiceRecordingService = MockVoiceRecordingServiceForIntegration()
        mockNoteCreationService = MockNoteCreationServiceForIntegration()
        mockKeywordDetectionService = MockKeywordDetectionServiceForIntegration()
        mockAudioSessionManager = MockAudioSessionManagerForIntegration()
        
        // Create service manager with mocked services
        serviceManager = ServiceManager()
        
        // Create recording coordinator with mocked services
        recordingCoordinator = RecordingCoordinator(
            voiceRecordingService: mockVoiceRecordingService,
            noteCreationService: mockNoteCreationService,
            keywordDetectionService: mockKeywordDetectionService,
            audioSessionManager: mockAudioSessionManager
        )
        
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() async throws {
        cancellables.removeAll()
        recordingCoordinator = nil
        mockVoiceRecordingService = nil
        mockNoteCreationService = nil
        mockKeywordDetectionService = nil
        mockAudioSessionManager = nil
        serviceManager = nil
    }
    
    // MARK: - Manual Recording Flow Tests
    
    func testManualRecordingFlow_SuccessfullyCreatesNote() async throws {
        // Given
        let expectedSession = RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: URL(fileURLWithPath: "/test/recording.m4a"),
            mode: .manual
        )
        let expectedResult = RecordingResult(
            session: expectedSession,
            duration: 10.0,
            transcription: "Test transcription",
            audioURL: expectedSession.audioURL
        )
        let expectedNote = Note(
            title: "Manual Recording",
            content: "Test transcription",
            audioURL: expectedSession.audioURL,
            duration: 10.0,
            keywords: []
        )
        
        mockVoiceRecordingService.startRecordingResult = .success(expectedSession)
        mockVoiceRecordingService.stopRecordingResult = .success(expectedResult)
        mockNoteCreationService.createNoteResult = .success(expectedNote)
        
        // When
        try await recordingCoordinator.startManualRecording()
        let createdNote = try await recordingCoordinator.stopManualRecording()
        
        // Then
        XCTAssertEqual(mockVoiceRecordingService.startRecordingCallCount, 1)
        XCTAssertEqual(mockVoiceRecordingService.stopRecordingCallCount, 1)
        XCTAssertEqual(mockNoteCreationService.createNoteCallCount, 1)
        XCTAssertNotNil(createdNote)
        XCTAssertEqual(createdNote?.title, expectedNote.title)
        XCTAssertEqual(createdNote?.content, expectedNote.content)
    }
    
    func testManualRecordingFlow_WhenRecordingFails_ThrowsError() async {
        // Given
        let expectedError = RecordingError.permissionDenied
        mockVoiceRecordingService.startRecordingResult = .failure(expectedError)
        
        // When/Then
        do {
            try await recordingCoordinator.startManualRecording()
            XCTFail("Expected error not thrown")
        } catch {
            XCTAssertTrue(error is RecordingError)
            if case .permissionDenied = error as? RecordingError {
                // Expected
            } else {
                XCTFail("Unexpected error type: \(error)")
            }
        }
        
        XCTAssertEqual(mockVoiceRecordingService.startRecordingCallCount, 1)
        XCTAssertEqual(mockVoiceRecordingService.stopRecordingCallCount, 0)
        XCTAssertEqual(mockNoteCreationService.createNoteCallCount, 0)
    }
    
    func testManualRecordingFlow_WhenNoteCreationFails_ThrowsError() async throws {
        // Given
        let expectedSession = RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: URL(fileURLWithPath: "/test/recording.m4a"),
            mode: .manual
        )
        let expectedResult = RecordingResult(
            session: expectedSession,
            duration: 10.0,
            transcription: "Test transcription",
            audioURL: expectedSession.audioURL
        )
        let expectedError = NSError(domain: "NoteCreation", code: 1)
        
        mockVoiceRecordingService.startRecordingResult = .success(expectedSession)
        mockVoiceRecordingService.stopRecordingResult = .success(expectedResult)
        mockNoteCreationService.createNoteResult = .failure(expectedError)
        
        // When
        try await recordingCoordinator.startManualRecording()
        
        // Then
        do {
            _ = try await recordingCoordinator.stopManualRecording()
            XCTFail("Expected error not thrown")
        } catch {
            XCTAssertEqual((error as NSError).domain, expectedError.domain)
            XCTAssertEqual((error as NSError).code, expectedError.code)
        }
        
        XCTAssertEqual(mockVoiceRecordingService.startRecordingCallCount, 1)
        XCTAssertEqual(mockVoiceRecordingService.stopRecordingCallCount, 1)
        XCTAssertEqual(mockNoteCreationService.createNoteCallCount, 1)
    }
    
    // MARK: - Background Recording Flow Tests
    
    func testBackgroundRecordingFlow_SuccessfullyCreatesNote() async throws {
        // Given
        let keyword = "test"
        let expectedSession = RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: URL(fileURLWithPath: "/test/background.m4a"),
            mode: .background
        )
        let expectedResult = RecordingResult(
            session: expectedSession,
            duration: 5.0,
            transcription: "Test background recording",
            audioURL: expectedSession.audioURL
        )
        let expectedNote = Note(
            title: "Background Recording - test",
            content: "Test background recording",
            audioURL: expectedSession.audioURL,
            duration: 5.0,
            keywords: [Keyword(text: keyword)]
        )
        
        mockVoiceRecordingService.startRecordingResult = .success(expectedSession)
        mockVoiceRecordingService.stopRecordingResult = .success(expectedResult)
        mockNoteCreationService.createNoteResult = .success(expectedNote)
        
        // When
        try await recordingCoordinator.handleKeywordDetection(keyword: keyword)
        
        // Wait for background processing
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        // Then
        XCTAssertEqual(mockVoiceRecordingService.startRecordingCallCount, 1)
        XCTAssertEqual(mockVoiceRecordingService.stopRecordingCallCount, 1)
        XCTAssertEqual(mockNoteCreationService.createNoteCallCount, 1)
        XCTAssertNotNil(recordingCoordinator.lastCreatedNote)
        XCTAssertEqual(recordingCoordinator.lastCreatedNote?.title, expectedNote.title)
    }
    
    func testBackgroundMonitoring_EnablesAndDisablesCorrectly() async throws {
        // Given
        let keywords = ["test1", "test2", "test3"]
        
        // When - Enable background monitoring
        try await recordingCoordinator.enableBackgroundMonitoring(keywords: keywords)
        
        // Then
        XCTAssertEqual(mockKeywordDetectionService.startMonitoringCallCount, 1)
        XCTAssertEqual(mockKeywordDetectionService.lastMonitoredKeywords, keywords)
        
        // When - Disable background monitoring
        await recordingCoordinator.disableBackgroundMonitoring()
        
        // Then
        XCTAssertEqual(mockKeywordDetectionService.stopMonitoringCallCount, 1)
    }
    
    // MARK: - Siri Shortcuts Flow Tests
    
    func testSiriShortcutFlow_StartRecording_SuccessfullyStartsRecording() async throws {
        // Given
        let expectedSession = RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: URL(fileURLWithPath: "/test/siri.m4a"),
            mode: .manual
        )
        
        mockVoiceRecordingService.startRecordingResult = .success(expectedSession)
        
        // When
        try await recordingCoordinator.handleSiriShortcut(shortcutType: .startRecording)
        
        // Then
        XCTAssertEqual(mockVoiceRecordingService.startRecordingCallCount, 1)
        
        // Verify recording state
        let currentState = recordingCoordinator.recordingState.value
        if case .recording = currentState {
            // Expected
        } else {
            XCTFail("Expected recording state, got: \(currentState)")
        }
    }
    
    func testSiriShortcutFlow_CreateQuickNote_SuccessfullyCreatesNote() async throws {
        // Given
        let content = "Quick note from Siri"
        let expectedNote = Note(
            title: "Siri Quick Note",
            content: content,
            audioURL: nil,
            duration: 0,
            keywords: []
        )
        
        mockNoteCreationService.createTextNoteResult = .success(expectedNote)
        
        // When
        try await recordingCoordinator.handleSiriShortcut(shortcutType: .createQuickNote(content: content))
        
        // Then
        XCTAssertEqual(mockNoteCreationService.createTextNoteCallCount, 1)
        XCTAssertNotNil(recordingCoordinator.lastCreatedNote)
        XCTAssertEqual(recordingCoordinator.lastCreatedNote?.content, content)
    }
    
    // MARK: - State Management Tests
    
    func testRecordingStateTransitions() async throws {
        // Given
        var stateChanges: [RecordingState] = []
        
        recordingCoordinator.recordingState
            .sink { state in
                stateChanges.append(state)
            }
            .store(in: &cancellables)
        
        let expectedSession = RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: URL(fileURLWithPath: "/test/state.m4a"),
            mode: .manual
        )
        let expectedResult = RecordingResult(
            session: expectedSession,
            duration: 10.0,
            transcription: "State test",
            audioURL: expectedSession.audioURL
        )
        let expectedNote = Note(
            title: "State Test",
            content: "State test",
            audioURL: expectedSession.audioURL,
            duration: 10.0,
            keywords: []
        )
        
        mockVoiceRecordingService.startRecordingResult = .success(expectedSession)
        mockVoiceRecordingService.stopRecordingResult = .success(expectedResult)
        mockNoteCreationService.createNoteResult = .success(expectedNote)
        
        // When
        try await recordingCoordinator.startManualRecording()
        _ = try await recordingCoordinator.stopManualRecording()
        
        // Wait for state changes to propagate
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        // Then
        XCTAssertGreaterThanOrEqual(stateChanges.count, 3) // idle -> recording -> processing -> idle
        
        // Verify state progression
        if case .idle = stateChanges.first {
            // Expected initial state
        } else {
            XCTFail("Expected initial idle state")
        }
        
        let hasRecordingState = stateChanges.contains { state in
            if case .recording = state { return true }
            return false
        }
        XCTAssertTrue(hasRecordingState, "Should have recording state")
        
        let hasProcessingState = stateChanges.contains { state in
            if case .processing = state { return true }
            return false
        }
        XCTAssertTrue(hasProcessingState, "Should have processing state")
    }
    
    // MARK: - Error Recovery Tests

    func testErrorRecovery_AfterRecordingFailure_CanStartNewRecording() async throws {
        // Given
        let failureError = RecordingError.audioSessionConfigurationFailed
        let successSession = RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: URL(fileURLWithPath: "/test/recovery.m4a"),
            mode: .manual
        )

        // First attempt fails
        mockVoiceRecordingService.startRecordingResult = .failure(failureError)

        // When - First attempt
        do {
            try await recordingCoordinator.startManualRecording()
            XCTFail("Expected error not thrown")
        } catch {
            // Expected failure
        }

        // Given - Second attempt succeeds
        mockVoiceRecordingService.startRecordingResult = .success(successSession)

        // When - Second attempt
        try await recordingCoordinator.startManualRecording()

        // Then
        XCTAssertEqual(mockVoiceRecordingService.startRecordingCallCount, 2)

        let currentState = recordingCoordinator.recordingState.value
        if case .recording = currentState {
            // Expected
        } else {
            XCTFail("Expected recording state after recovery")
        }
    }

    // MARK: - Performance Tests

    func testPerformance_MultipleRecordingCycles() {
        measure {
            Task {
                for i in 0..<10 {
                    let session = RecordingSession(
                        id: UUID(),
                        startTime: Date(),
                        audioURL: URL(fileURLWithPath: "/test/perf\(i).m4a"),
                        mode: .manual
                    )
                    let result = RecordingResult(
                        session: session,
                        duration: 1.0,
                        transcription: "Performance test \(i)",
                        audioURL: session.audioURL
                    )
                    let note = Note(
                        title: "Performance Test \(i)",
                        content: "Performance test \(i)",
                        audioURL: session.audioURL,
                        duration: 1.0,
                        keywords: []
                    )

                    mockVoiceRecordingService.startRecordingResult = .success(session)
                    mockVoiceRecordingService.stopRecordingResult = .success(result)
                    mockNoteCreationService.createNoteResult = .success(note)

                    try? await recordingCoordinator.startManualRecording()
                    _ = try? await recordingCoordinator.stopManualRecording()
                }
            }
        }
    }

    func testPerformance_ConcurrentKeywordDetection() {
        measure {
            Task {
                let keywords = (0..<100).map { "keyword\($0)" }
                try? await recordingCoordinator.enableBackgroundMonitoring(keywords: keywords)

                // Simulate multiple keyword detections
                for keyword in keywords.prefix(10) {
                    mockKeywordDetectionService.simulateKeywordDetection(keyword)
                }

                await recordingCoordinator.disableBackgroundMonitoring()
            }
        }
    }
}
