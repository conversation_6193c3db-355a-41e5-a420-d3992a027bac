//
//  PreferencesManagerTests.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest
import Combine
@testable import EchoNote

// MARK: - Mock Dependencies for Preferences Manager

class MockUserDefaults: UserDefaults {
    private var storage: [String: Any] = [:]
    
    override func set(_ value: Any?, forKey defaultName: String) {
        storage[defaultName] = value
    }
    
    override func data(forKey defaultName: String) -> Data? {
        return storage[defaultName] as? Data
    }
    
    override func removeObject(forKey defaultName: String) {
        storage.removeValue(forKey: defaultName)
    }
    
    func clear() {
        storage.removeAll()
    }
}

class MockKeywordDetectionServiceForPreferences: KeywordDetectionServicing {
    @Published var isMonitoring = false
    @Published var activeKeywords: [String] = []
    
    let detectionState = CurrentValueSubject<DetectionState, Never>(.inactive)
    var detectionStream: AnyPublisher<KeywordDetection, Never> {
        Just(KeywordDetection(keyword: "test", confidence: 0.9, timestamp: Date(), context: "test")).eraseToAnyPublisher()
    }
    
    var stopMonitoringCallCount = 0
    
    func startMonitoring(for keywords: [String]) async throws {
        activeKeywords = keywords
        isMonitoring = true
    }
    
    func stopMonitoring() async {
        stopMonitoringCallCount += 1
        isMonitoring = false
        activeKeywords = []
    }
    
    func updateKeywords(_ keywords: [String]) async {
        activeKeywords = keywords
    }
    
    func setDetectionSensitivity(_ sensitivity: Float) async {}
}

// MARK: - Preferences Manager Tests

@MainActor
final class PreferencesManagerTests: XCTestCase {
    
    var preferencesManager: PreferencesManager!
    var mockUserDefaults: MockUserDefaults!
    var mockKeywordDetectionService: MockKeywordDetectionServiceForPreferences!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() async throws {
        mockUserDefaults = MockUserDefaults()
        mockKeywordDetectionService = MockKeywordDetectionServiceForPreferences()
        cancellables = Set<AnyCancellable>()
        
        preferencesManager = PreferencesManager(
            userDefaults: mockUserDefaults,
            keywordDetectionService: mockKeywordDetectionService
        )
    }
    
    override func tearDown() async throws {
        cancellables.removeAll()
        preferencesManager = nil
        mockUserDefaults = nil
        mockKeywordDetectionService = nil
    }
    
    // MARK: - Initialization Tests
    
    func testInitialization_WithNoExistingPreferences_UsesDefaults() {
        // Then
        XCTAssertEqual(preferencesManager.preferences.recordingQuality, .medium)
        XCTAssertTrue(preferencesManager.preferences.backgroundMonitoringEnabled)
        XCTAssertTrue(preferencesManager.preferences.transcriptionEnabled)
        XCTAssertEqual(preferencesManager.preferences.autoStopDuration, .seconds15)
        XCTAssertEqual(preferencesManager.preferences.maxRecordingDuration, .minute10)
        XCTAssertTrue(preferencesManager.preferences.saveAudioEnabled)
        XCTAssertNil(preferencesManager.preferences.darkModeEnabled)
        XCTAssertEqual(preferencesManager.preferences.selectedTheme, "default")
        XCTAssertEqual(preferencesManager.preferences.animationSpeed, 1.0)
        XCTAssertTrue(preferencesManager.preferences.notificationsEnabled)
        XCTAssertTrue(preferencesManager.preferences.hapticFeedbackEnabled)
    }
    
    func testInitialization_WithExistingPreferences_LoadsSavedPreferences() throws {
        // Given
        let savedPreferences = AppUserPreferences(
            recordingQuality: .high,
            backgroundMonitoringEnabled: false,
            transcriptionEnabled: false,
            autoStopDuration: .minute1,
            maxRecordingDuration: .minute30,
            saveAudioEnabled: false,
            darkModeEnabled: true,
            selectedTheme: "custom",
            animationSpeed: 2.0,
            notificationsEnabled: false,
            hapticFeedbackEnabled: false
        )
        
        let data = try JSONEncoder().encode(savedPreferences)
        mockUserDefaults.set(data, forKey: "appUserPreferences")
        
        // When
        let newPreferencesManager = PreferencesManager(
            userDefaults: mockUserDefaults,
            keywordDetectionService: mockKeywordDetectionService
        )
        
        // Then
        XCTAssertEqual(newPreferencesManager.preferences.recordingQuality, .high)
        XCTAssertFalse(newPreferencesManager.preferences.backgroundMonitoringEnabled)
        XCTAssertFalse(newPreferencesManager.preferences.transcriptionEnabled)
        XCTAssertEqual(newPreferencesManager.preferences.autoStopDuration, .minute1)
        XCTAssertEqual(newPreferencesManager.preferences.maxRecordingDuration, .minute30)
        XCTAssertFalse(newPreferencesManager.preferences.saveAudioEnabled)
        XCTAssertEqual(newPreferencesManager.preferences.darkModeEnabled, true)
        XCTAssertEqual(newPreferencesManager.preferences.selectedTheme, "custom")
        XCTAssertEqual(newPreferencesManager.preferences.animationSpeed, 2.0)
        XCTAssertFalse(newPreferencesManager.preferences.notificationsEnabled)
        XCTAssertFalse(newPreferencesManager.preferences.hapticFeedbackEnabled)
    }
    
    // MARK: - Recording Preferences Tests
    
    func testUpdateRecordingQuality_UpdatesPreferenceAndSaves() async {
        // When
        await preferencesManager.updateRecordingQuality(.high)
        
        // Then
        XCTAssertEqual(preferencesManager.preferences.recordingQuality, .high)
        
        // Verify persistence
        let savedData = mockUserDefaults.data(forKey: "appUserPreferences")
        XCTAssertNotNil(savedData)
        
        let decodedPreferences = try? JSONDecoder().decode(AppUserPreferences.self, from: savedData!)
        XCTAssertEqual(decodedPreferences?.recordingQuality, .high)
    }
    
    func testSetBackgroundMonitoring_WhenDisabling_StopsKeywordDetection() async {
        // Given
        await preferencesManager.setBackgroundMonitoring(enabled: true)
        XCTAssertTrue(preferencesManager.preferences.backgroundMonitoringEnabled)
        
        // When
        await preferencesManager.setBackgroundMonitoring(enabled: false)
        
        // Then
        XCTAssertFalse(preferencesManager.preferences.backgroundMonitoringEnabled)
        XCTAssertEqual(mockKeywordDetectionService.stopMonitoringCallCount, 1)
    }
    
    func testSetTranscription_UpdatesPreference() async {
        // When
        await preferencesManager.setTranscription(enabled: false)
        
        // Then
        XCTAssertFalse(preferencesManager.preferences.transcriptionEnabled)
    }
    
    func testSetAutoStopDuration_UpdatesPreference() async {
        // When
        await preferencesManager.setAutoStopDuration(.minute2)
        
        // Then
        XCTAssertEqual(preferencesManager.preferences.autoStopDuration, .minute2)
    }
    
    func testSetMaxRecordingDuration_UpdatesPreference() async {
        // When
        await preferencesManager.setMaxRecordingDuration(.unlimited)
        
        // Then
        XCTAssertEqual(preferencesManager.preferences.maxRecordingDuration, .unlimited)
    }
    
    func testSetSaveAudio_UpdatesPreference() async {
        // When
        await preferencesManager.setSaveAudio(enabled: false)
        
        // Then
        XCTAssertFalse(preferencesManager.preferences.saveAudioEnabled)
    }
    
    // MARK: - UI Preferences Tests
    
    func testSetDarkMode_UpdatesPreference() async {
        // When
        await preferencesManager.setDarkMode(enabled: true)
        
        // Then
        XCTAssertEqual(preferencesManager.preferences.darkModeEnabled, true)
        
        // When setting to system default
        await preferencesManager.setDarkMode(enabled: nil)
        
        // Then
        XCTAssertNil(preferencesManager.preferences.darkModeEnabled)
    }
    
    func testSetTheme_UpdatesPreference() async {
        // When
        await preferencesManager.setTheme("dark")
        
        // Then
        XCTAssertEqual(preferencesManager.preferences.selectedTheme, "dark")
    }
    
    func testSetAnimationSpeed_ClampsValue() async {
        // When setting too low
        await preferencesManager.setAnimationSpeed(0.05)
        
        // Then
        XCTAssertEqual(preferencesManager.preferences.animationSpeed, 0.1)
        
        // When setting too high
        await preferencesManager.setAnimationSpeed(5.0)
        
        // Then
        XCTAssertEqual(preferencesManager.preferences.animationSpeed, 3.0)
        
        // When setting valid value
        await preferencesManager.setAnimationSpeed(1.5)
        
        // Then
        XCTAssertEqual(preferencesManager.preferences.animationSpeed, 1.5)
    }
    
    // MARK: - System Preferences Tests
    
    func testSetNotifications_UpdatesPreference() async {
        // When
        await preferencesManager.setNotifications(enabled: false)
        
        // Then
        XCTAssertFalse(preferencesManager.preferences.notificationsEnabled)
    }
    
    func testSetHapticFeedback_UpdatesPreference() async {
        // When
        await preferencesManager.setHapticFeedback(enabled: false)
        
        // Then
        XCTAssertFalse(preferencesManager.preferences.hapticFeedbackEnabled)
    }
    
    // MARK: - Bulk Operations Tests
    
    func testUpdatePreferences_UpdatesAllPreferences() async {
        // Given
        let newPreferences = AppUserPreferences(
            recordingQuality: .low,
            backgroundMonitoringEnabled: false,
            transcriptionEnabled: false,
            autoStopDuration: .never,
            maxRecordingDuration: .unlimited,
            saveAudioEnabled: false,
            darkModeEnabled: false,
            selectedTheme: "custom",
            animationSpeed: 0.5,
            notificationsEnabled: false,
            hapticFeedbackEnabled: false
        )
        
        // When
        await preferencesManager.updatePreferences(newPreferences)
        
        // Then
        XCTAssertEqual(preferencesManager.preferences, newPreferences)
        XCTAssertEqual(mockKeywordDetectionService.stopMonitoringCallCount, 1)
    }
    
    func testResetToDefaults_ResetsAllPreferences() async {
        // Given
        await preferencesManager.updateRecordingQuality(.low)
        await preferencesManager.setBackgroundMonitoring(enabled: false)
        await preferencesManager.setTheme("custom")
        
        // When
        await preferencesManager.resetToDefaults()
        
        // Then
        XCTAssertEqual(preferencesManager.preferences, .default)
    }
    
    // MARK: - Publisher Tests
    
    func testPreferencesPublisher_EmitsUpdates() async {
        // Given
        var publishedPreferences: [AppUserPreferences] = []
        
        preferencesManager.preferencesPublisher
            .sink { preferences in
                publishedPreferences.append(preferences)
            }
            .store(in: &cancellables)
        
        // When
        await preferencesManager.updateRecordingQuality(.high)
        await preferencesManager.setTheme("dark")
        
        // Wait for publishers to emit
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        // Then
        XCTAssertGreaterThanOrEqual(publishedPreferences.count, 3) // Initial + 2 updates
        XCTAssertEqual(publishedPreferences.last?.recordingQuality, .high)
        XCTAssertEqual(publishedPreferences.last?.selectedTheme, "dark")
    }
    
    // MARK: - Enum Tests
    
    func testRecordingQuality_Properties() {
        XCTAssertEqual(RecordingQuality.low.sampleRate, 8000)
        XCTAssertEqual(RecordingQuality.medium.sampleRate, 22050)
        XCTAssertEqual(RecordingQuality.high.sampleRate, 44100)
        
        XCTAssertEqual(RecordingQuality.low.bitRate, 64000)
        XCTAssertEqual(RecordingQuality.medium.bitRate, 128000)
        XCTAssertEqual(RecordingQuality.high.bitRate, 256000)
    }
    
    func testAutoStopDuration_Properties() {
        XCTAssertEqual(AutoStopDuration.seconds5.timeInterval, 5)
        XCTAssertEqual(AutoStopDuration.minute1.timeInterval, 60)
        XCTAssertNil(AutoStopDuration.never.timeInterval)
    }
    
    func testMaxRecordingDuration_Properties() {
        XCTAssertEqual(MaxRecordingDuration.minute1.timeInterval, 60)
        XCTAssertEqual(MaxRecordingDuration.minute30.timeInterval, 1800)
        XCTAssertNil(MaxRecordingDuration.unlimited.timeInterval)
    }
    
    // MARK: - Extensions Tests
    
    func testAppUserPreferences_Extensions() {
        var preferences = AppUserPreferences.default
        
        // Test dark mode
        XCTAssertFalse(preferences.isDarkModeEnabled) // nil should default to false
        
        preferences.darkModeEnabled = true
        XCTAssertTrue(preferences.isDarkModeEnabled)
        
        // Test animation duration
        preferences.animationSpeed = 2.0
        XCTAssertEqual(preferences.effectiveAnimationDuration, 0.6) // 0.3 * 2.0
        
        // Test should show animations
        preferences.animationSpeed = 0.05
        XCTAssertFalse(preferences.shouldShowAnimations)
        
        preferences.animationSpeed = 1.0
        XCTAssertTrue(preferences.shouldShowAnimations)
    }
    
    // MARK: - Performance Tests
    
    func testPerformance_MultiplePreferenceUpdates() {
        measure {
            Task {
                for i in 0..<100 {
                    await preferencesManager.setAnimationSpeed(Double(i % 3) + 0.5)
                }
            }
        }
    }
    
    func testPerformance_BulkPreferenceUpdates() {
        measure {
            Task {
                for i in 0..<50 {
                    let preferences = AppUserPreferences(
                        recordingQuality: RecordingQuality.allCases[i % 3],
                        backgroundMonitoringEnabled: i % 2 == 0,
                        transcriptionEnabled: i % 3 == 0,
                        autoStopDuration: AutoStopDuration.allCases[i % AutoStopDuration.allCases.count],
                        maxRecordingDuration: MaxRecordingDuration.allCases[i % MaxRecordingDuration.allCases.count],
                        saveAudioEnabled: i % 2 == 0,
                        darkModeEnabled: i % 3 == 0 ? true : nil,
                        selectedTheme: "theme\(i)",
                        animationSpeed: Double(i % 3) + 0.5,
                        notificationsEnabled: i % 2 == 0,
                        hapticFeedbackEnabled: i % 3 == 0
                    )
                    await preferencesManager.updatePreferences(preferences)
                }
            }
        }
    }
}
