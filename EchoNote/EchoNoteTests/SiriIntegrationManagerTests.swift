//
//  SiriIntegrationManagerTests.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest
import Intents
@testable import EchoNote

// MARK: - Mock Dependencies

class MockRecordingCoordinatorForSiri: RecordingCoordinating {
    let recordingState = CurrentValueSubject<RecordingState, Never>(.idle)
    @Published var activeSession: RecordingSession?
    @Published var lastCreatedNote: Note?
    
    var handledShortcuts: [SiriShortcutType] = []
    var shouldThrowError = false
    
    func startManualRecording() async throws {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 1)
        }
    }
    
    func stopManualRecording() async throws -> Note? {
        return nil
    }
    
    func handleKeywordDetection(keyword: String) async throws {}
    
    func handleSiriShortcut(shortcutType: SiriShortcutType) async throws {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 2)
        }
        handledShortcuts.append(shortcutType)
    }
    
    func cancelCurrentRecording() async {}
    
    func enableBackgroundMonitoring(keywords: [String]) async throws {}
    
    func disableBackgroundMonitoring() async {}
}

class MockINInteractionManager: INInteractionManager {
    var donatedInteractions: [INInteraction] = []
    var shouldThrowError = false
    
    override func donate(_ interaction: INInteraction) async throws {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 3)
        }
        donatedInteractions.append(interaction)
    }
}

// MARK: - SiriIntegrationManager Tests

@MainActor
final class SiriIntegrationManagerTests: XCTestCase {
    
    var siriManager: SiriIntegrationManager!
    var mockCoordinator: MockRecordingCoordinatorForSiri!
    var mockIntentManager: MockINInteractionManager!
    
    override func setUp() async throws {
        mockCoordinator = MockRecordingCoordinatorForSiri()
        mockIntentManager = MockINInteractionManager()
        
        siriManager = SiriIntegrationManager(
            recordingCoordinator: mockCoordinator,
            intentManager: mockIntentManager
        )
    }
    
    override func tearDown() async throws {
        siriManager = nil
        mockCoordinator = nil
        mockIntentManager = nil
    }
    
    // MARK: - Shortcut Handling Tests
    
    func testHandleStartRecordingShortcut() async throws {
        // When
        try await siriManager.handleShortcut(.startRecording)
        
        // Then
        XCTAssertEqual(mockCoordinator.handledShortcuts.count, 1)
        if case .startRecording = mockCoordinator.handledShortcuts.first {
            // Expected
        } else {
            XCTFail("Expected startRecording shortcut")
        }
    }
    
    func testHandleStopRecordingShortcut() async throws {
        // When
        try await siriManager.handleShortcut(.stopRecording)
        
        // Then
        XCTAssertEqual(mockCoordinator.handledShortcuts.count, 1)
        if case .stopRecording = mockCoordinator.handledShortcuts.first {
            // Expected
        } else {
            XCTFail("Expected stopRecording shortcut")
        }
    }
    
    func testHandleCreateQuickNoteShortcut() async throws {
        // Given
        let content = "Test note content"
        
        // When
        try await siriManager.handleShortcut(.createQuickNote(content: content))
        
        // Then
        XCTAssertEqual(mockCoordinator.handledShortcuts.count, 1)
        if case .createQuickNote(let receivedContent) = mockCoordinator.handledShortcuts.first {
            XCTAssertEqual(receivedContent, content)
        } else {
            XCTFail("Expected createQuickNote shortcut")
        }
    }
    
    func testHandleShortcutWithoutCoordinator() async throws {
        // Given
        let managerWithoutCoordinator = SiriIntegrationManager(
            recordingCoordinator: nil,
            intentManager: mockIntentManager
        )
        
        // When/Then
        do {
            try await managerWithoutCoordinator.handleShortcut(.startRecording)
            XCTFail("Expected error when coordinator is unavailable")
        } catch {
            XCTAssertTrue(error is SiriIntegrationError)
            if case .coordinatorUnavailable = error as? SiriIntegrationError {
                // Expected
            } else {
                XCTFail("Expected coordinatorUnavailable error")
            }
        }
    }
    
    func testHandleShortcutWithCoordinatorError() async throws {
        // Given
        mockCoordinator.shouldThrowError = true
        
        // When/Then
        do {
            try await siriManager.handleShortcut(.startRecording)
            XCTFail("Expected error when coordinator throws")
        } catch {
            XCTAssertTrue(error is SiriIntegrationError)
            if case .shortcutHandlingFailed = error as? SiriIntegrationError {
                // Expected
            } else {
                XCTFail("Expected shortcutHandlingFailed error")
            }
        }
    }
    
    // MARK: - Intent Donation Tests
    
    func testDonateRecordingIntent() async throws {
        // When
        await siriManager.donateRecordingIntent()
        
        // Then
        XCTAssertEqual(mockIntentManager.donatedInteractions.count, 1)
        let interaction = mockIntentManager.donatedInteractions.first!
        XCTAssertTrue(interaction.intent is StartRecordingIntent)
        XCTAssertNotNil(interaction.identifier)
        XCTAssertTrue(interaction.identifier!.hasPrefix("start-recording-"))
    }
    
    func testDonateQuickNoteIntentWithContent() async throws {
        // Given
        let content = "Test content"
        
        // When
        await siriManager.donateQuickNoteIntent(content: content)
        
        // Then
        XCTAssertEqual(mockIntentManager.donatedInteractions.count, 1)
        let interaction = mockIntentManager.donatedInteractions.first!
        XCTAssertTrue(interaction.intent is CreateQuickNoteIntent)
        
        let quickNoteIntent = interaction.intent as! CreateQuickNoteIntent
        XCTAssertEqual(quickNoteIntent.content, content)
        XCTAssertNotNil(interaction.identifier)
        XCTAssertTrue(interaction.identifier!.hasPrefix("quick-note-"))
    }
    
    func testDonateQuickNoteIntentWithoutContent() async throws {
        // When
        await siriManager.donateQuickNoteIntent(content: nil)
        
        // Then
        XCTAssertEqual(mockIntentManager.donatedInteractions.count, 1)
        let interaction = mockIntentManager.donatedInteractions.first!
        XCTAssertTrue(interaction.intent is CreateQuickNoteIntent)
        
        let quickNoteIntent = interaction.intent as! CreateQuickNoteIntent
        XCTAssertNil(quickNoteIntent.content)
    }
    
    // MARK: - User Activity Handling Tests
    
    func testHandleStartRecordingUserActivity() async throws {
        // Given
        let activity = NSUserActivity(activityType: "com.echonote.startRecording")
        activity.userInfo = ["action": "startRecording"]
        
        // When
        let handled = await siriManager.handleUserActivity(activity)
        
        // Then
        XCTAssertTrue(handled)
        XCTAssertEqual(mockCoordinator.handledShortcuts.count, 1)
        if case .startRecording = mockCoordinator.handledShortcuts.first {
            // Expected
        } else {
            XCTFail("Expected startRecording shortcut")
        }
    }
    
    func testHandleCreateQuickNoteUserActivity() async throws {
        // Given
        let activity = NSUserActivity(activityType: "com.echonote.createQuickNote")
        activity.userInfo = [
            "action": "createQuickNote",
            "content": "Test content"
        ]
        
        // When
        let handled = await siriManager.handleUserActivity(activity)
        
        // Then
        XCTAssertTrue(handled)
        XCTAssertEqual(mockCoordinator.handledShortcuts.count, 1)
        if case .createQuickNote(let content) = mockCoordinator.handledShortcuts.first {
            XCTAssertEqual(content, "Test content")
        } else {
            XCTFail("Expected createQuickNote shortcut")
        }
    }
    
    func testHandleUnknownUserActivity() async throws {
        // Given
        let activity = NSUserActivity(activityType: "com.echonote.unknown")
        activity.userInfo = ["action": "unknown"]
        
        // When
        let handled = await siriManager.handleUserActivity(activity)
        
        // Then
        XCTAssertFalse(handled)
        XCTAssertTrue(mockCoordinator.handledShortcuts.isEmpty)
    }
    
    func testHandleUserActivityWithoutAction() async throws {
        // Given
        let activity = NSUserActivity(activityType: "com.echonote.test")
        activity.userInfo = [:]
        
        // When
        let handled = await siriManager.handleUserActivity(activity)
        
        // Then
        XCTAssertFalse(handled)
        XCTAssertTrue(mockCoordinator.handledShortcuts.isEmpty)
    }
    
    // MARK: - Intent Response Handling Tests
    
    func testHandleStartRecordingIntentResponse() async throws {
        // Given
        let intent = StartRecordingIntent()
        
        // When
        let handled = await siriManager.handleIntentResponse(intent)
        
        // Then
        XCTAssertTrue(handled)
        XCTAssertEqual(mockCoordinator.handledShortcuts.count, 1)
        if case .startRecording = mockCoordinator.handledShortcuts.first {
            // Expected
        } else {
            XCTFail("Expected startRecording shortcut")
        }
    }
    
    func testHandleCreateQuickNoteIntentResponse() async throws {
        // Given
        let intent = CreateQuickNoteIntent()
        intent.content = "Intent content"
        
        // When
        let handled = await siriManager.handleIntentResponse(intent)
        
        // Then
        XCTAssertTrue(handled)
        XCTAssertEqual(mockCoordinator.handledShortcuts.count, 1)
        if case .createQuickNote(let content) = mockCoordinator.handledShortcuts.first {
            XCTAssertEqual(content, "Intent content")
        } else {
            XCTFail("Expected createQuickNote shortcut")
        }
    }
    
    func testHandleUnknownIntentResponse() async throws {
        // Given
        let intent = INIntent() // Generic intent
        
        // When
        let handled = await siriManager.handleIntentResponse(intent)
        
        // Then
        XCTAssertFalse(handled)
        XCTAssertTrue(mockCoordinator.handledShortcuts.isEmpty)
    }
    
    // MARK: - Custom Intent Tests
    
    func testStartRecordingIntentProperties() {
        // Given
        let intent = StartRecordingIntent()
        
        // Then
        XCTAssertEqual(intent.suggestedInvocationPhrase, "Start recording")
        XCTAssertEqual(intent.identifier, "StartRecordingIntent")
    }
    
    func testCreateQuickNoteIntentProperties() {
        // Given
        let intent = CreateQuickNoteIntent()
        intent.content = "Test content"
        
        // Then
        XCTAssertEqual(intent.suggestedInvocationPhrase, "Create a quick note")
        XCTAssertEqual(intent.identifier, "CreateQuickNoteIntent")
        XCTAssertEqual(intent.content, "Test content")
    }
    
    // MARK: - Error Handling Tests
    
    func testSiriIntegrationErrorDescriptions() {
        let errors: [SiriIntegrationError] = [
            .coordinatorUnavailable,
            .authorizationDenied,
            .shortcutRegistrationFailed(underlying: NSError(domain: "Test", code: 1)),
            .shortcutHandlingFailed(underlying: NSError(domain: "Test", code: 2)),
            .intentDonationFailed(underlying: NSError(domain: "Test", code: 3))
        ]
        
        for error in errors {
            XCTAssertNotNil(error.errorDescription)
            XCTAssertFalse(error.errorDescription!.isEmpty)
        }
    }
}
