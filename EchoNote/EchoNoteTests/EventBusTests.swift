//
//  EventBusTests.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest
import Combine
@testable import EchoNote

@MainActor
final class EventBusTests: XCTestCase {
    
    var eventBus: EventBus!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() async throws {
        eventBus = EventBus.shared
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() async throws {
        cancellables.removeAll()
        eventBus = nil
    }
    
    // MARK: - Recording Events Tests
    
    func testRecordingStartedEvent() {
        // Given
        let session = RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: URL(fileURLWithPath: "/test.m4a"),
            mode: .manual
        )
        var receivedEvent: RecordingStartedEvent?
        
        eventBus.recordingStarted
            .sink { event in
                receivedEvent = event
            }
            .store(in: &cancellables)
        
        // When
        eventBus.publishRecordingStarted(session: session)
        
        // Then
        XCTAssertNotNil(receivedEvent)
        XCTAssertEqual(receivedEvent?.session.id, session.id)
        XCTAssertEqual(receivedEvent?.session.mode, session.mode)
    }
    
    func testRecordingStoppedEvent() {
        // Given
        let session = RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: URL(fileURLWithPath: "/test.m4a"),
            mode: .manual
        )
        let result = RecordingResult(
            session: session,
            duration: 10.0,
            transcription: "Test",
            audioURL: session.audioURL
        )
        var receivedEvent: RecordingStoppedEvent?
        
        eventBus.recordingStopped
            .sink { event in
                receivedEvent = event
            }
            .store(in: &cancellables)
        
        // When
        eventBus.publishRecordingStopped(result: result)
        
        // Then
        XCTAssertNotNil(receivedEvent)
        XCTAssertEqual(receivedEvent?.result.session.id, result.session.id)
        XCTAssertEqual(receivedEvent?.result.duration, result.duration)
        XCTAssertEqual(receivedEvent?.result.transcription, result.transcription)
    }
    
    func testRecordingFailedEvent() {
        // Given
        let error = RecordingError.permissionDenied
        let sessionId = UUID()
        var receivedEvent: RecordingFailedEvent?
        
        eventBus.recordingFailed
            .sink { event in
                receivedEvent = event
            }
            .store(in: &cancellables)
        
        // When
        eventBus.publishRecordingFailed(error: error, sessionId: sessionId)
        
        // Then
        XCTAssertNotNil(receivedEvent)
        XCTAssertEqual(receivedEvent?.sessionId, sessionId)
        XCTAssertTrue(receivedEvent?.error is RecordingError)
    }
    
    // MARK: - Note Events Tests
    
    func testNoteCreatedEvent() {
        // Given
        let note = Note(
            title: "Test Note",
            content: "Test content",
            audioURL: URL(fileURLWithPath: "/test.m4a"),
            duration: 10.0,
            keywords: []
        )
        var receivedEvent: NoteCreatedEvent?
        
        eventBus.noteCreated
            .sink { event in
                receivedEvent = event
            }
            .store(in: &cancellables)
        
        // When
        eventBus.publishNoteCreated(note: note)
        
        // Then
        XCTAssertNotNil(receivedEvent)
        XCTAssertEqual(receivedEvent?.note.id, note.id)
        XCTAssertEqual(receivedEvent?.note.title, note.title)
        XCTAssertEqual(receivedEvent?.note.content, note.content)
    }
    
    func testNoteUpdatedEvent() {
        // Given
        let originalNote = Note(
            title: "Original",
            content: "Original content",
            audioURL: nil,
            duration: 0,
            keywords: []
        )
        let updatedNote = Note(
            title: "Updated",
            content: "Updated content",
            audioURL: nil,
            duration: 0,
            keywords: []
        )
        var receivedEvent: NoteUpdatedEvent?
        
        eventBus.noteUpdated
            .sink { event in
                receivedEvent = event
            }
            .store(in: &cancellables)
        
        // When
        eventBus.publish(NoteUpdatedEvent(note: updatedNote, previousVersion: originalNote))
        
        // Then
        XCTAssertNotNil(receivedEvent)
        XCTAssertEqual(receivedEvent?.note.title, "Updated")
        XCTAssertEqual(receivedEvent?.previousVersion?.title, "Original")
    }
    
    // MARK: - Keyword Events Tests
    
    func testKeywordDetectedEvent() {
        // Given
        let keyword = "test"
        let confidence: Float = 0.9
        let context = "Test context"
        var receivedEvent: KeywordDetectedEvent?
        
        eventBus.keywordDetected
            .sink { event in
                receivedEvent = event
            }
            .store(in: &cancellables)
        
        // When
        eventBus.publishKeywordDetected(keyword: keyword, confidence: confidence, context: context)
        
        // Then
        XCTAssertNotNil(receivedEvent)
        XCTAssertEqual(receivedEvent?.keyword, keyword)
        XCTAssertEqual(receivedEvent?.confidence, confidence)
        XCTAssertEqual(receivedEvent?.context, context)
    }
    
    func testKeywordsUpdatedEvent() {
        // Given
        let keywords = [
            Keyword(text: "test1"),
            Keyword(text: "test2"),
            Keyword(text: "test3")
        ]
        var receivedEvent: KeywordsUpdatedEvent?
        
        eventBus.keywordsUpdated
            .sink { event in
                receivedEvent = event
            }
            .store(in: &cancellables)
        
        // When
        eventBus.publish(KeywordsUpdatedEvent(keywords: keywords))
        
        // Then
        XCTAssertNotNil(receivedEvent)
        XCTAssertEqual(receivedEvent?.keywords.count, 3)
        XCTAssertEqual(receivedEvent?.keywords.map { $0.text }, ["test1", "test2", "test3"])
    }
    
    // MARK: - Background Monitoring Events Tests
    
    func testBackgroundMonitoringStartedEvent() {
        // Given
        let keywords = ["keyword1", "keyword2"]
        var receivedEvent: BackgroundMonitoringStartedEvent?
        
        eventBus.backgroundMonitoringStarted
            .sink { event in
                receivedEvent = event
            }
            .store(in: &cancellables)
        
        // When
        eventBus.publish(BackgroundMonitoringStartedEvent(keywords: keywords))
        
        // Then
        XCTAssertNotNil(receivedEvent)
        XCTAssertEqual(receivedEvent?.keywords, keywords)
    }
    
    func testBackgroundMonitoringStoppedEvent() {
        // Given
        var receivedEvent: BackgroundMonitoringStoppedEvent?
        
        eventBus.backgroundMonitoringStopped
            .sink { event in
                receivedEvent = event
            }
            .store(in: &cancellables)
        
        // When
        eventBus.publish(BackgroundMonitoringStoppedEvent())
        
        // Then
        XCTAssertNotNil(receivedEvent)
        XCTAssertNotNil(receivedEvent?.timestamp)
        XCTAssertNotNil(receivedEvent?.eventId)
    }
    
    // MARK: - Error Events Tests
    
    func testErrorOccurredEvent() {
        // Given
        let error = NSError(domain: "Test", code: 1, userInfo: [NSLocalizedDescriptionKey: "Test error"])
        let context = "Test context"
        let severity = ErrorSeverity.critical
        var receivedEvent: ErrorOccurredEvent?
        
        eventBus.errorOccurred
            .sink { event in
                receivedEvent = event
            }
            .store(in: &cancellables)
        
        // When
        eventBus.publishError(error: error, context: context, severity: severity)
        
        // Then
        XCTAssertNotNil(receivedEvent)
        XCTAssertEqual((receivedEvent?.error as NSError?)?.domain, error.domain)
        XCTAssertEqual(receivedEvent?.context, context)
        XCTAssertEqual(receivedEvent?.severity, severity)
    }
    
    // MARK: - Event Filtering Tests
    
    func testEventsOfType() {
        // Given
        var recordingEvents: [RecordingStartedEvent] = []
        
        eventBus.events(ofType: RecordingStartedEvent.self)
            .sink { event in
                recordingEvents.append(event)
            }
            .store(in: &cancellables)
        
        let session1 = RecordingSession(id: UUID(), startTime: Date(), audioURL: URL(fileURLWithPath: "/test1.m4a"), mode: .manual)
        let session2 = RecordingSession(id: UUID(), startTime: Date(), audioURL: URL(fileURLWithPath: "/test2.m4a"), mode: .background)
        
        // When
        eventBus.publishRecordingStarted(session: session1)
        eventBus.publishNoteCreated(note: Note(title: "Test", content: "Test", audioURL: nil, duration: 0, keywords: []))
        eventBus.publishRecordingStarted(session: session2)
        
        // Then
        XCTAssertEqual(recordingEvents.count, 2)
        XCTAssertEqual(recordingEvents[0].session.id, session1.id)
        XCTAssertEqual(recordingEvents[1].session.id, session2.id)
    }
    
    func testRecordingEventsStream() {
        // Given
        var allRecordingEvents: [AppEvent] = []
        
        eventBus.recordingEvents
            .sink { event in
                allRecordingEvents.append(event)
            }
            .store(in: &cancellables)
        
        let session = RecordingSession(id: UUID(), startTime: Date(), audioURL: URL(fileURLWithPath: "/test.m4a"), mode: .manual)
        let result = RecordingResult(session: session, duration: 10.0, transcription: "Test", audioURL: session.audioURL)
        let error = RecordingError.permissionDenied
        
        // When
        eventBus.publishRecordingStarted(session: session)
        eventBus.publishNoteCreated(note: Note(title: "Test", content: "Test", audioURL: nil, duration: 0, keywords: [])) // Should not appear
        eventBus.publishRecordingStopped(result: result)
        eventBus.publishRecordingFailed(error: error, sessionId: session.id)
        
        // Then
        XCTAssertEqual(allRecordingEvents.count, 3)
        XCTAssertTrue(allRecordingEvents[0] is RecordingStartedEvent)
        XCTAssertTrue(allRecordingEvents[1] is RecordingStoppedEvent)
        XCTAssertTrue(allRecordingEvents[2] is RecordingFailedEvent)
    }
    
    func testEventsInTimeRange() {
        // Given
        let startTime = Date()
        var eventsInRange: [AppEvent] = []
        
        // Wait a bit to ensure different timestamps
        try? await Task.sleep(nanoseconds: 10_000_000) // 0.01 seconds
        
        let rangeStart = Date()
        
        eventBus.eventsInTimeRange(from: rangeStart, to: Date().addingTimeInterval(1))
            .sink { event in
                eventsInRange.append(event)
            }
            .store(in: &cancellables)
        
        // When
        let session = RecordingSession(id: UUID(), startTime: Date(), audioURL: URL(fileURLWithPath: "/test.m4a"), mode: .manual)
        eventBus.publishRecordingStarted(session: session) // Should be in range
        
        // Then
        XCTAssertEqual(eventsInRange.count, 1)
        XCTAssertTrue(eventsInRange[0] is RecordingStartedEvent)
    }
    
    // MARK: - All Events Stream Tests
    
    func testAllEventsStream() {
        // Given
        var allEvents: [AppEvent] = []
        
        eventBus.allEvents
            .sink { event in
                allEvents.append(event)
            }
            .store(in: &cancellables)
        
        // When
        let session = RecordingSession(id: UUID(), startTime: Date(), audioURL: URL(fileURLWithPath: "/test.m4a"), mode: .manual)
        let note = Note(title: "Test", content: "Test", audioURL: nil, duration: 0, keywords: [])
        
        eventBus.publishRecordingStarted(session: session)
        eventBus.publishNoteCreated(note: note)
        eventBus.publishKeywordDetected(keyword: "test", confidence: 0.9)
        
        // Then
        XCTAssertEqual(allEvents.count, 3)
        XCTAssertTrue(allEvents[0] is RecordingStartedEvent)
        XCTAssertTrue(allEvents[1] is NoteCreatedEvent)
        XCTAssertTrue(allEvents[2] is KeywordDetectedEvent)
    }
    
    // MARK: - Performance Tests
    
    func testEventPublishingPerformance() {
        measure {
            for i in 0..<1000 {
                let session = RecordingSession(
                    id: UUID(),
                    startTime: Date(),
                    audioURL: URL(fileURLWithPath: "/test\(i).m4a"),
                    mode: .manual
                )
                eventBus.publishRecordingStarted(session: session)
            }
        }
    }
    
    func testEventSubscriptionPerformance() {
        // Given
        var eventCount = 0
        
        measure {
            eventBus.recordingStarted
                .sink { _ in
                    eventCount += 1
                }
                .store(in: &cancellables)
            
            for i in 0..<1000 {
                let session = RecordingSession(
                    id: UUID(),
                    startTime: Date(),
                    audioURL: URL(fileURLWithPath: "/test\(i).m4a"),
                    mode: .manual
                )
                eventBus.publishRecordingStarted(session: session)
            }
        }
        
        XCTAssertEqual(eventCount, 1000)
    }
}
