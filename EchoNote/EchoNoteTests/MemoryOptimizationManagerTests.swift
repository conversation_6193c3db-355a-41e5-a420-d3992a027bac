//
//  MemoryOptimizationManagerTests.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest
import Combine
@testable import EchoNote

@MainActor
final class MemoryOptimizationManagerTests: XCTestCase {
    
    var memoryOptimizationManager: MemoryOptimizationManager!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() async throws {
        memoryOptimizationManager = MemoryOptimizationManager.shared
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() async throws {
        memoryOptimizationManager.stopMemoryMonitoring()
        cancellables.removeAll()
        memoryOptimizationManager = nil
    }
    
    // MARK: - Memory Monitoring Tests
    
    func testStartMemoryMonitoring_UpdatesMemoryUsage() async throws {
        // When
        memoryOptimizationManager.startMemoryMonitoring()
        
        // Wait for initial measurement
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        // Then
        XCTAssertNotNil(memoryOptimizationManager.currentMemoryUsage)
        XCTAssertGreaterThan(memoryOptimizationManager.currentMemoryUsage?.appMemoryUsage ?? 0, 0)
    }
    
    func testStopMemoryMonitoring_StopsUpdates() async throws {
        // Given
        memoryOptimizationManager.startMemoryMonitoring()
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        let initialUsage = memoryOptimizationManager.currentMemoryUsage
        XCTAssertNotNil(initialUsage)
        
        // When
        memoryOptimizationManager.stopMemoryMonitoring()
        
        // Wait to ensure no more updates
        try await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Then
        // Memory usage should not have been updated (timestamp should be the same)
        XCTAssertEqual(
            memoryOptimizationManager.currentMemoryUsage?.timestamp,
            initialUsage?.timestamp
        )
    }
    
    // MARK: - Memory Usage Metrics Tests
    
    func testMemoryUsageMetrics_CalculatesMemoryPressure() {
        // Test low pressure
        let lowPressureMetrics = MemoryUsageMetrics(
            totalMemory: 1000,
            usedMemory: 500, // 50%
            availableMemory: 500,
            appMemoryUsage: 100,
            timestamp: Date()
        )
        XCTAssertEqual(lowPressureMetrics.memoryPressure, .low)
        
        // Test moderate pressure
        let moderatePressureMetrics = MemoryUsageMetrics(
            totalMemory: 1000,
            usedMemory: 700, // 70%
            availableMemory: 300,
            appMemoryUsage: 100,
            timestamp: Date()
        )
        XCTAssertEqual(moderatePressureMetrics.memoryPressure, .moderate)
        
        // Test high pressure
        let highPressureMetrics = MemoryUsageMetrics(
            totalMemory: 1000,
            usedMemory: 850, // 85%
            availableMemory: 150,
            appMemoryUsage: 100,
            timestamp: Date()
        )
        XCTAssertEqual(highPressureMetrics.memoryPressure, .high)
        
        // Test critical pressure
        let criticalPressureMetrics = MemoryUsageMetrics(
            totalMemory: 1000,
            usedMemory: 950, // 95%
            availableMemory: 50,
            appMemoryUsage: 100,
            timestamp: Date()
        )
        XCTAssertEqual(criticalPressureMetrics.memoryPressure, .critical)
    }
    
    func testMemoryUsageMetrics_FormatsAppMemoryUsage() {
        let metrics = MemoryUsageMetrics(
            totalMemory: 1000,
            usedMemory: 500,
            availableMemory: 500,
            appMemoryUsage: 1024 * 1024, // 1 MB
            timestamp: Date()
        )
        
        XCTAssertTrue(metrics.formattedAppMemoryUsage.contains("MB"))
    }
    
    // MARK: - Memory Optimization Tests
    
    func testOptimizeMemoryUsage_SetsOptimizingFlag() async {
        // Given
        XCTAssertFalse(memoryOptimizationManager.isOptimizing)
        
        // When
        let optimizationTask = Task {
            await memoryOptimizationManager.optimizeMemoryUsage()
        }
        
        // Wait a moment to check the flag
        try? await Task.sleep(nanoseconds: 10_000_000) // 0.01 seconds
        
        // Then (during optimization)
        // Note: This might be flaky due to timing, but it tests the concept
        
        // Wait for completion
        await optimizationTask.value
        
        // Then (after optimization)
        XCTAssertFalse(memoryOptimizationManager.isOptimizing)
    }
    
    func testOptimizeMemoryUsage_DoesNotRunConcurrently() async {
        // Given
        let firstOptimization = Task {
            await memoryOptimizationManager.optimizeMemoryUsage()
        }
        
        // When - Start second optimization while first is running
        let secondOptimization = Task {
            await memoryOptimizationManager.optimizeMemoryUsage()
        }
        
        // Then - Both should complete without issues
        await firstOptimization.value
        await secondOptimization.value
        
        XCTAssertFalse(memoryOptimizationManager.isOptimizing)
    }
    
    // MARK: - Optimization Strategy Tests
    
    func testApplyOptimizationStrategy_ClearCaches() async {
        // When
        await memoryOptimizationManager.applyOptimizationStrategy(.clearCaches)
        
        // Then
        // This test mainly ensures the method doesn't crash
        // In a real implementation, we would verify that caches are actually cleared
        XCTAssertTrue(true)
    }
    
    func testApplyOptimizationStrategy_ReleaseUnusedResources() async {
        // When
        await memoryOptimizationManager.applyOptimizationStrategy(.releaseUnusedResources)
        
        // Then
        XCTAssertTrue(true)
    }
    
    func testApplyOptimizationStrategy_CompressAudioData() async {
        // When
        await memoryOptimizationManager.applyOptimizationStrategy(.compressAudioData)
        
        // Then
        XCTAssertTrue(true)
    }
    
    func testApplyOptimizationStrategy_LimitConcurrentOperations() async {
        // When
        await memoryOptimizationManager.applyOptimizationStrategy(.limitConcurrentOperations)
        
        // Then
        XCTAssertTrue(true)
    }
    
    func testApplyOptimizationStrategy_ReduceImageQuality() async {
        // When
        await memoryOptimizationManager.applyOptimizationStrategy(.reduceImageQuality)
        
        // Then
        XCTAssertTrue(true)
    }
    
    func testApplyOptimizationStrategy_PurgeOldRecordings() async {
        // When
        await memoryOptimizationManager.applyOptimizationStrategy(.purgeOldRecordings)
        
        // Then
        XCTAssertTrue(true)
    }
    
    // MARK: - Memory Usage Report Tests
    
    func testGetMemoryUsageReport_WithNoData_ReturnsUnavailableMessage() {
        // Given - No memory monitoring started
        
        // When
        let report = memoryOptimizationManager.getMemoryUsageReport()
        
        // Then
        XCTAssertEqual(report, "Memory usage data not available")
    }
    
    func testGetMemoryUsageReport_WithData_ReturnsFormattedReport() async throws {
        // Given
        memoryOptimizationManager.startMemoryMonitoring()
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        // When
        let report = memoryOptimizationManager.getMemoryUsageReport()
        
        // Then
        XCTAssertTrue(report.contains("Memory Usage Report"))
        XCTAssertTrue(report.contains("Total Memory:"))
        XCTAssertTrue(report.contains("Used Memory:"))
        XCTAssertTrue(report.contains("Available Memory:"))
        XCTAssertTrue(report.contains("App Memory Usage:"))
        XCTAssertTrue(report.contains("Memory Pressure:"))
        XCTAssertTrue(report.contains("Timestamp:"))
    }
    
    // MARK: - Memory Pressure Level Tests
    
    func testMemoryPressureLevel_ReturnsCorrectLevel() async throws {
        // Given
        memoryOptimizationManager.startMemoryMonitoring()
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        // When
        let pressureLevel = memoryOptimizationManager.memoryPressureLevel
        
        // Then
        XCTAssertTrue(MemoryPressureLevel.allCases.contains(pressureLevel))
    }
    
    // MARK: - Memory Pressure Level Enum Tests
    
    func testMemoryPressureLevel_Colors() {
        XCTAssertEqual(MemoryPressureLevel.low.color, .green)
        XCTAssertEqual(MemoryPressureLevel.moderate.color, .yellow)
        XCTAssertEqual(MemoryPressureLevel.high.color, .orange)
        XCTAssertEqual(MemoryPressureLevel.critical.color, .red)
    }
    
    // MARK: - Memory Optimization Strategy Tests
    
    func testMemoryOptimizationStrategy_Descriptions() {
        let strategies = [
            MemoryOptimizationStrategy.clearCaches,
            .releaseUnusedResources,
            .compressAudioData,
            .limitConcurrentOperations,
            .reduceImageQuality,
            .purgeOldRecordings
        ]
        
        for strategy in strategies {
            XCTAssertFalse(strategy.description.isEmpty)
        }
    }
    
    // MARK: - Memory Optimization Event Tests
    
    func testMemoryOptimizationEvent_Initialization() {
        // Given
        let initialUsage: UInt64 = 1000
        let finalUsage: UInt64 = 800
        let memoryFreed: UInt64 = 200
        let strategies: [MemoryOptimizationStrategy] = [.clearCaches, .releaseUnusedResources]
        
        // When
        let event = MemoryOptimizationEvent(
            initialUsage: initialUsage,
            finalUsage: finalUsage,
            memoryFreed: memoryFreed,
            strategies: strategies
        )
        
        // Then
        XCTAssertEqual(event.initialUsage, initialUsage)
        XCTAssertEqual(event.finalUsage, finalUsage)
        XCTAssertEqual(event.memoryFreed, memoryFreed)
        XCTAssertEqual(event.strategies.count, strategies.count)
        XCTAssertNotNil(event.timestamp)
        XCTAssertNotNil(event.eventId)
    }
    
    // MARK: - Publisher Tests
    
    func testMemoryUsagePublisher_EmitsUpdates() async throws {
        // Given
        var receivedUsages: [MemoryUsageMetrics?] = []
        
        memoryOptimizationManager.$currentMemoryUsage
            .sink { usage in
                receivedUsages.append(usage)
            }
            .store(in: &cancellables)
        
        // When
        memoryOptimizationManager.startMemoryMonitoring()
        
        // Wait for updates
        try await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Then
        XCTAssertGreaterThan(receivedUsages.count, 1) // Initial nil + at least one update
        XCTAssertNotNil(receivedUsages.last!)
    }
    
    func testIsOptimizingPublisher_EmitsUpdates() async throws {
        // Given
        var optimizingStates: [Bool] = []
        
        memoryOptimizationManager.$isOptimizing
            .sink { isOptimizing in
                optimizingStates.append(isOptimizing)
            }
            .store(in: &cancellables)
        
        // When
        await memoryOptimizationManager.optimizeMemoryUsage()
        
        // Wait for state changes
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        // Then
        XCTAssertGreaterThan(optimizingStates.count, 1) // Initial false + potential state changes
        XCTAssertFalse(optimizingStates.last!) // Should end with false
    }
    
    // MARK: - Performance Tests
    
    func testPerformance_MemoryUsageCalculation() {
        measure {
            for _ in 0..<100 {
                _ = memoryOptimizationManager.getMemoryUsageReport()
            }
        }
    }
    
    func testPerformance_OptimizationStrategies() {
        measure {
            Task {
                for strategy in [MemoryOptimizationStrategy.clearCaches, .releaseUnusedResources] {
                    await memoryOptimizationManager.applyOptimizationStrategy(strategy)
                }
            }
        }
    }
    
    // MARK: - Integration Tests
    
    func testMemoryMonitoring_Integration() async throws {
        // Given
        var memoryUpdates: [MemoryUsageMetrics] = []
        
        memoryOptimizationManager.$currentMemoryUsage
            .compactMap { $0 }
            .sink { usage in
                memoryUpdates.append(usage)
            }
            .store(in: &cancellables)
        
        // When
        memoryOptimizationManager.startMemoryMonitoring()
        
        // Wait for several updates
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        memoryOptimizationManager.stopMemoryMonitoring()
        
        // Then
        XCTAssertGreaterThan(memoryUpdates.count, 0)
        
        // Verify all updates have valid data
        for update in memoryUpdates {
            XCTAssertGreaterThan(update.totalMemory, 0)
            XCTAssertGreaterThan(update.appMemoryUsage, 0)
            XCTAssertTrue(MemoryPressureLevel.allCases.contains(update.memoryPressure))
        }
    }
}
