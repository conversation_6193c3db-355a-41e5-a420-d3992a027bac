//
//  VoiceRecordingServiceTests.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest
import AVFoundation
import Speech
import Combine
@testable import EchoNote

// MARK: - Mock Dependencies

class MockAudioSessionManager: AudioSessionManaging {
    @Published var currentSessionState: AudioSessionState = .inactive
    var sessionStatePublisher: AnyPublisher<AudioSessionState, Never> {
        $currentSessionState.eraseToAnyPublisher()
    }
    
    var shouldThrowError = false
    var configuredMode: RecordingMode?
    
    func configureSession(for mode: RecordingMode) async throws {
        if shouldThrowError {
            throw AudioSessionError.configurationFailed(underlying: NSError(domain: "Test", code: 1))
        }
        configuredMode = mode
        currentSessionState = .active(mode)
    }
    
    func activateSession() async throws {
        if shouldThrowError {
            throw AudioSessionError.activationFailed(underlying: NSError(domain: "Test", code: 2))
        }
    }
    
    func deactivateSession() async throws {
        if shouldThrowError {
            throw AudioSessionError.deactivationFailed(underlying: NSError(domain: "Test", code: 3))
        }
        currentSessionState = .inactive
    }
    
    func handleInterruption(_ type: AVAudioSession.InterruptionType) async {
        currentSessionState = .interrupted
    }
    
    func handleRouteChange(_ reason: AVAudioSession.RouteChangeReason) async {
        // Mock implementation
    }
}

class MockPermissionsService: PermissionsServiceProtocol {
    var hasMicrophonePermission: Bool = true
    var hasSpeechRecognitionPermission: Bool = true
    var shouldGrantPermissions = true
    
    func requestMicrophonePermission() async -> Bool {
        return shouldGrantPermissions
    }
    
    func requestSpeechRecognitionPermission() async -> Bool {
        return shouldGrantPermissions
    }
    
    func requestAllPermissions() async -> Bool {
        return shouldGrantPermissions && hasMicrophonePermission && hasSpeechRecognitionPermission
    }
}

// MARK: - VoiceRecordingService Tests

@MainActor
final class VoiceRecordingServiceTests: XCTestCase {
    
    var voiceRecordingService: VoiceRecordingService!
    var mockAudioSessionManager: MockAudioSessionManager!
    var mockPermissionsService: MockPermissionsService!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() async throws {
        mockAudioSessionManager = MockAudioSessionManager()
        mockPermissionsService = MockPermissionsService()
        cancellables = Set<AnyCancellable>()
        
        voiceRecordingService = VoiceRecordingService(
            audioSessionManager: mockAudioSessionManager,
            permissionsService: mockPermissionsService
        )
    }
    
    override func tearDown() async throws {
        await voiceRecordingService.cancelRecording()
        cancellables.removeAll()
        voiceRecordingService = nil
        mockAudioSessionManager = nil
        mockPermissionsService = nil
    }
    
    // MARK: - Initial State Tests
    
    func testInitialState() {
        // Then
        XCTAssertFalse(voiceRecordingService.isRecording)
        XCTAssertNil(voiceRecordingService.currentSession)
        XCTAssertEqual(voiceRecordingService.recordingDuration, 0)
        XCTAssertEqual(voiceRecordingService.transcribedText, "")
        XCTAssertTrue(voiceRecordingService.audioLevels.isEmpty)
        XCTAssertEqual(voiceRecordingService.recordingState.value, .idle)
    }
    
    // MARK: - Recording State Tests
    
    func testRecordingStatePublisher() async throws {
        // Given
        var receivedStates: [RecordingState] = []
        let expectation = XCTestExpectation(description: "State changes received")
        expectation.expectedFulfillmentCount = 3
        
        voiceRecordingService.recordingState
            .sink { state in
                receivedStates.append(state)
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // When
        let session = try await voiceRecordingService.startRecording(mode: .manual)
        let _ = try await voiceRecordingService.stopRecording()
        
        // Then
        await fulfillment(of: [expectation], timeout: 2.0)
        
        XCTAssertEqual(receivedStates.count, 3)
        XCTAssertEqual(receivedStates[0], .idle) // Initial state
        if case .recording(let duration) = receivedStates[1] {
            XCTAssertEqual(duration, 0, accuracy: 0.1)
        } else {
            XCTFail("Expected recording state")
        }
        XCTAssertEqual(receivedStates[2], .processing)
    }
    
    // MARK: - Start Recording Tests
    
    func testStartRecordingSuccess() async throws {
        // When
        let session = try await voiceRecordingService.startRecording(mode: .manual)
        
        // Then
        XCTAssertTrue(voiceRecordingService.isRecording)
        XCTAssertNotNil(voiceRecordingService.currentSession)
        XCTAssertEqual(voiceRecordingService.currentSession?.id, session.id)
        XCTAssertEqual(voiceRecordingService.currentSession?.mode, .manual)
        XCTAssertEqual(mockAudioSessionManager.configuredMode, .manual)
        
        if case .recording(let duration) = voiceRecordingService.recordingState.value {
            XCTAssertEqual(duration, 0, accuracy: 0.1)
        } else {
            XCTFail("Expected recording state")
        }
    }
    
    func testStartRecordingWhenAlreadyRecording() async throws {
        // Given
        let _ = try await voiceRecordingService.startRecording(mode: .manual)
        
        // When/Then
        do {
            let _ = try await voiceRecordingService.startRecording(mode: .background)
            XCTFail("Expected error when starting recording while already recording")
        } catch {
            XCTAssertTrue(error is RecordingError)
            if case .failed(let stateError) = voiceRecordingService.recordingState.value {
                XCTAssertTrue(stateError is RecordingError)
            } else {
                XCTFail("Expected failed state")
            }
        }
    }
    
    func testStartRecordingWithoutPermissions() async throws {
        // Given
        mockPermissionsService.shouldGrantPermissions = false
        
        // When/Then
        do {
            let _ = try await voiceRecordingService.startRecording(mode: .manual)
            XCTFail("Expected error when permissions denied")
        } catch {
            XCTAssertTrue(error is RecordingError)
            XCTAssertFalse(voiceRecordingService.isRecording)
            
            if case .failed(let stateError) = voiceRecordingService.recordingState.value {
                XCTAssertTrue(stateError is RecordingError)
            } else {
                XCTFail("Expected failed state")
            }
        }
    }
    
    func testStartRecordingWithAudioSessionError() async throws {
        // Given
        mockAudioSessionManager.shouldThrowError = true
        
        // When/Then
        do {
            let _ = try await voiceRecordingService.startRecording(mode: .manual)
            XCTFail("Expected error when audio session configuration fails")
        } catch {
            XCTAssertTrue(error is AudioSessionError)
            XCTAssertFalse(voiceRecordingService.isRecording)
        }
    }
    
    // MARK: - Stop Recording Tests
    
    func testStopRecordingSuccess() async throws {
        // Given
        let session = try await voiceRecordingService.startRecording(mode: .manual)
        
        // When
        let result = try await voiceRecordingService.stopRecording()
        
        // Then
        XCTAssertFalse(voiceRecordingService.isRecording)
        XCTAssertNil(voiceRecordingService.currentSession)
        XCTAssertEqual(result.session.id, session.id)
        XCTAssertEqual(voiceRecordingService.recordingState.value, .idle)
    }
    
    func testStopRecordingWhenNotRecording() async throws {
        // When/Then
        do {
            let _ = try await voiceRecordingService.stopRecording()
            XCTFail("Expected error when stopping recording while not recording")
        } catch {
            XCTAssertTrue(error is RecordingError)
            
            if case .failed(let stateError) = voiceRecordingService.recordingState.value {
                XCTAssertTrue(stateError is RecordingError)
            } else {
                XCTFail("Expected failed state")
            }
        }
    }
    
    // MARK: - Cancel Recording Tests
    
    func testCancelRecording() async throws {
        // Given
        let session = try await voiceRecordingService.startRecording(mode: .manual)
        let audioURL = session.audioURL
        
        // When
        await voiceRecordingService.cancelRecording()
        
        // Then
        XCTAssertFalse(voiceRecordingService.isRecording)
        XCTAssertNil(voiceRecordingService.currentSession)
        XCTAssertEqual(voiceRecordingService.recordingDuration, 0)
        XCTAssertEqual(voiceRecordingService.transcribedText, "")
        XCTAssertTrue(voiceRecordingService.audioLevels.isEmpty)
        XCTAssertEqual(voiceRecordingService.recordingState.value, .idle)
        
        // Audio file should be cleaned up
        XCTAssertFalse(FileManager.default.fileExists(atPath: audioURL.path))
    }
    
    // MARK: - Transcription Tests
    
    func testTranscribeAudioFromURL() async throws {
        // Given
        let testURL = URL(fileURLWithPath: "/tmp/test.m4a")
        
        // When/Then
        // Note: This test would require a real audio file and Speech framework setup
        // In a real test environment, we would mock SFSpeechRecognizer
        do {
            let _ = try await voiceRecordingService.transcribeAudio(from: testURL)
            // If we reach here, transcription was attempted
        } catch {
            // Expected to fail in test environment without real audio file
            XCTAssertTrue(error is RecordingError || error is NSError)
        }
    }
    
    // MARK: - Performance Tests
    
    func testRecordingPerformance() {
        measure {
            Task {
                do {
                    let _ = try await voiceRecordingService.startRecording(mode: .manual)
                    let _ = try await voiceRecordingService.stopRecording()
                } catch {
                    // Performance test - errors are acceptable
                }
            }
        }
    }
    
    // MARK: - Thread Safety Tests
    
    func testConcurrentRecordingOperations() async {
        let expectation = XCTestExpectation(description: "Concurrent operations complete")
        expectation.expectedFulfillmentCount = 5
        
        await withTaskGroup(of: Void.self) { group in
            for _ in 0..<5 {
                group.addTask {
                    do {
                        let _ = try await self.voiceRecordingService.startRecording(mode: .manual)
                        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
                        let _ = try await self.voiceRecordingService.stopRecording()
                    } catch {
                        // Expected - only one recording should succeed
                    }
                    expectation.fulfill()
                }
            }
        }
        
        await fulfillment(of: [expectation], timeout: 5.0)
        
        // Should end up in idle state
        XCTAssertEqual(voiceRecordingService.recordingState.value, .idle)
        XCTAssertFalse(voiceRecordingService.isRecording)
    }
}
