//
//  PerformanceTests.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest
import Combine
@testable import EchoNote

// MARK: - Performance Tests

final class PerformanceTests: XCTestCase {
    
    var serviceManager: ServiceManager!
    var recordingCoordinator: RecordingCoordinator!
    var mockVoiceRecordingService: MockVoiceRecordingServiceForPerformance!
    var mockNoteCreationService: MockNoteCreationServiceForPerformance!
    var mockKeywordDetectionService: MockKeywordDetectionServiceForPerformance!
    var mockAudioSessionManager: MockAudioSessionManagerForPerformance!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() async throws {
        // Create mock services
        mockVoiceRecordingService = MockVoiceRecordingServiceForPerformance()
        mockNoteCreationService = MockNoteCreationServiceForPerformance()
        mockKeywordDetectionService = MockKeywordDetectionServiceForPerformance()
        mockAudioSessionManager = MockAudioSessionManagerForPerformance()
        
        // Create service manager
        serviceManager = ServiceManager()
        
        // Create recording coordinator with mocked services
        recordingCoordinator = RecordingCoordinator(
            voiceRecordingService: mockVoiceRecordingService,
            noteCreationService: mockNoteCreationService,
            keywordDetectionService: mockKeywordDetectionService,
            audioSessionManager: mockAudioSessionManager
        )
        
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() async throws {
        cancellables.removeAll()
        recordingCoordinator = nil
        mockVoiceRecordingService = nil
        mockNoteCreationService = nil
        mockKeywordDetectionService = nil
        mockAudioSessionManager = nil
        serviceManager = nil
    }
    
    // MARK: - Recording Performance Tests
    
    func testRecordingStartLatency() {
        measure {
            Task {
                try? await recordingCoordinator.startManualRecording()
                await recordingCoordinator.cancelCurrentRecording()
            }
        }
    }
    
    func testRecordingStopLatency() async throws {
        // Given
        try await recordingCoordinator.startManualRecording()
        
        // When/Then
        measure {
            Task {
                _ = try? await recordingCoordinator.stopManualRecording()
            }
        }
    }
    
    func testConcurrentRecordingOperations() {
        measure {
            Task {
                // Simulate multiple concurrent recording attempts
                await withTaskGroup(of: Void.self) { group in
                    for _ in 0..<10 {
                        group.addTask {
                            try? await self.recordingCoordinator.startManualRecording()
                            await self.recordingCoordinator.cancelCurrentRecording()
                        }
                    }
                }
            }
        }
    }
    
    func testRecordingMemoryUsage() async throws {
        // Start recording
        try await recordingCoordinator.startManualRecording()
        
        // Measure memory usage during recording
        measure(metrics: [XCTMemoryMetric()]) {
            // Simulate recording activity
            for _ in 0..<100 {
                // Simulate audio processing
                autoreleasepool {
                    let _ = Data(count: 1024) // Simulate audio data
                }
            }
        }
        
        // Stop recording
        _ = try? await recordingCoordinator.stopManualRecording()
    }
    
    // MARK: - Keyword Detection Performance Tests
    
    func testKeywordDetectionStartup() {
        let keywords = (0..<100).map { "keyword\($0)" }
        
        measure {
            Task {
                try? await mockKeywordDetectionService.startMonitoring(for: keywords)
                await mockKeywordDetectionService.stopMonitoring()
            }
        }
    }
    
    func testKeywordDetectionThroughput() async throws {
        // Given
        let keywords = ["test", "performance", "keyword"]
        try await mockKeywordDetectionService.startMonitoring(for: keywords)
        
        // When/Then
        measure {
            // Simulate rapid keyword detections
            for i in 0..<1000 {
                mockKeywordDetectionService.simulateKeywordDetection("test\(i % 3)")
            }
        }
        
        await mockKeywordDetectionService.stopMonitoring()
    }
    
    func testKeywordDetectionMemoryUsage() async throws {
        let keywords = (0..<1000).map { "keyword\($0)" }
        
        measure(metrics: [XCTMemoryMetric()]) {
            Task {
                try? await mockKeywordDetectionService.startMonitoring(for: keywords)
                
                // Simulate keyword detection activity
                for _ in 0..<100 {
                    mockKeywordDetectionService.simulateKeywordDetection("test")
                }
                
                await mockKeywordDetectionService.stopMonitoring()
            }
        }
    }
    
    // MARK: - Background Monitoring Performance Tests
    
    func testBackgroundMonitoringStartup() {
        let keywords = (0..<50).map { "background\($0)" }
        
        measure {
            Task {
                try? await recordingCoordinator.enableBackgroundMonitoring(keywords: keywords)
                await recordingCoordinator.disableBackgroundMonitoring()
            }
        }
    }
    
    func testBackgroundMonitoringCPUUsage() async throws {
        // Given
        let keywords = ["cpu", "test", "performance"]
        try await recordingCoordinator.enableBackgroundMonitoring(keywords: keywords)
        
        // When/Then
        measure(metrics: [XCTCPUMetric()]) {
            // Simulate background monitoring activity
            for _ in 0..<100 {
                mockKeywordDetectionService.simulateKeywordDetection("cpu")
                Thread.sleep(forTimeInterval: 0.01) // 10ms
            }
        }
        
        await recordingCoordinator.disableBackgroundMonitoring()
    }
    
    // MARK: - Note Creation Performance Tests
    
    func testNoteCreationPerformance() {
        let session = RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: URL(fileURLWithPath: "/test.m4a"),
            mode: .manual
        )
        let result = RecordingResult(
            session: session,
            duration: 10.0,
            transcription: "Performance test note content",
            audioURL: session.audioURL
        )
        
        measure {
            Task {
                _ = try? await mockNoteCreationService.createNote(
                    from: result,
                    title: "Performance Test",
                    keywords: []
                )
            }
        }
    }
    
    func testBulkNoteCreation() {
        measure {
            Task {
                for i in 0..<100 {
                    _ = try? await mockNoteCreationService.createTextNote(
                        title: "Bulk Note \(i)",
                        content: "Content for note \(i)",
                        keywords: []
                    )
                }
            }
        }
    }
    
    // MARK: - Service Manager Performance Tests
    
    func testServiceManagerInitialization() {
        measure {
            let _ = ServiceManager()
        }
    }
    
    func testServiceManagerConcurrentAccess() {
        measure {
            Task {
                await withTaskGroup(of: Void.self) { group in
                    for _ in 0..<20 {
                        group.addTask {
                            let _ = await self.serviceManager.getRecordingCoordinator()
                            let _ = await self.serviceManager.getKeywordDetectionService()
                            let _ = await self.serviceManager.getPermissionsService()
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Memory Optimization Performance Tests
    
    func testMemoryOptimizationPerformance() {
        let memoryManager = MemoryOptimizationManager.shared
        
        measure {
            Task {
                await memoryManager.optimizeMemoryUsage()
            }
        }
    }
    
    func testMemoryMonitoringOverhead() {
        let memoryManager = MemoryOptimizationManager.shared
        
        measure(metrics: [XCTCPUMetric()]) {
            memoryManager.startMemoryMonitoring()
            Thread.sleep(forTimeInterval: 1.0) // Monitor for 1 second
            memoryManager.stopMemoryMonitoring()
        }
    }
    
    // MARK: - Event Bus Performance Tests
    
    func testEventBusPublishingPerformance() {
        let eventBus = EventBus.shared
        
        measure {
            for i in 0..<1000 {
                let session = RecordingSession(
                    id: UUID(),
                    startTime: Date(),
                    audioURL: URL(fileURLWithPath: "/test\(i).m4a"),
                    mode: .manual
                )
                eventBus.publishRecordingStarted(session: session)
            }
        }
    }
    
    func testEventBusSubscriptionPerformance() {
        let eventBus = EventBus.shared
        var eventCount = 0
        
        measure {
            let subscription = eventBus.recordingStarted
                .sink { _ in
                    eventCount += 1
                }
            
            for i in 0..<1000 {
                let session = RecordingSession(
                    id: UUID(),
                    startTime: Date(),
                    audioURL: URL(fileURLWithPath: "/test\(i).m4a"),
                    mode: .manual
                )
                eventBus.publishRecordingStarted(session: session)
            }
            
            subscription.cancel()
        }
        
        XCTAssertEqual(eventCount, 1000)
    }
    
    // MARK: - Error Recovery Performance Tests
    
    func testErrorRecoveryPerformance() {
        let errorRecoveryManager = ErrorRecoveryManager.shared
        
        measure {
            Task {
                for i in 0..<50 {
                    let context = RecoveryContext(
                        originalError: RecordingError.audioSessionConfigurationFailed,
                        component: "Recording",
                        operation: "test\(i)"
                    )
                    _ = await errorRecoveryManager.attemptRecovery(for: context)
                }
            }
        }
    }
    
    // MARK: - Preferences Management Performance Tests
    
    func testPreferencesUpdatePerformance() {
        let preferencesManager = PreferencesManager(userDefaults: UserDefaults())
        
        measure {
            Task {
                for i in 0..<100 {
                    await preferencesManager.setAnimationSpeed(Double(i % 3) + 0.5)
                }
            }
        }
    }
    
    // MARK: - Keyword Management Performance Tests
    
    func testKeywordManagerPerformance() async throws {
        let mockDataManager = MockDataManagerForKeywords()
        let keywordManager = KeywordManager(dataManager: mockDataManager)
        
        measure {
            Task {
                for i in 0..<100 {
                    try? await keywordManager.addKeyword("performance\(i)")
                }
            }
        }
    }
    
    // MARK: - Integration Performance Tests
    
    func testFullRecordingWorkflow() {
        measure {
            Task {
                // Start recording
                try? await recordingCoordinator.startManualRecording()
                
                // Simulate recording duration
                try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
                
                // Stop recording and create note
                _ = try? await recordingCoordinator.stopManualRecording()
            }
        }
    }
    
    func testBackgroundKeywordDetectionWorkflow() async throws {
        // Given
        let keywords = ["workflow", "test", "performance"]
        
        measure {
            Task {
                // Enable background monitoring
                try? await recordingCoordinator.enableBackgroundMonitoring(keywords: keywords)
                
                // Simulate keyword detection
                try? await recordingCoordinator.handleKeywordDetection(keyword: "workflow")
                
                // Disable background monitoring
                await recordingCoordinator.disableBackgroundMonitoring()
            }
        }
    }
}
