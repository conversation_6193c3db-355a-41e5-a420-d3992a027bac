//
//  MockServicesForPerformance.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import Foundation
import Combine
@testable import EchoNote

// MARK: - Mock Voice Recording Service for Performance

class MockVoiceRecordingServiceForPerformance: VoiceRecordingServicing {
    @Published var isRecording = false
    let recordingState = CurrentValueSubject<RecordingState, Never>(.idle)
    @Published var currentSession: RecordingSession?
    @Published var recordingDuration: TimeInterval = 0
    @Published var transcribedText = ""
    @Published var audioLevels: [Float] = []
    
    var recordingProgress: AnyPublisher<RecordingProgress, Never> {
        Timer.publish(every: 0.1, on: .main, in: .common)
            .autoconnect()
            .map { _ in
                RecordingProgress(
                    duration: self.recordingDuration,
                    transcription: self.transcribedText,
                    audioLevels: self.audioLevels,
                    isActive: self.isRecording
                )
            }
            .eraseToAnyPublisher()
    }
    
    func startRecording(mode: RecordingSession.RecordingMode) async throws -> RecordingSession {
        let session = RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: URL(fileURLWithPath: "/tmp/performance_test.m4a"),
            mode: mode
        )
        
        currentSession = session
        isRecording = true
        recordingState.send(.recording(session: session))
        
        // Simulate minimal processing delay
        try await Task.sleep(nanoseconds: 1_000_000) // 1ms
        
        return session
    }
    
    func stopRecording() async throws -> RecordingResult {
        guard let session = currentSession else {
            throw RecordingError.notRecording
        }
        
        currentSession = nil
        isRecording = false
        recordingState.send(.processing)
        
        // Simulate minimal processing delay
        try await Task.sleep(nanoseconds: 5_000_000) // 5ms
        
        let result = RecordingResult(
            session: session,
            duration: 1.0,
            transcription: "Performance test transcription",
            audioURL: session.audioURL
        )
        
        recordingState.send(.idle)
        return result
    }
    
    func pauseRecording() async throws {
        // Minimal implementation for performance testing
    }
    
    func resumeRecording() async throws {
        // Minimal implementation for performance testing
    }
    
    func cancelRecording() async {
        currentSession = nil
        isRecording = false
        recordingState.send(.idle)
    }
    
    func transcribeAudio(from url: URL) async throws -> String {
        // Simulate minimal transcription delay
        try await Task.sleep(nanoseconds: 2_000_000) // 2ms
        return "Performance test transcription"
    }
}

// MARK: - Mock Note Creation Service for Performance

class MockNoteCreationServiceForPerformance: NoteCreationServicing {
    
    func createNote(
        from result: RecordingResult,
        title: String?,
        keywords: [Keyword]
    ) async throws -> Note {
        // Simulate minimal processing delay
        try await Task.sleep(nanoseconds: 1_000_000) // 1ms
        
        return Note(
            title: title ?? "Performance Test Note",
            content: result.transcription,
            audioURL: result.audioURL,
            duration: result.duration,
            keywords: keywords
        )
    }
    
    func createTextNote(
        title: String,
        content: String,
        keywords: [Keyword]
    ) async throws -> Note {
        // Simulate minimal processing delay
        try await Task.sleep(nanoseconds: 500_000) // 0.5ms
        
        return Note(
            title: title,
            content: content,
            audioURL: nil,
            duration: 0,
            keywords: keywords
        )
    }
}

// MARK: - Mock Keyword Detection Service for Performance

class MockKeywordDetectionServiceForPerformance: KeywordDetectionServicing {
    @Published var isMonitoring = false
    @Published var activeKeywords: [String] = []
    
    let detectionState = CurrentValueSubject<DetectionState, Never>(.inactive)
    private let detectionSubject = PassthroughSubject<KeywordDetection, Never>()
    
    var detectionStream: AnyPublisher<KeywordDetection, Never> {
        detectionSubject.eraseToAnyPublisher()
    }
    
    func startMonitoring(for keywords: [String]) async throws {
        // Simulate minimal startup delay
        try await Task.sleep(nanoseconds: 2_000_000) // 2ms
        
        activeKeywords = keywords
        isMonitoring = true
        detectionState.send(.monitoring)
    }
    
    func stopMonitoring() async {
        isMonitoring = false
        activeKeywords = []
        detectionState.send(.inactive)
    }
    
    func updateKeywords(_ keywords: [String]) async {
        activeKeywords = keywords
    }
    
    func setDetectionSensitivity(_ sensitivity: Float) async {
        // Minimal implementation for performance testing
    }
    
    // Helper method for performance testing
    func simulateKeywordDetection(_ keyword: String) {
        let detection = KeywordDetection(
            keyword: keyword,
            confidence: 0.9,
            timestamp: Date(),
            context: "Performance test"
        )
        detectionSubject.send(detection)
        detectionState.send(.keywordDetected(keyword))
    }
}

// MARK: - Mock Audio Session Manager for Performance

class MockAudioSessionManagerForPerformance: AudioSessionManaging {
    @Published var currentSessionState: AudioSessionState = .inactive
    
    func configureSession(for mode: RecordingMode) async throws {
        // Simulate minimal configuration delay
        try await Task.sleep(nanoseconds: 1_000_000) // 1ms
        currentSessionState = .configured(mode)
    }
    
    func activateSession() async throws {
        // Simulate minimal activation delay
        try await Task.sleep(nanoseconds: 500_000) // 0.5ms
        currentSessionState = .active
    }
    
    func deactivateSession() async throws {
        // Simulate minimal deactivation delay
        try await Task.sleep(nanoseconds: 500_000) // 0.5ms
        currentSessionState = .inactive
    }
}

// MARK: - Mock Data Manager for Performance

class MockDataManagerForPerformance: DataManaging {
    private var notes: [Note] = []
    private var keywords: [Keyword] = []
    
    func saveNote(_ note: Note) async throws {
        // Simulate minimal save delay
        try await Task.sleep(nanoseconds: 1_000_000) // 1ms
        notes.append(note)
    }
    
    func fetchNotes() async throws -> [Note] {
        // Simulate minimal fetch delay
        try await Task.sleep(nanoseconds: 500_000) // 0.5ms
        return notes
    }
    
    func fetchNote(withId id: UUID) async throws -> Note? {
        return notes.first { $0.id == id }
    }
    
    func updateNote(_ note: Note) async throws {
        // Simulate minimal update delay
        try await Task.sleep(nanoseconds: 1_000_000) // 1ms
        if let index = notes.firstIndex(where: { $0.id == note.id }) {
            notes[index] = note
        }
    }
    
    func deleteNote(_ note: Note) async throws {
        notes.removeAll { $0.id == note.id }
    }
    
    func fetchKeywords() async throws -> [Keyword] {
        // Simulate minimal fetch delay
        try await Task.sleep(nanoseconds: 500_000) // 0.5ms
        return keywords
    }
    
    func saveKeywords(_ keywords: [Keyword]) async throws {
        // Simulate minimal save delay
        try await Task.sleep(nanoseconds: 1_000_000) // 1ms
        self.keywords = keywords
    }
    
    func save() async throws {
        // Simulate minimal save delay
        try await Task.sleep(nanoseconds: 2_000_000) // 2ms
    }
    
    func delete<T: PersistentModel>(_ model: T) async throws {
        // Minimal implementation for performance testing
    }
    
    func fetch<T: PersistentModel>(_ type: T.Type) async throws -> [T] {
        return []
    }
    
    func insert<T: PersistentModel>(_ model: T) async {
        // Minimal implementation for performance testing
    }
}

// MARK: - Mock Data Manager for Keywords (Performance)

class MockDataManagerForKeywords: DataManaging {
    private var keywords: [Keyword] = []
    
    func fetchKeywords() async throws -> [Keyword] {
        // Simulate minimal fetch delay
        try await Task.sleep(nanoseconds: 500_000) // 0.5ms
        return keywords
    }
    
    func saveKeywords(_ keywords: [Keyword]) async throws {
        // Simulate minimal save delay
        try await Task.sleep(nanoseconds: 1_000_000) // 1ms
        self.keywords = keywords
    }
    
    // Other DataManaging methods (minimal implementations)
    func saveNote(_ note: Note) async throws {}
    func fetchNotes() async throws -> [Note] { return [] }
    func fetchNote(withId id: UUID) async throws -> Note? { return nil }
    func updateNote(_ note: Note) async throws {}
    func deleteNote(_ note: Note) async throws {}
    func save() async throws {}
    func delete<T: PersistentModel>(_ model: T) async throws {}
    func fetch<T: PersistentModel>(_ type: T.Type) async throws -> [T] { return [] }
    func insert<T: PersistentModel>(_ model: T) async {}
}

// MARK: - Performance Metrics Helper

class PerformanceMetrics {
    static func measureExecutionTime<T>(
        operation: () async throws -> T
    ) async rethrows -> (result: T, executionTime: TimeInterval) {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = try await operation()
        let executionTime = CFAbsoluteTimeGetCurrent() - startTime
        return (result, executionTime)
    }
    
    static func measureMemoryUsage<T>(
        operation: () throws -> T
    ) rethrows -> (result: T, memoryUsage: UInt64) {
        let initialMemory = getCurrentMemoryUsage()
        let result = try operation()
        let finalMemory = getCurrentMemoryUsage()
        let memoryUsage = finalMemory > initialMemory ? finalMemory - initialMemory : 0
        return (result, memoryUsage)
    }
    
    private static func getCurrentMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        return result == KERN_SUCCESS ? UInt64(info.resident_size) : 0
    }
}

// MARK: - Performance Test Configuration

struct PerformanceTestConfig {
    static let defaultIterations = 100
    static let stressTestIterations = 1000
    static let concurrencyLevel = 10
    static let maxAcceptableLatency: TimeInterval = 0.1 // 100ms
    static let maxAcceptableMemoryIncrease: UInt64 = 10 * 1024 * 1024 // 10MB
}

// MARK: - Performance Assertions

extension XCTestCase {
    func assertPerformance<T>(
        _ operation: () async throws -> T,
        maxExecutionTime: TimeInterval = PerformanceTestConfig.maxAcceptableLatency,
        file: StaticString = #file,
        line: UInt = #line
    ) async rethrows -> T {
        let (result, executionTime) = try await PerformanceMetrics.measureExecutionTime(operation: operation)
        
        XCTAssertLessThan(
            executionTime,
            maxExecutionTime,
            "Operation took \(executionTime)s, expected less than \(maxExecutionTime)s",
            file: file,
            line: line
        )
        
        return result
    }
    
    func assertMemoryUsage<T>(
        _ operation: () throws -> T,
        maxMemoryIncrease: UInt64 = PerformanceTestConfig.maxAcceptableMemoryIncrease,
        file: StaticString = #file,
        line: UInt = #line
    ) rethrows -> T {
        let (result, memoryUsage) = try PerformanceMetrics.measureMemoryUsage(operation: operation)
        
        XCTAssertLessThan(
            memoryUsage,
            maxMemoryIncrease,
            "Operation used \(memoryUsage) bytes, expected less than \(maxMemoryIncrease) bytes",
            file: file,
            line: line
        )
        
        return result
    }
}
