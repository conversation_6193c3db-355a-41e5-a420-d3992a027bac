//
//  ErrorHandlerTests.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest
import SwiftUI
@testable import EchoNote

@MainActor
final class ErrorHandlerTests: XCTestCase {
    
    var errorHandler: ErrorHandler!
    
    override func setUp() async throws {
        errorHandler = ErrorHandler.shared
        errorHandler.clearAll() // Reset state
    }
    
    override func tearDown() async throws {
        errorHandler.clearAll()
        errorHandler = nil
    }
    
    // MARK: - Basic Error Handling Tests
    
    func testHandleError() {
        // Given
        let testError = NSError(domain: "Test", code: 1, userInfo: [NSLocalizedDescriptionKey: "Test error"])
        
        // When
        errorHandler.handle(testError)
        
        // Then
        XCTAssertNotNil(errorHandler.currentError)
        XCTAssertTrue(errorHandler.isShowingError)
        XCTAssertEqual(errorHandler.currentError?.error.localizedDescription, "Test error")
    }
    
    func testHandleErrorWithCustomTitle() {
        // Given
        let testError = NSError(domain: "Test", code: 1)
        let customTitle = "Custom Error Title"
        
        // When
        errorHandler.handle(testError, title: customTitle)
        
        // Then
        XCTAssertEqual(errorHandler.currentError?.title, customTitle)
    }
    
    func testHandleErrorWithCustomMessage() {
        // Given
        let testError = NSError(domain: "Test", code: 1)
        let customMessage = "Custom error message"
        
        // When
        errorHandler.handle(testError, message: customMessage)
        
        // Then
        XCTAssertEqual(errorHandler.currentError?.message, customMessage)
    }
    
    func testHandleErrorWithRetryAction() {
        // Given
        let testError = NSError(domain: "Test", code: 1)
        var retryActionCalled = false
        let retryAction = { retryActionCalled = true }
        
        // When
        errorHandler.handle(testError, retryAction: retryAction)
        
        // Then
        XCTAssertNotNil(errorHandler.currentError?.retryAction)
        
        // Execute retry action
        errorHandler.currentError?.retryAction?()
        XCTAssertTrue(retryActionCalled)
    }
    
    // MARK: - Specialized Error Handling Tests
    
    func testHandleRecordingError() {
        // Given
        let recordingError = RecordingError.permissionDenied
        
        // When
        errorHandler.handleRecordingError(recordingError)
        
        // Then
        XCTAssertNotNil(errorHandler.currentError)
        XCTAssertEqual(errorHandler.currentError?.title, "Recording Error")
        XCTAssertEqual(errorHandler.currentError?.severity, .critical)
    }
    
    func testHandleNetworkError() {
        // Given
        let networkError = NSError(domain: "Network", code: -1009, userInfo: [NSLocalizedDescriptionKey: "No internet connection"])
        
        // When
        errorHandler.handleNetworkError(networkError)
        
        // Then
        XCTAssertNotNil(errorHandler.currentError)
        XCTAssertEqual(errorHandler.currentError?.title, "Network Error")
        XCTAssertEqual(errorHandler.currentError?.severity, .warning)
    }
    
    func testHandlePermissionError() {
        // Given
        let permissionError = AudioSessionError.permissionDenied
        
        // When
        errorHandler.handlePermissionError(permissionError)
        
        // Then
        XCTAssertNotNil(errorHandler.currentError)
        XCTAssertEqual(errorHandler.currentError?.title, "Permission Required")
        XCTAssertEqual(errorHandler.currentError?.severity, .critical)
    }
    
    // MARK: - Info and Warning Tests
    
    func testShowInfo() {
        // Given
        let infoMessage = "This is an info message"
        
        // When
        errorHandler.showInfo(infoMessage)
        
        // Then
        XCTAssertNotNil(errorHandler.currentError)
        XCTAssertEqual(errorHandler.currentError?.title, "Info")
        XCTAssertEqual(errorHandler.currentError?.message, infoMessage)
        XCTAssertEqual(errorHandler.currentError?.severity, .info)
    }
    
    func testShowWarning() {
        // Given
        let warningMessage = "This is a warning message"
        
        // When
        errorHandler.showWarning(warningMessage)
        
        // Then
        XCTAssertNotNil(errorHandler.currentError)
        XCTAssertEqual(errorHandler.currentError?.title, "Warning")
        XCTAssertEqual(errorHandler.currentError?.message, warningMessage)
        XCTAssertEqual(errorHandler.currentError?.severity, .warning)
    }
    
    // MARK: - Dismiss and Clear Tests
    
    func testDismiss() {
        // Given
        let testError = NSError(domain: "Test", code: 1)
        errorHandler.handle(testError)
        XCTAssertTrue(errorHandler.isShowingError)
        
        // When
        errorHandler.dismiss()
        
        // Then
        XCTAssertNil(errorHandler.currentError)
        XCTAssertFalse(errorHandler.isShowingError)
    }
    
    func testClearAll() {
        // Given
        let testError1 = NSError(domain: "Test1", code: 1)
        let testError2 = NSError(domain: "Test2", code: 2)
        
        errorHandler.handle(testError1)
        errorHandler.errorQueue.append(ErrorInfo(error: testError2))
        
        XCTAssertTrue(errorHandler.isShowingError)
        XCTAssertFalse(errorHandler.errorQueue.isEmpty)
        
        // When
        errorHandler.clearAll()
        
        // Then
        XCTAssertNil(errorHandler.currentError)
        XCTAssertFalse(errorHandler.isShowingError)
        XCTAssertTrue(errorHandler.errorQueue.isEmpty)
    }
    
    // MARK: - Error Queue Tests
    
    func testErrorQueue() {
        // Given
        let errors = (1...3).map { NSError(domain: "Test\($0)", code: $0) }
        
        // When - Add multiple toast errors
        for error in errors {
            errorHandler.handle(error, style: .toast)
        }
        
        // Then
        XCTAssertNotNil(errorHandler.currentError)
        XCTAssertEqual(errorHandler.errorQueue.count, 2) // First error is current, others are queued
    }
    
    func testErrorQueueMaxSize() {
        // Given
        let maxSize = 5
        let errors = (1...10).map { NSError(domain: "Test\($0)", code: $0) }
        
        // When - Add more errors than max queue size
        for error in errors {
            errorHandler.handle(error, style: .toast)
        }
        
        // Then
        XCTAssertLessThanOrEqual(errorHandler.errorQueue.count, maxSize)
    }
    
    // MARK: - Error Severity Tests
    
    func testRecordingErrorSeverity() {
        let permissionError = RecordingError.permissionDenied
        XCTAssertEqual(permissionError.severity, .critical)
        
        let alreadyRecordingError = RecordingError.alreadyRecording
        XCTAssertEqual(alreadyRecordingError.severity, .warning)
        
        let speechError = RecordingError.speechRecognitionUnavailable
        XCTAssertEqual(speechError.severity, .error)
    }
    
    func testAudioSessionErrorSeverity() {
        let permissionError = AudioSessionError.permissionDenied
        XCTAssertEqual(permissionError.severity, .critical)
        
        let hardwareError = AudioSessionError.hardwareUnavailable
        XCTAssertEqual(hardwareError.severity, .critical)
        
        let configError = AudioSessionError.configurationFailed(underlying: NSError(domain: "Test", code: 1))
        XCTAssertEqual(configError.severity, .warning)
    }
    
    // MARK: - User Friendly Message Tests
    
    func testRecordingErrorUserFriendlyMessage() {
        let permissionError = RecordingError.permissionDenied
        XCTAssertEqual(permissionError.userFriendlyMessage, "Please allow microphone access in Settings to record notes.")
        
        let alreadyRecordingError = RecordingError.alreadyRecording
        XCTAssertEqual(alreadyRecordingError.userFriendlyMessage, "A recording is already in progress.")
    }
    
    func testAudioSessionErrorUserFriendlyMessage() {
        let permissionError = AudioSessionError.permissionDenied
        XCTAssertEqual(permissionError.userFriendlyMessage, "Microphone access is required to record notes.")
        
        let hardwareError = AudioSessionError.hardwareUnavailable
        XCTAssertEqual(hardwareError.userFriendlyMessage, "Audio hardware is not available.")
    }
    
    // MARK: - ErrorInfo Tests
    
    func testErrorInfoInitialization() {
        // Given
        let testError = NSError(domain: "Test", code: 1, userInfo: [NSLocalizedDescriptionKey: "Test error"])
        let retryAction = {}
        let dismissAction = {}
        
        // When
        let errorInfo = ErrorInfo(
            error: testError,
            severity: .warning,
            title: "Custom Title",
            message: "Custom Message",
            retryAction: retryAction,
            dismissAction: dismissAction
        )
        
        // Then
        XCTAssertEqual(errorInfo.severity, .warning)
        XCTAssertEqual(errorInfo.title, "Custom Title")
        XCTAssertEqual(errorInfo.message, "Custom Message")
        XCTAssertNotNil(errorInfo.retryAction)
        XCTAssertNotNil(errorInfo.dismissAction)
    }
    
    func testErrorInfoDefaultValues() {
        // Given
        let testError = NSError(domain: "Test", code: 1, userInfo: [NSLocalizedDescriptionKey: "Test error"])
        
        // When
        let errorInfo = ErrorInfo(error: testError)
        
        // Then
        XCTAssertEqual(errorInfo.severity, .error)
        XCTAssertEqual(errorInfo.title, "Error")
        XCTAssertEqual(errorInfo.message, "Test error")
        XCTAssertNil(errorInfo.retryAction)
        XCTAssertNil(errorInfo.dismissAction)
    }
    
    // MARK: - Performance Tests
    
    func testErrorHandlingPerformance() {
        measure {
            for i in 0..<100 {
                let error = NSError(domain: "Performance", code: i)
                errorHandler.handle(error)
                errorHandler.dismiss()
            }
        }
    }
}
