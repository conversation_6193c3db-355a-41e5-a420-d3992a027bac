//
//  RecordingCoordinatorTests.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest
import Combine
@testable import EchoNote

// MARK: - Mock Services

class MockVoiceRecordingService: VoiceRecordingServicing {
    @Published var isRecording = false
    @Published var currentSession: RecordingSession?
    @Published var recordingDuration: TimeInterval = 0
    @Published var transcribedText = ""
    @Published var audioLevels: [Float] = []
    
    let recordingState = CurrentValueSubject<RecordingState, Never>(.idle)
    var recordingProgress: AnyPublisher<RecordingProgress, Never> {
        Just(RecordingProgress(duration: 0, audioLevel: 0)).eraseToAnyPublisher()
    }
    
    var shouldThrowError = false
    var mockRecordingResult: RecordingResult?
    
    func startRecording(mode: RecordingSession.RecordingMode) async throws -> RecordingSession {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 1)
        }
        
        let session = RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: URL(fileURLWithPath: "/tmp/test.m4a"),
            mode: mode
        )
        
        currentSession = session
        isRecording = true
        return session
    }
    
    func stopRecording() async throws -> RecordingResult {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 2)
        }
        
        guard let session = currentSession else {
            throw NSError(domain: "MockError", code: 3)
        }
        
        isRecording = false
        currentSession = nil
        
        return mockRecordingResult ?? RecordingResult(
            session: session,
            duration: 30.0,
            transcription: "Mock transcription",
            audioURL: session.audioURL,
            audioLevels: []
        )
    }
    
    func pauseRecording() async throws {}
    func resumeRecording() async throws {}
    
    func cancelRecording() async {
        isRecording = false
        currentSession = nil
    }
    
    func transcribeAudio(from url: URL) async throws -> String {
        return "Mock transcription"
    }
}

class MockKeywordDetectionService: KeywordDetectionServicing {
    @Published var isMonitoring = false
    @Published var activeKeywords: [String] = []
    
    let detectionState = CurrentValueSubject<DetectionState, Never>(.inactive)
    var detectionStream: AnyPublisher<KeywordDetection, Never> {
        Just(KeywordDetection(keyword: "test", confidence: 0.9, timestamp: Date(), context: "test")).eraseToAnyPublisher()
    }
    
    var shouldThrowError = false
    
    func startMonitoring(for keywords: [String]) async throws {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 4)
        }
        
        activeKeywords = keywords
        isMonitoring = true
        detectionState.send(.monitoring)
    }
    
    func stopMonitoring() async {
        isMonitoring = false
        activeKeywords = []
        detectionState.send(.inactive)
    }
    
    func updateKeywords(_ keywords: [String]) async {
        activeKeywords = keywords
    }
    
    func setDetectionSensitivity(_ sensitivity: Float) async {}
}

class MockNoteCreationService: NoteCreationServicing {
    var shouldThrowError = false
    var mockNote: Note?
    
    func createNote(from recording: RecordingResult, transcription: String) async throws -> Note {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 5)
        }
        
        return mockNote ?? Note(
            title: "Mock Note",
            content: transcription,
            audioURL: recording.audioURL.path,
            duration: recording.duration,
            createdAt: Date(),
            modifiedAt: Date(),
            isFavorite: false,
            keywords: []
        )
    }
    
    func createNote(from request: NoteCreationRequest) async throws -> Note {
        return mockNote ?? Note(
            title: request.title ?? "Mock Note",
            content: request.content,
            audioURL: request.audioURL,
            duration: request.duration,
            createdAt: Date(),
            modifiedAt: Date(),
            isFavorite: false,
            keywords: []
        )
    }
    
    func createTextNote(title: String, content: String, keywords: [String]) async throws -> Note {
        return mockNote ?? Note(
            title: title,
            content: content,
            audioURL: nil,
            duration: 0,
            createdAt: Date(),
            modifiedAt: Date(),
            isFavorite: false,
            keywords: []
        )
    }
    
    func saveNote(_ note: Note) async throws {}
    func deleteNote(_ note: Note) async throws {}
    func updateNote(_ note: Note) async throws {}
}

class MockDataManager: DataManaging {
    func saveNote(_ note: Note) async throws {}
    func fetchNotes() async throws -> [Note] { return [] }
    func fetchNote(withId id: UUID) async throws -> Note? { return nil }
    func updateNote(_ note: Note) async throws {}
    func deleteNote(_ note: Note) async throws {}
    func fetchKeywords() async throws -> [Keyword] { return [] }
    func saveKeywords(_ keywords: [Keyword]) async throws {}
    func save() async throws {}
    func delete<T: PersistentModel>(_ model: T) async throws {}
    func fetch<T: PersistentModel>(_ type: T.Type) async throws -> [T] { return [] }
    func insert<T: PersistentModel>(_ model: T) async {}
}

// MARK: - RecordingCoordinator Tests

@MainActor
final class RecordingCoordinatorTests: XCTestCase {
    
    var recordingCoordinator: RecordingCoordinator!
    var mockVoiceService: MockVoiceRecordingService!
    var mockKeywordService: MockKeywordDetectionService!
    var mockNoteService: MockNoteCreationService!
    var mockDataManager: MockDataManager!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() async throws {
        mockVoiceService = MockVoiceRecordingService()
        mockKeywordService = MockKeywordDetectionService()
        mockNoteService = MockNoteCreationService()
        mockDataManager = MockDataManager()
        cancellables = Set<AnyCancellable>()
        
        recordingCoordinator = RecordingCoordinator(
            voiceRecordingService: mockVoiceService,
            keywordDetectionService: mockKeywordService,
            noteCreationService: mockNoteService,
            dataManager: mockDataManager
        )
    }
    
    override func tearDown() async throws {
        await recordingCoordinator.cancelCurrentRecording()
        cancellables.removeAll()
        recordingCoordinator = nil
        mockVoiceService = nil
        mockKeywordService = nil
        mockNoteService = nil
        mockDataManager = nil
    }
    
    // MARK: - Initial State Tests
    
    func testInitialState() {
        XCTAssertEqual(recordingCoordinator.recordingState.value, .idle)
        XCTAssertNil(recordingCoordinator.activeSession)
        XCTAssertNil(recordingCoordinator.lastCreatedNote)
        XCTAssertFalse(recordingCoordinator.isBackgroundMonitoringEnabled)
    }
    
    // MARK: - Manual Recording Tests
    
    func testStartManualRecording() async throws {
        // When
        try await recordingCoordinator.startManualRecording()
        
        // Then
        XCTAssertNotNil(recordingCoordinator.activeSession)
        XCTAssertTrue(mockVoiceService.isRecording)
        
        if case .recording(let session) = recordingCoordinator.recordingState.value {
            XCTAssertEqual(session.mode, .manual)
        } else {
            XCTFail("Expected recording state")
        }
    }
    
    func testStartManualRecordingWhenAlreadyRecording() async throws {
        // Given
        try await recordingCoordinator.startManualRecording()
        
        // When/Then
        do {
            try await recordingCoordinator.startManualRecording()
            XCTFail("Expected error when starting recording while already recording")
        } catch {
            XCTAssertTrue(error is RecordingCoordinatorError)
        }
    }
    
    func testStopManualRecording() async throws {
        // Given
        try await recordingCoordinator.startManualRecording()
        
        // When
        let note = try await recordingCoordinator.stopManualRecording()
        
        // Then
        XCTAssertNotNil(note)
        XCTAssertNil(recordingCoordinator.activeSession)
        XCTAssertFalse(mockVoiceService.isRecording)
        XCTAssertEqual(recordingCoordinator.recordingState.value, .idle)
        XCTAssertEqual(recordingCoordinator.lastCreatedNote?.title, "Mock Note")
    }
    
    func testStopManualRecordingWithoutActiveRecording() async throws {
        // When/Then
        do {
            _ = try await recordingCoordinator.stopManualRecording()
            XCTFail("Expected error when stopping recording without active session")
        } catch {
            XCTAssertTrue(error is RecordingCoordinatorError)
        }
    }
    
    // MARK: - Background Monitoring Tests
    
    func testEnableBackgroundMonitoring() async throws {
        // Given
        let keywords = ["idea", "task", "note"]
        
        // When
        try await recordingCoordinator.enableBackgroundMonitoring(keywords: keywords)
        
        // Then
        XCTAssertTrue(recordingCoordinator.isBackgroundMonitoringEnabled)
        XCTAssertTrue(mockKeywordService.isMonitoring)
        XCTAssertEqual(mockKeywordService.activeKeywords, keywords)
        
        if case .listening(let listeningKeywords) = recordingCoordinator.recordingState.value {
            XCTAssertEqual(listeningKeywords, keywords)
        } else {
            XCTFail("Expected listening state")
        }
    }
    
    func testDisableBackgroundMonitoring() async throws {
        // Given
        try await recordingCoordinator.enableBackgroundMonitoring(keywords: ["test"])
        XCTAssertTrue(recordingCoordinator.isBackgroundMonitoringEnabled)
        
        // When
        await recordingCoordinator.disableBackgroundMonitoring()
        
        // Then
        XCTAssertFalse(recordingCoordinator.isBackgroundMonitoringEnabled)
        XCTAssertFalse(mockKeywordService.isMonitoring)
        XCTAssertTrue(mockKeywordService.activeKeywords.isEmpty)
        XCTAssertEqual(recordingCoordinator.recordingState.value, .idle)
    }
    
    // MARK: - Keyword Detection Tests
    
    func testHandleKeywordDetection() async throws {
        // Given
        try await recordingCoordinator.enableBackgroundMonitoring(keywords: ["idea"])
        
        // When
        try await recordingCoordinator.handleKeywordDetection(keyword: "idea")
        
        // Then
        XCTAssertNotNil(recordingCoordinator.activeSession)
        XCTAssertTrue(mockVoiceService.isRecording)
        
        if case .recording(let session) = recordingCoordinator.recordingState.value {
            XCTAssertEqual(session.mode, .background)
        } else {
            XCTFail("Expected recording state")
        }
    }
    
    // MARK: - Siri Shortcut Tests
    
    func testHandleSiriShortcutStartRecording() async throws {
        // When
        try await recordingCoordinator.handleSiriShortcut(shortcutType: .startRecording)
        
        // Then
        XCTAssertNotNil(recordingCoordinator.activeSession)
        XCTAssertTrue(mockVoiceService.isRecording)
    }
    
    func testHandleSiriShortcutStopRecording() async throws {
        // Given
        try await recordingCoordinator.startManualRecording()
        
        // When
        try await recordingCoordinator.handleSiriShortcut(shortcutType: .stopRecording)
        
        // Then
        XCTAssertNil(recordingCoordinator.activeSession)
        XCTAssertFalse(mockVoiceService.isRecording)
        XCTAssertNotNil(recordingCoordinator.lastCreatedNote)
    }
    
    func testHandleSiriShortcutCreateQuickNote() async throws {
        // When
        try await recordingCoordinator.handleSiriShortcut(shortcutType: .createQuickNote(content: "Quick note content"))
        
        // Then
        XCTAssertNotNil(recordingCoordinator.lastCreatedNote)
        XCTAssertEqual(recordingCoordinator.lastCreatedNote?.content, "Quick note content")
    }
    
    // MARK: - Cancel Recording Tests
    
    func testCancelCurrentRecording() async throws {
        // Given
        try await recordingCoordinator.startManualRecording()
        XCTAssertNotNil(recordingCoordinator.activeSession)
        
        // When
        await recordingCoordinator.cancelCurrentRecording()
        
        // Then
        XCTAssertNil(recordingCoordinator.activeSession)
        XCTAssertFalse(mockVoiceService.isRecording)
        XCTAssertEqual(recordingCoordinator.recordingState.value, .idle)
    }
    
    // MARK: - State Management Tests
    
    func testRecordingStatePublisher() async throws {
        // Given
        var receivedStates: [RecordingState] = []
        let expectation = XCTestExpectation(description: "State changes received")
        expectation.expectedFulfillmentCount = 4
        
        recordingCoordinator.recordingState
            .sink { state in
                receivedStates.append(state)
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // When
        try await recordingCoordinator.startManualRecording()
        _ = try await recordingCoordinator.stopManualRecording()
        
        // Then
        await fulfillment(of: [expectation], timeout: 2.0)
        
        XCTAssertEqual(receivedStates.count, 4)
        XCTAssertEqual(receivedStates[0], .idle) // Initial state
        if case .recording = receivedStates[1] {} else { XCTFail("Expected recording state") }
        XCTAssertEqual(receivedStates[2], .processing)
        XCTAssertEqual(receivedStates[3], .idle)
    }
    
    // MARK: - Error Handling Tests
    
    func testErrorHandling() async throws {
        // Given
        mockVoiceService.shouldThrowError = true
        
        // When/Then
        do {
            try await recordingCoordinator.startManualRecording()
            XCTFail("Expected error from voice service")
        } catch {
            // Should handle error gracefully
            if case .error = recordingCoordinator.recordingState.value {
                // Expected error state
            } else {
                XCTFail("Expected error state")
            }
        }
    }
}
