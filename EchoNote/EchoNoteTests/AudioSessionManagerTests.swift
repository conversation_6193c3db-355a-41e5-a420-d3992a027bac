//
//  AudioSessionManagerTests.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest
import AVFoundation
import Combine
@testable import EchoNote

// MARK: - Mock Audio Session

class MockAVAudioSession: AVAudioSession {
    var mockCategory: AVAudioSession.Category = .ambient
    var mockIsActive = false
    var shouldThrowError = false
    var lastSetCategory: AVAudioSession.Category?
    var lastSetMode: AVAudioSession.Mode?
    var lastSetOptions: AVAudioSession.CategoryOptions?
    
    override var category: AVAudioSession.Category {
        return mockCategory
    }
    
    override func setCategory(_ category: AVAudioSession.Category, mode: AVAudioSession.Mode, options: AVAudioSession.CategoryOptions) throws {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 1, userInfo: nil)
        }
        
        lastSetCategory = category
        lastSetMode = mode
        lastSetOptions = options
        mockCategory = category
    }
    
    override func setActive(_ active: Bool, options: AVAudioSession.SetActiveOptions) throws {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 2, userInfo: nil)
        }
        
        mockIsActive = active
    }
    
    override func setPreferredSampleRate(_ sampleRate: Double) throws {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 3, userInfo: nil)
        }
    }
    
    override func setPreferredIOBufferDuration(_ duration: TimeInterval) throws {
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 4, userInfo: nil)
        }
    }
}

// MARK: - AudioSessionManager Tests

@MainActor
final class AudioSessionManagerTests: XCTestCase {
    
    var audioSessionManager: AudioSessionManager!
    var mockAudioSession: MockAVAudioSession!
    var mockNotificationCenter: NotificationCenter!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() async throws {
        mockAudioSession = MockAVAudioSession()
        mockNotificationCenter = NotificationCenter()
        cancellables = Set<AnyCancellable>()
        
        // Create AudioSessionManager with mock dependencies
        audioSessionManager = AudioSessionManager()
    }
    
    override func tearDown() async throws {
        cancellables.removeAll()
        audioSessionManager = nil
        mockAudioSession = nil
        mockNotificationCenter = nil
    }
    
    // MARK: - Configuration Tests
    
    func testConfigureSessionForManualRecording() async throws {
        // When
        try await audioSessionManager.configureSession(for: .manual)
        
        // Then
        XCTAssertEqual(audioSessionManager.currentSessionState, .active(.manual))
        XCTAssertTrue(audioSessionManager.isActive)
    }
    
    func testConfigureSessionForBackgroundMonitoring() async throws {
        // When
        try await audioSessionManager.configureSession(for: .backgroundMonitoring)
        
        // Then
        XCTAssertEqual(audioSessionManager.currentSessionState, .active(.backgroundMonitoring))
        XCTAssertTrue(audioSessionManager.isActive)
    }
    
    func testConfigureSessionForSiriShortcut() async throws {
        // When
        try await audioSessionManager.configureSession(for: .siriShortcut)
        
        // Then
        XCTAssertEqual(audioSessionManager.currentSessionState, .active(.siriShortcut))
        XCTAssertTrue(audioSessionManager.isActive)
    }
    
    func testConfigureSessionForPlayback() async throws {
        // When
        try await audioSessionManager.configureSession(for: .playback)
        
        // Then
        XCTAssertEqual(audioSessionManager.currentSessionState, .active(.playback))
        XCTAssertTrue(audioSessionManager.isActive)
    }
    
    func testConfigureSessionFailure() async {
        // Given
        mockAudioSession.shouldThrowError = true
        
        // When/Then
        do {
            try await audioSessionManager.configureSession(for: .manual)
            XCTFail("Expected configuration to throw error")
        } catch {
            XCTAssertTrue(error is AudioSessionError)
            XCTAssertFalse(audioSessionManager.isActive)
        }
    }
    
    // MARK: - Session State Tests
    
    func testInitialState() {
        // Then
        XCTAssertEqual(audioSessionManager.currentSessionState, .inactive)
        XCTAssertFalse(audioSessionManager.isActive)
    }
    
    func testDeactivateSession() async throws {
        // Given
        try await audioSessionManager.configureSession(for: .manual)
        XCTAssertTrue(audioSessionManager.isActive)
        
        // When
        try await audioSessionManager.deactivateSession()
        
        // Then
        XCTAssertEqual(audioSessionManager.currentSessionState, .inactive)
        XCTAssertFalse(audioSessionManager.isActive)
    }
    
    // MARK: - State Publisher Tests
    
    func testSessionStatePublisher() async throws {
        // Given
        var receivedStates: [AudioSessionState] = []
        let expectation = XCTestExpectation(description: "State changes received")
        expectation.expectedFulfillmentCount = 3
        
        audioSessionManager.sessionStatePublisher
            .sink { state in
                receivedStates.append(state)
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // When
        try await audioSessionManager.configureSession(for: .manual)
        try await audioSessionManager.deactivateSession()
        
        // Then
        await fulfillment(of: [expectation], timeout: 2.0)
        
        XCTAssertEqual(receivedStates.count, 3)
        XCTAssertEqual(receivedStates[0], .inactive) // Initial state
        XCTAssertEqual(receivedStates[1], .active(.manual))
        XCTAssertEqual(receivedStates[2], .inactive)
    }
    
    // MARK: - Interruption Handling Tests
    
    func testHandleInterruptionBegan() async {
        // Given
        try? await audioSessionManager.configureSession(for: .manual)
        
        // When
        await audioSessionManager.handleInterruption(.began)
        
        // Then
        XCTAssertEqual(audioSessionManager.currentSessionState, .interrupted)
        XCTAssertFalse(audioSessionManager.isActive)
    }
    
    func testHandleInterruptionEnded() async {
        // Given
        await audioSessionManager.handleInterruption(.began)
        XCTAssertEqual(audioSessionManager.currentSessionState, .interrupted)
        
        // When
        await audioSessionManager.handleInterruption(.ended)
        
        // Then
        // Should attempt to reactivate (though it might fail in test environment)
        XCTAssertNotEqual(audioSessionManager.currentSessionState, .interrupted)
    }
    
    // MARK: - Route Change Handling Tests
    
    func testHandleRouteChangeNewDevice() async {
        // When
        await audioSessionManager.handleRouteChange(.newDeviceAvailable)
        
        // Then
        // Should handle gracefully without changing state
        XCTAssertEqual(audioSessionManager.currentSessionState, .inactive)
    }
    
    func testHandleRouteChangeNoSuitableRoute() async {
        // When
        await audioSessionManager.handleRouteChange(.noSuitableRouteForCategory)
        
        // Then
        // Should update state to failed
        if case .failed(let error) = audioSessionManager.currentSessionState {
            XCTAssertTrue(error is AudioSessionError)
        } else {
            XCTFail("Expected failed state with AudioSessionError")
        }
    }
    
    // MARK: - Performance Tests
    
    func testConfigurationPerformance() {
        measure {
            Task {
                try? await audioSessionManager.configureSession(for: .manual)
                try? await audioSessionManager.deactivateSession()
            }
        }
    }
    
    // MARK: - Thread Safety Tests
    
    func testConcurrentConfigurationCalls() async {
        let expectation = XCTestExpectation(description: "Concurrent operations complete")
        expectation.expectedFulfillmentCount = 5
        
        await withTaskGroup(of: Void.self) { group in
            for i in 0..<5 {
                group.addTask {
                    let mode: RecordingMode = i % 2 == 0 ? .manual : .backgroundMonitoring
                    try? await self.audioSessionManager.configureSession(for: mode)
                    expectation.fulfill()
                }
            }
        }
        
        await fulfillment(of: [expectation], timeout: 5.0)
        
        // Should end up in some active state
        XCTAssertTrue(audioSessionManager.isActive)
    }
}
