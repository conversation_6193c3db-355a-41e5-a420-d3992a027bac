//
//  ErrorRecoveryManagerTests.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest
import Combine
@testable import EchoNote

// MARK: - Mock Dependencies for Error Recovery

class MockRecordingCoordinatorForRecovery: RecordingCoordinating {
    let recordingState = CurrentValueSubject<RecordingState, Never>(.idle)
    @Published var activeSession: RecordingSession?
    @Published var lastCreatedNote: Note?
    
    var cancelCurrentRecordingCallCount = 0
    var startManualRecordingCallCount = 0
    var shouldThrowError = false
    
    func startManualRecording() async throws {
        startManualRecordingCallCount += 1
        if shouldThrowError {
            throw RecordingError.audioSessionConfigurationFailed
        }
    }
    
    func stopManualRecording() async throws -> Note? { return nil }
    func handleKeywordDetection(keyword: String) async throws {}
    func handleSiriShortcut(shortcutType: SiriShortcutType) async throws {}
    
    func cancelCurrentRecording() async {
        cancelCurrentRecordingCallCount += 1
    }
    
    func enableBackgroundMonitoring(keywords: [String]) async throws {}
    func disableBackgroundMonitoring() async {}
}

class MockKeywordDetectionServiceForRecovery: KeywordDetectionServicing {
    @Published var isMonitoring = false
    @Published var activeKeywords: [String] = []
    
    let detectionState = CurrentValueSubject<DetectionState, Never>(.inactive)
    var detectionStream: AnyPublisher<KeywordDetection, Never> {
        Just(KeywordDetection(keyword: "test", confidence: 0.9, timestamp: Date(), context: "test")).eraseToAnyPublisher()
    }
    
    var stopMonitoringCallCount = 0
    var startMonitoringCallCount = 0
    var shouldThrowError = false
    
    func startMonitoring(for keywords: [String]) async throws {
        startMonitoringCallCount += 1
        if shouldThrowError {
            throw NSError(domain: "MockError", code: 1)
        }
        activeKeywords = keywords
        isMonitoring = true
    }
    
    func stopMonitoring() async {
        stopMonitoringCallCount += 1
        isMonitoring = false
        activeKeywords = []
    }
    
    func updateKeywords(_ keywords: [String]) async {
        activeKeywords = keywords
    }
    
    func setDetectionSensitivity(_ sensitivity: Float) async {}
}

class MockAudioSessionManagerForRecovery: AudioSessionManaging {
    @Published var currentSessionState: AudioSessionState = .inactive
    
    var deactivateSessionCallCount = 0
    var configureSessionCallCount = 0
    var activateSessionCallCount = 0
    var shouldThrowError = false
    
    func configureSession(for mode: RecordingMode) async throws {
        configureSessionCallCount += 1
        if shouldThrowError {
            throw AudioSessionError.configurationFailed(underlying: NSError(domain: "MockError", code: 1))
        }
        currentSessionState = .configured(mode)
    }
    
    func activateSession() async throws {
        activateSessionCallCount += 1
        if shouldThrowError {
            throw AudioSessionError.activationFailed(underlying: NSError(domain: "MockError", code: 2))
        }
        currentSessionState = .active
    }
    
    func deactivateSession() async throws {
        deactivateSessionCallCount += 1
        if shouldThrowError {
            throw AudioSessionError.deactivationFailed(underlying: NSError(domain: "MockError", code: 3))
        }
        currentSessionState = .inactive
    }
}

// MARK: - Error Recovery Manager Tests

@MainActor
final class ErrorRecoveryManagerTests: XCTestCase {
    
    var errorRecoveryManager: ErrorRecoveryManager!
    var mockRecordingCoordinator: MockRecordingCoordinatorForRecovery!
    var mockKeywordDetectionService: MockKeywordDetectionServiceForRecovery!
    var mockAudioSessionManager: MockAudioSessionManagerForRecovery!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() async throws {
        errorRecoveryManager = ErrorRecoveryManager.shared
        mockRecordingCoordinator = MockRecordingCoordinatorForRecovery()
        mockKeywordDetectionService = MockKeywordDetectionServiceForRecovery()
        mockAudioSessionManager = MockAudioSessionManagerForRecovery()
        cancellables = Set<AnyCancellable>()
        
        // Set mock services
        errorRecoveryManager.setServices(
            recordingCoordinator: mockRecordingCoordinator,
            keywordDetectionService: mockKeywordDetectionService,
            audioSessionManager: mockAudioSessionManager
        )
    }
    
    override func tearDown() async throws {
        cancellables.removeAll()
        errorRecoveryManager = nil
        mockRecordingCoordinator = nil
        mockKeywordDetectionService = nil
        mockAudioSessionManager = nil
    }
    
    // MARK: - Strategy Registration Tests
    
    func testRegisterRecoveryStrategy_RegistersStrategy() {
        // Given
        let strategy = RecoveryStrategy.retry(maxAttempts: 5, delay: 2.0)
        
        // When
        errorRecoveryManager.registerRecoveryStrategy(for: NSError.self, strategy: strategy)
        
        // Then
        let retrievedStrategy = errorRecoveryManager.getRecoveryStrategy(for: NSError(domain: "Test", code: 1))
        XCTAssertNotNil(retrievedStrategy)
    }
    
    func testGetRecoveryStrategy_ForRecordingError_ReturnsCorrectStrategy() {
        // Given
        let recordingError = RecordingError.permissionDenied
        
        // When
        let strategy = errorRecoveryManager.getRecoveryStrategy(for: recordingError)
        
        // Then
        XCTAssertNotNil(strategy)
        if case .retry(let maxAttempts, let delay) = strategy {
            XCTAssertEqual(maxAttempts, 3)
            XCTAssertEqual(delay, 1.0)
        } else {
            XCTFail("Expected retry strategy")
        }
    }
    
    func testCanRecover_WithKnownError_ReturnsTrue() {
        // Given
        let recordingError = RecordingError.alreadyRecording
        
        // When
        let canRecover = errorRecoveryManager.canRecover(from: recordingError)
        
        // Then
        XCTAssertTrue(canRecover)
    }
    
    func testCanRecover_WithUnknownError_ReturnsTrue() {
        // Given
        let unknownError = NSError(domain: "Unknown", code: 999)
        
        // When
        let canRecover = errorRecoveryManager.canRecover(from: unknownError)
        
        // Then
        XCTAssertTrue(canRecover) // Should use default strategy
    }
    
    // MARK: - Recovery Attempt Tests
    
    func testAttemptRecovery_WithRetryStrategy_SuccessfulRecovery() async throws {
        // Given
        let context = RecoveryContext(
            originalError: RecordingError.audioSessionConfigurationFailed,
            component: "Recording",
            operation: "startRecording"
        )
        
        // When
        let result = await errorRecoveryManager.attemptRecovery(for: context)
        
        // Then
        XCTAssertTrue(result.isSuccessful)
        XCTAssertEqual(mockRecordingCoordinator.cancelCurrentRecordingCallCount, 1)
        XCTAssertEqual(mockRecordingCoordinator.startManualRecordingCallCount, 1)
    }
    
    func testAttemptRecovery_WithRetryStrategy_FailedRecovery() async throws {
        // Given
        mockRecordingCoordinator.shouldThrowError = true
        let context = RecoveryContext(
            originalError: RecordingError.audioSessionConfigurationFailed,
            component: "Recording",
            operation: "startRecording"
        )
        
        // When
        let result = await errorRecoveryManager.attemptRecovery(for: context)
        
        // Then
        XCTAssertFalse(result.isSuccessful)
        if case .failed(let error) = result {
            XCTAssertTrue(error is ErrorRecoveryError)
        } else {
            XCTFail("Expected failed result")
        }
        XCTAssertEqual(mockRecordingCoordinator.cancelCurrentRecordingCallCount, 3) // 3 retry attempts
    }
    
    func testAttemptRecovery_WithResetStrategy_AudioSession() async throws {
        // Given
        let context = RecoveryContext(
            originalError: AudioSessionError.configurationFailed(underlying: NSError(domain: "Test", code: 1)),
            component: "AudioSession",
            operation: "configure"
        )
        
        // When
        let result = await errorRecoveryManager.attemptRecovery(for: context)
        
        // Then
        XCTAssertTrue(result.isSuccessful)
        XCTAssertEqual(mockAudioSessionManager.deactivateSessionCallCount, 1)
        XCTAssertEqual(mockAudioSessionManager.configureSessionCallCount, 1)
        XCTAssertEqual(mockAudioSessionManager.activateSessionCallCount, 1)
    }
    
    func testAttemptRecovery_WithFallbackStrategy() async throws {
        // Given
        var fallbackExecuted = false
        let fallbackStrategy = RecoveryStrategy.fallback {
            fallbackExecuted = true
        }
        
        errorRecoveryManager.registerRecoveryStrategy(for: KeywordError.self, strategy: fallbackStrategy)
        
        let context = RecoveryContext(
            originalError: KeywordError.keywordNotFound,
            component: "KeywordDetection",
            operation: "detect"
        )
        
        // When
        let result = await errorRecoveryManager.attemptRecovery(for: context)
        
        // Then
        XCTAssertTrue(result.isSuccessful)
        XCTAssertTrue(fallbackExecuted)
        if case .partialSuccess(let message) = result {
            XCTAssertTrue(message.contains("Fallback action executed"))
        } else {
            XCTFail("Expected partial success result")
        }
    }
    
    func testAttemptRecovery_WithUserInterventionStrategy() async throws {
        // Given
        let userMessage = "Please check your settings"
        let userStrategy = RecoveryStrategy.userIntervention(message: userMessage)
        
        errorRecoveryManager.registerRecoveryStrategy(for: NSError.self, strategy: userStrategy)
        
        let context = RecoveryContext(
            originalError: NSError(domain: "Test", code: 1),
            component: "Test",
            operation: "test"
        )
        
        // When
        let result = await errorRecoveryManager.attemptRecovery(for: context)
        
        // Then
        XCTAssertFalse(result.isSuccessful)
        if case .requiresUserAction(let message) = result {
            XCTAssertEqual(message, userMessage)
        } else {
            XCTFail("Expected requiresUserAction result")
        }
    }
    
    func testAttemptRecovery_WithIgnoreStrategy() async throws {
        // Given
        let ignoreStrategy = RecoveryStrategy.ignore
        
        errorRecoveryManager.registerRecoveryStrategy(for: NSError.self, strategy: ignoreStrategy)
        
        let context = RecoveryContext(
            originalError: NSError(domain: "Test", code: 1),
            component: "Test",
            operation: "test"
        )
        
        // When
        let result = await errorRecoveryManager.attemptRecovery(for: context)
        
        // Then
        XCTAssertTrue(result.isSuccessful)
        if case .success = result {
            // Expected
        } else {
            XCTFail("Expected success result")
        }
    }
    
    // MARK: - Component Recovery Tests
    
    func testRecoverKeywordDetection_SuccessfulRecovery() async throws {
        // Given
        mockKeywordDetectionService.activeKeywords = ["test1", "test2"]
        let context = RecoveryContext(
            originalError: NSError(domain: "Test", code: 1),
            component: "KeywordDetection",
            operation: "monitor"
        )
        
        // When
        let result = await errorRecoveryManager.attemptRecovery(for: context)
        
        // Then
        XCTAssertTrue(result.isSuccessful)
        XCTAssertEqual(mockKeywordDetectionService.stopMonitoringCallCount, 1)
        XCTAssertEqual(mockKeywordDetectionService.startMonitoringCallCount, 1)
    }
    
    func testRecoverAudioSession_FailedRecovery() async throws {
        // Given
        mockAudioSessionManager.shouldThrowError = true
        let context = RecoveryContext(
            originalError: AudioSessionError.configurationFailed(underlying: NSError(domain: "Test", code: 1)),
            component: "AudioSession",
            operation: "configure"
        )
        
        // When
        let result = await errorRecoveryManager.attemptRecovery(for: context)
        
        // Then
        XCTAssertFalse(result.isSuccessful)
        XCTAssertEqual(mockAudioSessionManager.deactivateSessionCallCount, 1)
        // Configuration should fail, so activate shouldn't be called
        XCTAssertEqual(mockAudioSessionManager.activateSessionCallCount, 0)
    }
    
    // MARK: - Recovery Strategy Tests
    
    func testRecoveryStrategy_Description() {
        let retryStrategy = RecoveryStrategy.retry(maxAttempts: 3, delay: 1.0)
        XCTAssertTrue(retryStrategy.description.contains("Retry up to 3 times"))
        
        let fallbackStrategy = RecoveryStrategy.fallback { }
        XCTAssertEqual(fallbackStrategy.description, "Execute fallback action")
        
        let resetStrategy = RecoveryStrategy.reset(component: "AudioSession")
        XCTAssertEqual(resetStrategy.description, "Reset AudioSession component")
        
        let userStrategy = RecoveryStrategy.userIntervention(message: "Test message")
        XCTAssertTrue(userStrategy.description.contains("User intervention required"))
        
        let ignoreStrategy = RecoveryStrategy.ignore
        XCTAssertEqual(ignoreStrategy.description, "Ignore error")
    }
    
    // MARK: - Recovery Result Tests
    
    func testRecoveryResult_IsSuccessful() {
        XCTAssertTrue(RecoveryResult.success.isSuccessful)
        XCTAssertTrue(RecoveryResult.partialSuccess(message: "Test").isSuccessful)
        XCTAssertFalse(RecoveryResult.failed(error: NSError(domain: "Test", code: 1)).isSuccessful)
        XCTAssertFalse(RecoveryResult.requiresUserAction(message: "Test").isSuccessful)
    }
    
    // MARK: - Recovery Context Tests
    
    func testRecoveryContext_Initialization() {
        // Given
        let error = NSError(domain: "Test", code: 1)
        let component = "TestComponent"
        let operation = "testOperation"
        let userContext = ["key": "value"]
        
        // When
        let context = RecoveryContext(
            originalError: error,
            component: component,
            operation: operation,
            attemptCount: 2,
            userContext: userContext
        )
        
        // Then
        XCTAssertEqual((context.originalError as NSError).domain, error.domain)
        XCTAssertEqual(context.component, component)
        XCTAssertEqual(context.operation, operation)
        XCTAssertEqual(context.attemptCount, 2)
        XCTAssertEqual(context.userContext["key"] as? String, "value")
        XCTAssertNotNil(context.timestamp)
    }
    
    // MARK: - Error Recovery Error Tests
    
    func testErrorRecoveryError_Descriptions() {
        let errors: [ErrorRecoveryError] = [
            .noStrategyFound,
            .retryLimitExceeded,
            .partialRecovery("Test message"),
            .userActionRequired("Test action"),
            .unknownComponent("TestComponent"),
            .resetRequested,
            .resetFailed("TestComponent")
        ]
        
        for error in errors {
            XCTAssertNotNil(error.errorDescription)
            XCTAssertFalse(error.errorDescription!.isEmpty)
        }
    }
    
    // MARK: - Performance Tests
    
    func testPerformance_MultipleRecoveryAttempts() {
        measure {
            Task {
                for i in 0..<50 {
                    let context = RecoveryContext(
                        originalError: RecordingError.audioSessionConfigurationFailed,
                        component: "Recording",
                        operation: "test\(i)"
                    )
                    _ = await errorRecoveryManager.attemptRecovery(for: context)
                }
            }
        }
    }
}
