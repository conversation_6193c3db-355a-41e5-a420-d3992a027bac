//
//  MigrationValidationTests.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest
import Combine
@testable import EchoNote

// MARK: - Migration Validation Tests

@MainActor
final class MigrationValidationTests: XCTestCase {
    
    var legacyCleanupManager: LegacyCodeCleanupManager!
    var legacyCompatibilityLayer: LegacyCompatibilityLayer!
    var serviceManager: ServiceManager!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() async throws {
        legacyCleanupManager = LegacyCodeCleanupManager.shared
        legacyCompatibilityLayer = LegacyCompatibilityLayer.shared
        serviceManager = ServiceManager.shared
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() async throws {
        cancellables.removeAll()
        legacyCleanupManager = nil
        legacyCompatibilityLayer = nil
        serviceManager = nil
    }
    
    // MARK: - Feature Flags Tests
    
    func testFeatureFlags_NewArchitectureEnabled() {
        XCTAssertTrue(FeatureFlags.useNewRecordingArchitecture)
        XCTAssertTrue(FeatureFlags.useNewBackgroundMonitoring)
        XCTAssertTrue(FeatureFlags.useNewSiriIntegration)
        XCTAssertTrue(FeatureFlags.useNewEventSystem)
        XCTAssertTrue(FeatureFlags.useNewErrorHandling)
        XCTAssertTrue(FeatureFlags.useNewMemoryOptimization)
    }
    
    func testFeatureFlags_LegacyCompatibilityDisabled() {
        XCTAssertFalse(FeatureFlags.enableLegacyCompatibility)
        XCTAssertTrue(FeatureFlags.strictMigrationMode)
    }
    
    // MARK: - Migration Status Tests
    
    func testLegacyComponents_AllMigrationsCompleted() {
        for component in LegacyComponent.allCases {
            XCTAssertEqual(
                component.migrationStatus,
                .completed,
                "Component \(component.rawValue) migration should be completed"
            )
        }
    }
    
    func testLegacyComponents_ReplacementMapping() {
        let expectedMappings: [LegacyComponent: String] = [
            .audioManager: "VoiceRecordingService + AudioSessionManager",
            .backgroundMonitorService: "KeywordDetectionService",
            .siriShortcutsService: "SiriIntegrationManager",
            .notificationCenterEvents: "EventBus (Combine)",
            .legacyUserPreferences: "PreferencesManager",
            .legacyKeywordManager: "KeywordManager"
        ]
        
        for (component, expectedReplacement) in expectedMappings {
            XCTAssertEqual(
                component.replacementComponent,
                expectedReplacement,
                "Component \(component.rawValue) should map to \(expectedReplacement)"
            )
        }
    }
    
    // MARK: - Service Manager Integration Tests
    
    func testServiceManager_CanCreateAllNewServices() async {
        // Test that all new services can be created
        let recordingCoordinator = await serviceManager.getRecordingCoordinator()
        XCTAssertNotNil(recordingCoordinator)
        
        let keywordDetectionService = await serviceManager.getKeywordDetectionService()
        XCTAssertNotNil(keywordDetectionService)
        
        let permissionsService = await serviceManager.getPermissionsService()
        XCTAssertNotNil(permissionsService)
        
        let dataManager = await serviceManager.getDataManager()
        XCTAssertNotNil(dataManager)
    }
    
    func testServiceManager_ServicesAreProperlyInitialized() async {
        let recordingCoordinator = await serviceManager.getRecordingCoordinator()
        
        // Test that the coordinator has proper state
        let initialState = recordingCoordinator.recordingState.value
        if case .idle = initialState {
            // Expected initial state
        } else {
            XCTFail("Recording coordinator should start in idle state")
        }
    }
    
    // MARK: - New Architecture Functionality Tests
    
    func testNewRecordingArchitecture_BasicFunctionality() async throws {
        let recordingCoordinator = await serviceManager.getRecordingCoordinator()
        
        // Test that we can start and stop recording
        try await recordingCoordinator.startManualRecording()
        
        let currentState = recordingCoordinator.recordingState.value
        if case .recording = currentState {
            // Expected recording state
        } else {
            XCTFail("Should be in recording state after starting recording")
        }
        
        // Cancel recording for cleanup
        await recordingCoordinator.cancelCurrentRecording()
    }
    
    func testNewEventSystem_EventPublishing() async throws {
        let eventBus = EventBus.shared
        var receivedEvents: [AppEvent] = []
        
        eventBus.allEvents
            .sink { event in
                receivedEvents.append(event)
            }
            .store(in: &cancellables)
        
        // Publish a test event
        let session = RecordingSession(
            id: UUID(),
            startTime: Date(),
            audioURL: URL(fileURLWithPath: "/test.m4a"),
            mode: .manual
        )
        eventBus.publishRecordingStarted(session: session)
        
        // Wait for event to be processed
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        XCTAssertEqual(receivedEvents.count, 1)
        XCTAssertTrue(receivedEvents.first is RecordingStartedEvent)
    }
    
    func testNewErrorHandling_ErrorRecovery() async {
        let errorRecoveryManager = ErrorRecoveryManager.shared
        
        let context = RecoveryContext(
            originalError: RecordingError.audioSessionConfigurationFailed,
            component: "Recording",
            operation: "test"
        )
        
        let result = await errorRecoveryManager.attemptRecovery(for: context)
        
        // Should be able to attempt recovery (even if it fails)
        XCTAssertNotNil(result)
    }
    
    func testNewMemoryOptimization_MemoryMonitoring() async throws {
        let memoryManager = MemoryOptimizationManager.shared
        
        memoryManager.startMemoryMonitoring()
        
        // Wait for initial measurement
        try await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        XCTAssertNotNil(memoryManager.currentMemoryUsage)
        XCTAssertGreaterThan(memoryManager.currentMemoryUsage?.appMemoryUsage ?? 0, 0)
        
        memoryManager.stopMemoryMonitoring()
    }
    
    // MARK: - Legacy Compatibility Tests
    
    func testLegacyCompatibility_DisabledByDefault() {
        XCTAssertFalse(FeatureFlags.enableLegacyCompatibility)
    }
    
    func testLegacyCompatibility_FailsWhenDisabled() async {
        // Test that legacy methods fail when compatibility is disabled
        let expectation = expectation(description: "Legacy method should fail")
        
        legacyCompatibilityLayer.startRecording { success, error in
            XCTAssertFalse(success)
            XCTAssertNotNil(error)
            expectation.fulfill()
        }
        
        await fulfillment(of: [expectation], timeout: 1.0)
    }
    
    func testLegacyCompatibility_WorksWhenEnabled() async {
        // Temporarily enable legacy compatibility
        let originalFlag = FeatureFlags.enableLegacyCompatibility
        FeatureFlags.enableLegacyCompatibility = true
        
        defer {
            FeatureFlags.enableLegacyCompatibility = originalFlag
        }
        
        let expectation = expectation(description: "Legacy method should work")
        
        legacyCompatibilityLayer.startRecording { success, error in
            // Should succeed when compatibility is enabled
            XCTAssertTrue(success)
            XCTAssertNil(error)
            expectation.fulfill()
        }
        
        await fulfillment(of: [expectation], timeout: 2.0)
        
        // Clean up by stopping recording
        let stopExpectation = expectation(description: "Stop recording")
        legacyCompatibilityLayer.stopRecording { _, _ in
            stopExpectation.fulfill()
        }
        await fulfillment(of: [stopExpectation], timeout: 2.0)
    }
    
    // MARK: - Cleanup Manager Tests
    
    func testCleanupManager_MigrationValidation() throws {
        // Test that migration validation passes
        XCTAssertNoThrow(try legacyCleanupManager.validateMigrationIntegrity())
    }
    
    func testCleanupManager_GeneratesReport() {
        let report = legacyCleanupManager.generateCleanupReport()
        
        XCTAssertTrue(report.contains("Legacy Code Cleanup Report"))
        XCTAssertTrue(report.contains("Migration Status:"))
        XCTAssertTrue(report.contains("Feature Flags:"))
        
        // Check that all components are mentioned
        for component in LegacyComponent.allCases {
            XCTAssertTrue(report.contains(component.rawValue))
        }
    }
    
    func testCleanupManager_PerformCleanup() async throws {
        // Test that cleanup can be performed without errors
        XCTAssertNoThrow(try await legacyCleanupManager.performCleanup())
    }
    
    // MARK: - Integration Tests
    
    func testFullMigration_RecordingWorkflow() async throws {
        // Test that the full recording workflow works with new architecture
        let recordingCoordinator = await serviceManager.getRecordingCoordinator()
        
        // Start recording
        try await recordingCoordinator.startManualRecording()
        
        // Verify recording state
        let recordingState = recordingCoordinator.recordingState.value
        if case .recording = recordingState {
            // Expected
        } else {
            XCTFail("Should be in recording state")
        }
        
        // Stop recording
        let note = try await recordingCoordinator.stopManualRecording()
        
        // Verify note creation
        XCTAssertNotNil(note)
        XCTAssertFalse(note?.title.isEmpty ?? true)
    }
    
    func testFullMigration_BackgroundMonitoring() async throws {
        // Test that background monitoring works with new architecture
        let recordingCoordinator = await serviceManager.getRecordingCoordinator()
        
        let keywords = ["test", "migration"]
        
        // Enable background monitoring
        try await recordingCoordinator.enableBackgroundMonitoring(keywords: keywords)
        
        // Simulate keyword detection
        try await recordingCoordinator.handleKeywordDetection(keyword: "test")
        
        // Disable background monitoring
        await recordingCoordinator.disableBackgroundMonitoring()
        
        // Should complete without errors
        XCTAssertTrue(true)
    }
    
    func testFullMigration_SiriIntegration() async throws {
        // Test that Siri integration works with new architecture
        let siriManager = SiriIntegrationManager.shared
        
        // Test shortcut handling
        try await siriManager.handleShortcut(.startRecording)
        
        // Should complete without errors
        XCTAssertTrue(true)
    }
    
    // MARK: - Performance Validation Tests
    
    func testMigration_PerformanceRegression() {
        // Test that new architecture doesn't have significant performance regression
        measure {
            Task {
                let recordingCoordinator = await serviceManager.getRecordingCoordinator()
                try? await recordingCoordinator.startManualRecording()
                await recordingCoordinator.cancelCurrentRecording()
            }
        }
    }
    
    func testMigration_MemoryUsage() {
        // Test that new architecture doesn't use significantly more memory
        measure(metrics: [XCTMemoryMetric()]) {
            Task {
                let _ = await serviceManager.getRecordingCoordinator()
                let _ = await serviceManager.getKeywordDetectionService()
                let _ = await serviceManager.getPermissionsService()
                let _ = await serviceManager.getDataManager()
            }
        }
    }
    
    // MARK: - Regression Tests
    
    func testMigration_NoFunctionalityLoss() async throws {
        // Test that all original functionality is preserved
        
        // Recording functionality
        let recordingCoordinator = await serviceManager.getRecordingCoordinator()
        try await recordingCoordinator.startManualRecording()
        let note = try await recordingCoordinator.stopManualRecording()
        XCTAssertNotNil(note)
        
        // Background monitoring functionality
        try await recordingCoordinator.enableBackgroundMonitoring(keywords: ["test"])
        await recordingCoordinator.disableBackgroundMonitoring()
        
        // Siri integration functionality
        let siriManager = SiriIntegrationManager.shared
        try await siriManager.handleShortcut(.startRecording)
        
        // Error handling functionality
        let errorManager = ErrorRecoveryManager.shared
        let context = RecoveryContext(
            originalError: RecordingError.notRecording,
            component: "Test",
            operation: "test"
        )
        let _ = await errorManager.attemptRecovery(for: context)
        
        // Memory optimization functionality
        let memoryManager = MemoryOptimizationManager.shared
        await memoryManager.optimizeMemoryUsage()
        
        // All functionality should work without errors
        XCTAssertTrue(true)
    }
}
