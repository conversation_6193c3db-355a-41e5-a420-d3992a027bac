//
//  DataManagerTests.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest
import SwiftData
@testable import EchoNote

@MainActor
final class DataManagerTests: XCTestCase {
    
    var dataManager: DataManager!
    var modelContainer: ModelContainer!
    var modelContext: ModelContext!
    
    override func setUp() async throws {
        // Create in-memory model container for testing
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        modelContainer = try ModelContainer(for: Note.self, Keyword.self, configurations: config)
        modelContext = ModelContext(modelContainer)
        dataManager = DataManager(modelContext: modelContext)
    }
    
    override func tearDown() async throws {
        dataManager = nil
        modelContext = nil
        modelContainer = nil
    }
    
    // MARK: - Note Operations Tests
    
    func testSaveNote() async throws {
        // Given
        let note = Note(
            title: "Test Note",
            content: "Test content",
            audioURL: nil,
            duration: 0,
            createdAt: Date(),
            modifiedAt: Date(),
            isFavorite: false,
            keywords: []
        )
        
        // When
        try await dataManager.saveNote(note)
        
        // Then
        let fetchedNotes = try await dataManager.fetchNotes()
        XCTAssertEqual(fetchedNotes.count, 1)
        XCTAssertEqual(fetchedNotes.first?.title, "Test Note")
        XCTAssertEqual(fetchedNotes.first?.content, "Test content")
    }
    
    func testFetchNotes() async throws {
        // Given
        let note1 = Note(
            title: "Note 1",
            content: "Content 1",
            audioURL: nil,
            duration: 0,
            createdAt: Date().addingTimeInterval(-100),
            modifiedAt: Date(),
            isFavorite: false,
            keywords: []
        )
        
        let note2 = Note(
            title: "Note 2",
            content: "Content 2",
            audioURL: nil,
            duration: 0,
            createdAt: Date(),
            modifiedAt: Date(),
            isFavorite: false,
            keywords: []
        )
        
        try await dataManager.saveNote(note1)
        try await dataManager.saveNote(note2)
        
        // When
        let fetchedNotes = try await dataManager.fetchNotes()
        
        // Then
        XCTAssertEqual(fetchedNotes.count, 2)
        // Should be sorted by creation date (newest first)
        XCTAssertEqual(fetchedNotes.first?.title, "Note 2")
        XCTAssertEqual(fetchedNotes.last?.title, "Note 1")
    }
    
    func testFetchNoteById() async throws {
        // Given
        let note = Note(
            title: "Test Note",
            content: "Test content",
            audioURL: nil,
            duration: 0,
            createdAt: Date(),
            modifiedAt: Date(),
            isFavorite: false,
            keywords: []
        )
        
        try await dataManager.saveNote(note)
        
        // When
        let fetchedNote = try await dataManager.fetchNote(withId: note.id)
        
        // Then
        XCTAssertNotNil(fetchedNote)
        XCTAssertEqual(fetchedNote?.id, note.id)
        XCTAssertEqual(fetchedNote?.title, "Test Note")
    }
    
    func testFetchNoteByIdNotFound() async throws {
        // Given
        let nonExistentId = UUID()
        
        // When
        let fetchedNote = try await dataManager.fetchNote(withId: nonExistentId)
        
        // Then
        XCTAssertNil(fetchedNote)
    }
    
    func testUpdateNote() async throws {
        // Given
        let note = Note(
            title: "Original Title",
            content: "Original content",
            audioURL: nil,
            duration: 0,
            createdAt: Date(),
            modifiedAt: Date(),
            isFavorite: false,
            keywords: []
        )
        
        try await dataManager.saveNote(note)
        let originalModifiedAt = note.modifiedAt
        
        // Wait a bit to ensure timestamp difference
        try await Task.sleep(nanoseconds: 1_000_000) // 1ms
        
        // When
        note.title = "Updated Title"
        try await dataManager.updateNote(note)
        
        // Then
        let fetchedNote = try await dataManager.fetchNote(withId: note.id)
        XCTAssertEqual(fetchedNote?.title, "Updated Title")
        XCTAssertGreaterThan(fetchedNote?.modifiedAt ?? Date.distantPast, originalModifiedAt)
    }
    
    func testDeleteNote() async throws {
        // Given
        let note = Note(
            title: "Test Note",
            content: "Test content",
            audioURL: nil,
            duration: 0,
            createdAt: Date(),
            modifiedAt: Date(),
            isFavorite: false,
            keywords: []
        )
        
        try await dataManager.saveNote(note)
        
        // Verify note exists
        var fetchedNotes = try await dataManager.fetchNotes()
        XCTAssertEqual(fetchedNotes.count, 1)
        
        // When
        try await dataManager.deleteNote(note)
        
        // Then
        fetchedNotes = try await dataManager.fetchNotes()
        XCTAssertEqual(fetchedNotes.count, 0)
    }
    
    // MARK: - Keyword Operations Tests
    
    func testFetchKeywords() async throws {
        // Given
        let keyword1 = Keyword(text: "idea")
        let keyword2 = Keyword(text: "task")
        
        try await dataManager.saveKeywords([keyword1, keyword2])
        
        // When
        let fetchedKeywords = try await dataManager.fetchKeywords()
        
        // Then
        XCTAssertEqual(fetchedKeywords.count, 2)
        // Should be sorted alphabetically
        XCTAssertEqual(fetchedKeywords.first?.text, "idea")
        XCTAssertEqual(fetchedKeywords.last?.text, "task")
    }
    
    func testSaveKeywords() async throws {
        // Given
        let keywords = [
            Keyword(text: "meeting"),
            Keyword(text: "project"),
            Keyword(text: "reminder")
        ]
        
        // When
        try await dataManager.saveKeywords(keywords)
        
        // Then
        let fetchedKeywords = try await dataManager.fetchKeywords()
        XCTAssertEqual(fetchedKeywords.count, 3)
        
        let keywordTexts = fetchedKeywords.map { $0.text }.sorted()
        XCTAssertEqual(keywordTexts, ["meeting", "project", "reminder"])
    }
    
    // MARK: - Generic Operations Tests
    
    func testGenericSave() async throws {
        // Given
        let note = Note(
            title: "Generic Test",
            content: "Generic content",
            audioURL: nil,
            duration: 0,
            createdAt: Date(),
            modifiedAt: Date(),
            isFavorite: false,
            keywords: []
        )
        
        // When
        await dataManager.insert(note)
        try await dataManager.save()
        
        // Then
        let fetchedNotes = try await dataManager.fetch(Note.self)
        XCTAssertEqual(fetchedNotes.count, 1)
        XCTAssertEqual(fetchedNotes.first?.title, "Generic Test")
    }
    
    func testGenericDelete() async throws {
        // Given
        let note = Note(
            title: "Delete Test",
            content: "Delete content",
            audioURL: nil,
            duration: 0,
            createdAt: Date(),
            modifiedAt: Date(),
            isFavorite: false,
            keywords: []
        )
        
        await dataManager.insert(note)
        try await dataManager.save()
        
        // Verify note exists
        var fetchedNotes = try await dataManager.fetch(Note.self)
        XCTAssertEqual(fetchedNotes.count, 1)
        
        // When
        try await dataManager.delete(note)
        
        // Then
        fetchedNotes = try await dataManager.fetch(Note.self)
        XCTAssertEqual(fetchedNotes.count, 0)
    }
    
    func testGenericFetch() async throws {
        // Given
        let keyword1 = Keyword(text: "fetch1")
        let keyword2 = Keyword(text: "fetch2")
        
        await dataManager.insert(keyword1)
        await dataManager.insert(keyword2)
        try await dataManager.save()
        
        // When
        let fetchedKeywords = try await dataManager.fetch(Keyword.self)
        
        // Then
        XCTAssertEqual(fetchedKeywords.count, 2)
        let texts = fetchedKeywords.map { $0.text }.sorted()
        XCTAssertEqual(texts, ["fetch1", "fetch2"])
    }
    
    // MARK: - Performance Tests
    
    func testBatchOperationPerformance() {
        measure {
            Task {
                let notes = (1...100).map { i in
                    Note(
                        title: "Note \(i)",
                        content: "Content \(i)",
                        audioURL: nil,
                        duration: 0,
                        createdAt: Date(),
                        modifiedAt: Date(),
                        isFavorite: false,
                        keywords: []
                    )
                }
                
                for note in notes {
                    await dataManager.insert(note)
                }
                
                try? await dataManager.save()
            }
        }
    }
    
    // MARK: - Error Handling Tests
    
    func testSaveWithInvalidData() async {
        // This test would require creating invalid data scenarios
        // For now, we'll test that the async operations complete without crashing
        
        let note = Note(
            title: "",
            content: "",
            audioURL: nil,
            duration: 0,
            createdAt: Date(),
            modifiedAt: Date(),
            isFavorite: false,
            keywords: []
        )
        
        do {
            try await dataManager.saveNote(note)
            // Should succeed even with empty strings
            let fetchedNotes = try await dataManager.fetchNotes()
            XCTAssertEqual(fetchedNotes.count, 1)
        } catch {
            XCTFail("Save should not fail with empty strings: \(error)")
        }
    }
    
    // MARK: - Concurrency Tests
    
    func testConcurrentOperations() async {
        let expectation = XCTestExpectation(description: "Concurrent operations complete")
        expectation.expectedFulfillmentCount = 10
        
        await withTaskGroup(of: Void.self) { group in
            for i in 0..<10 {
                group.addTask {
                    let note = Note(
                        title: "Concurrent Note \(i)",
                        content: "Content \(i)",
                        audioURL: nil,
                        duration: 0,
                        createdAt: Date(),
                        modifiedAt: Date(),
                        isFavorite: false,
                        keywords: []
                    )
                    
                    try? await self.dataManager.saveNote(note)
                    expectation.fulfill()
                }
            }
        }
        
        await fulfillment(of: [expectation], timeout: 5.0)
        
        // Verify all notes were saved
        let fetchedNotes = try? await dataManager.fetchNotes()
        XCTAssertEqual(fetchedNotes?.count, 10)
    }
}
