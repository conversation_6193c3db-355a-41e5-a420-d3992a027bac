//
//  ServiceContainerTests.swift
//  EchoNoteTests
//
//  Created by <PERSON> on 29/6/2025.
//

import XCTest
@testable import EchoNote

// MARK: - Test Protocols and Mocks

protocol TestServiceProtocol {
    var value: String { get }
}

class MockTestService: TestServiceProtocol {
    let value: String
    
    init(value: String) {
        self.value = value
    }
}

protocol TestFactoryServiceProtocol {
    var id: UUID { get }
}

class MockFactoryService: TestFactoryServiceProtocol {
    let id = UUID()
}

// MARK: - ServiceContainer Tests

final class ServiceContainerTests: XCTestCase {
    
    var container: ServiceContainer!
    
    override func setUp() async throws {
        container = ServiceContainer()
    }
    
    override func tearDown() async throws {
        await container.reset()
        container = nil
    }
    
    // MARK: - Service Registration Tests
    
    func testRegisterSingletonService() async throws {
        // Given
        let mockService = MockTestService(value: "test")
        
        // When
        await container.register(TestServiceProtocol.self, service: mockService)
        
        // Then
        let resolvedService = await container.resolve(TestServiceProtocol.self)
        XCTAssertNotNil(resolvedService)
        XCTAssertEqual(resolvedService?.value, "test")
    }
    
    func testRegisterFactoryService() async throws {
        // Given
        let factory: () -> TestFactoryServiceProtocol = {
            return MockFactoryService()
        }
        
        // When
        await container.register(TestFactoryServiceProtocol.self, factory: factory)
        
        // Then
        let service1 = await container.resolve(TestFactoryServiceProtocol.self)
        let service2 = await container.resolve(TestFactoryServiceProtocol.self)
        
        XCTAssertNotNil(service1)
        XCTAssertNotNil(service2)
        XCTAssertNotEqual(service1?.id, service2?.id) // Different instances
    }
    
    func testRegisterSingletonFactory() async throws {
        // Given
        let factory: () -> TestFactoryServiceProtocol = {
            return MockFactoryService()
        }
        
        // When
        await container.registerSingleton(TestFactoryServiceProtocol.self, factory: factory)
        
        // Then
        let service1 = await container.resolve(TestFactoryServiceProtocol.self)
        let service2 = await container.resolve(TestFactoryServiceProtocol.self)
        
        XCTAssertNotNil(service1)
        XCTAssertNotNil(service2)
        XCTAssertEqual(service1?.id, service2?.id) // Same instance
    }
    
    // MARK: - Service Resolution Tests
    
    func testResolveUnregisteredService() async throws {
        // When
        let service = await container.resolve(TestServiceProtocol.self)
        
        // Then
        XCTAssertNil(service)
    }
    
    func testResolveRegisteredService() async throws {
        // Given
        let mockService = MockTestService(value: "registered")
        await container.register(TestServiceProtocol.self, service: mockService)
        
        // When
        let resolvedService = await container.resolve(TestServiceProtocol.self)
        
        // Then
        XCTAssertNotNil(resolvedService)
        XCTAssertEqual(resolvedService?.value, "registered")
    }
    
    func testOverwriteExistingService() async throws {
        // Given
        let firstService = MockTestService(value: "first")
        let secondService = MockTestService(value: "second")
        
        // When
        await container.register(TestServiceProtocol.self, service: firstService)
        await container.register(TestServiceProtocol.self, service: secondService)
        
        // Then
        let resolvedService = await container.resolve(TestServiceProtocol.self)
        XCTAssertEqual(resolvedService?.value, "second")
    }
    
    // MARK: - Container Management Tests
    
    func testIsRegistered() async throws {
        // Given
        let mockService = MockTestService(value: "test")
        
        // When
        let beforeRegistration = await container.isRegistered(TestServiceProtocol.self)
        await container.register(TestServiceProtocol.self, service: mockService)
        let afterRegistration = await container.isRegistered(TestServiceProtocol.self)
        
        // Then
        XCTAssertFalse(beforeRegistration)
        XCTAssertTrue(afterRegistration)
    }
    
    func testGetRegisteredServices() async throws {
        // Given
        let mockService1 = MockTestService(value: "test1")
        let mockService2 = MockFactoryService()
        
        // When
        await container.register(TestServiceProtocol.self, service: mockService1)
        await container.register(TestFactoryServiceProtocol.self, service: mockService2)
        
        let registeredServices = await container.getRegisteredServices()
        
        // Then
        XCTAssertEqual(registeredServices.count, 2)
        XCTAssertTrue(registeredServices.contains("TestServiceProtocol"))
        XCTAssertTrue(registeredServices.contains("TestFactoryServiceProtocol"))
    }
    
    func testResetContainer() async throws {
        // Given
        let mockService = MockTestService(value: "test")
        await container.register(TestServiceProtocol.self, service: mockService)
        
        // When
        await container.reset()
        
        // Then
        let resolvedService = await container.resolve(TestServiceProtocol.self)
        let registeredServices = await container.getRegisteredServices()
        
        XCTAssertNil(resolvedService)
        XCTAssertTrue(registeredServices.isEmpty)
    }
    
    // MARK: - Thread Safety Tests
    
    func testConcurrentRegistrationAndResolution() async throws {
        // Given
        let expectation = XCTestExpectation(description: "Concurrent operations complete")
        expectation.expectedFulfillmentCount = 10
        
        // When
        await withTaskGroup(of: Void.self) { group in
            for i in 0..<10 {
                group.addTask {
                    let service = MockTestService(value: "service\(i)")
                    await self.container.register(TestServiceProtocol.self, service: service)
                    
                    let resolved = await self.container.resolve(TestServiceProtocol.self)
                    XCTAssertNotNil(resolved)
                    
                    expectation.fulfill()
                }
            }
        }
        
        // Then
        await fulfillment(of: [expectation], timeout: 5.0)
        
        let finalService = await container.resolve(TestServiceProtocol.self)
        XCTAssertNotNil(finalService)
    }
    
    // MARK: - Performance Tests
    
    func testServiceResolutionPerformance() async throws {
        // Given
        let mockService = MockTestService(value: "performance")
        await container.register(TestServiceProtocol.self, service: mockService)
        
        // When/Then
        measure {
            Task {
                for _ in 0..<1000 {
                    let _ = await container.resolve(TestServiceProtocol.self)
                }
            }
        }
    }
}
